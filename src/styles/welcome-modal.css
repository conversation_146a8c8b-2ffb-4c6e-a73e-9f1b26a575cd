@font-face {
  font-family: 'Nothing';
  src: url('../assets/fonts/nothing-font-5x7.otf') format('opentype'),
       url('../assets/fonts/nothing-font-5x7.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

.font-pixel {
  font-family: 'Nothing', monospace;
  letter-spacing: 0.5px;
}

/* Animation for modal entrance */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.welcome-modal-content {
  animation: modalFadeIn 0.3s ease-out;
} 

.get-started-button {
  transform: translateY(0);
  
  box-shadow: 
    0px 19px 41px 0px hsla(0, 0%, 100%, 0.1),
    0px 75px 75px 0px hsla(0, 0%, 100%, 0.1),
    0px 169px 101px 0px hsla(0, 0%, 100%, 0.1),
    0px 300px 120px 0px hsla(0, 0%, 100%, 0.03),
    0px 469px 131px 0px hsla(0, 0%, 100%, 0);
}

.get-started-button:hover {
  transform: translateY(-8px);
  transition: all 800ms cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 
    0px 19px 41px 0px hsla(0, 0%, 100%, 0.1),
    0px 75px 75px 0px hsla(0, 0%, 100%, 0.15),
    0px 169px 101px 0px hsla(0, 0%, 100%, 0.1),
    0px 300px 120px 0px hsla(0, 0%, 100%, 0.03),
    0px 469px 131px 0px hsla(0, 0%, 100%, 0);
}

.get-started-button:not(:hover) {
  
  transition: all 800ms cubic-bezier(0.34, 1.56, 0.64, 1);
 
}


.interested-button {  
  box-shadow: 
    0px 19px 41px 0px hsla(0, 0%, 100%, 0.1),
    0px 75px 75px 0px hsla(0, 0%, 100%, 0.1),
    0px 169px 101px 0px hsla(0, 0%, 100%, 0.1),
    0px 300px 120px 0px hsla(0, 0%, 100%, 0.03),
    0px 469px 131px 0px hsla(0, 0%, 100%, 0);
}

.interested-button:hover {
  transition: all 800ms cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 
    0px 19px 41px 0px hsla(0, 0%, 100%, 0.1),
    0px 75px 75px 0px hsla(0, 0%, 100%, 0.15),
    0px 169px 101px 0px hsla(0, 0%, 100%, 0.1),
    0px 300px 120px 0px hsla(0, 0%, 100%, 0.03),
    0px 469px 131px 0px hsla(0, 0%, 100%, 0);
}

.interested-button:not(:hover) {
  
  transition: all 800ms cubic-bezier(0.34, 1.56, 0.64, 1);
 
}