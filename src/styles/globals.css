@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
    * {
        font-family: "Brockmann", sans-serif;
    }

    html {
        overflow-y: hidden;
        /* Mobile viewport height fix */
        height: 100vh;
        height: 100dvh; /* Dynamic viewport height for modern browsers */
    }

    body {
        padding-right: 0 !important; /* Prevent layout shift when dialog opens */
        /* Mobile viewport height fix */
        height: 100vh;
        height: 100dvh; /* Dynamic viewport height for modern browsers */
    }

    :root {
        --background: 240 3% 6%;
        --foreground: 222.2 84% 4.9%;

        --card: 240 3% 6%;
        --card-foreground: 222.2 84% 4.9%;

        --card-blue-background: 194 100% 35%;
        --card-blue-gray-background: 194 47% 81%;

        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;

        --primary: 240 14% 87%;  /* #DDDDE6 */
        --primary-foreground: 240 3% 6%;

        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;

        --yellow-primary: 40 44% 64%;
        --yellow-primary-text: 40 5% 38%;

        --loader-color: #1BB4CC;

        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;

        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;

        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 222.2 84% 4.9%;

        --radius: 0.5rem;

        /* Mobile viewport height fix */
        --vh: 1vh;
        --dvh: 1dvh;
    }

    .dark {
        --background: 240 3% 6%;
        --foreground: 210 40% 98%;

        --card: 240 3% 6%;
        --card-foreground: 210 40% 98%;

        --card-blue-background: 194 100% 35%;
        --card-blue-gray-background: 194 47% 81%;

        --yellow-primary: 40 44% 64%;
        --yellow-primary-text: 40 5% 38%;

        --loader-color: #1BB4CC;

        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;

        --primary: 240 14% 87%;  /* #DDDDE6 */
        --primary-foreground: 240 3% 6%;

        --secondary: 240 2% 12%;
        --secondary-foreground: 210 40% 98%;

        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 220 5% 38%;

        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;

        --border: 0 0% 35%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply antialiased bg-background text-foreground;
        font-feature-settings:
            "rlig" 1,
            "calt" 1;
    }

    /* Targeted focus outline removal for dialog-related elements */
    [role="dialog"]:focus,
    [role="dialog"] *:focus {
        outline: none !important;
        box-shadow: none !important;
    }

    [role="dialog"]:focus-visible,
    [role="dialog"] *:focus-visible {
        outline: none !important;
        box-shadow: none !important;
    }

    /* Remove focus outlines from dialog elements */
    [data-radix-dialog-content] {
        @apply focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none;
        outline: none !important;
        box-shadow: none !important;
    }

    [data-radix-dialog-content] * {
        @apply focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none;
        outline: none !important;
        box-shadow: none !important;
    }

    /* Global text selection styles */
    ::selection {
        background-color: rgba(102, 234, 255, 0.1);
        color: #66EAFF;
    }

    ::-moz-selection {
        background-color: rgba(102, 234, 255, 0.1);
        color: #66EAFF;
    }

    /* Fix autofill background color for agent name input */
    input#agent-name {
        background-color: rgba(255, 255, 255, 0.02) !important;
    }

    input#agent-name:-webkit-autofill,
    input#agent-name:-webkit-autofill:hover,
    input#agent-name:-webkit-autofill:focus,
    input#agent-name:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.02) inset !important;
        -webkit-text-fill-color: white !important;
        background-color: rgba(255, 255, 255, 0.02) !important;
        transition: background-color 5000s ease-in-out 0s;
    }
}

.font-sans {
    font-family: var(--font-sans);
}

@keyframes sweep {
    to {
        background-position: -200% center;
    }
}

@layer components {
    .diff-button {
        color: #0F0F10;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
    }
}


@keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

.hide-scrollbar{
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

  .shimmer-effect {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 2s infinite;
  }

  .hide-scrollbars{
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

.gradient-overlay-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-blend-mode: overlay;
  background: linear-gradient(180deg, rgba(255,255,255,0.7), rgba(0,0,0,0.7));
  border-radius: inherit;
}




.radial-bg{
    background: radial-gradient(50% 50% at 50% 50%, rgba(128, 255, 249, 0.15) 0%, rgba(128, 255, 249, 0.075) 100%);
    backdrop-filter: blur(100px);
  }

.radial-bg-white{
    background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.075) 100%);
    
  }

.tooltip-shadow {
    box-shadow:
        0px 9px 19px 0px #FFFFFF1A,
        0px 35px 35px 0px #FFFFFF17,
        0px 79px 48px 0px #FFFFFF0D,
        0px 141px 56px 0px #FFFFFF05,
        0px 221px 62px 0px #FFFFFF00;
    backdrop-filter: blur(20px);
    background-color: transparent;
}

.white_glow{
    box-shadow: 0px 0px 30px 0px rgba(255, 255, 255, 0.3);
}

.box-radial{
    background: radial-gradient(50% 50% at 50% 50%, rgba(128, 255, 249, 0.15) 0%, rgba(128, 255, 249, 0.075) 100%);
}

.loader-glow{
    box-shadow: 0px 0px 20px 0px rgba(128, 255, 249, 0.4);
}

.green-radial{
    background: radial-gradient(50% 50% at 50% 50%, rgba(95, 229, 92, 0.2) 0%, rgba(95, 229, 92, 0.1) 100%);
}

.white-text-glow{
    text-shadow: 0px 0px 20px 0px #FFFFFF66;
}

.shadow-white{
    background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.09) 0%, rgba(255, 255, 255, 0.075) 100%);
}

.login-slider-card{
    backdrop-filter: blur(112.04522705078125px);
    box-shadow: -1.12px -1.12px 0px 0px rgba(0, 0, 0, 0.05) inset;
}

.dark-text-shadow{
    text-shadow: -1.12px -1.12px 0px 0px rgba(0, 0, 0, 0.05) inset;;
}

.text-white-bg{
    background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.25) 100%);

}

/* Mobile viewport height utilities */
.h-screen-safe {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for modern browsers */
}

.min-h-screen-safe {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for modern browsers */
}

.max-h-screen-safe {
    max-height: 100vh;
    max-height: 100dvh; /* Dynamic viewport height for modern browsers */
}

/* Safe area utilities for mobile */
.pb-safe {
    padding-bottom: env(safe-area-inset-bottom, 0px);
}

.mb-safe {
    margin-bottom: env(safe-area-inset-bottom, 0px);
}

.bottom-safe {
    bottom: env(safe-area-inset-bottom, 0px);
}

/* Combined utilities for bottom-positioned elements */
.bottom-safe-padding {
    padding-bottom: max(8px, env(safe-area-inset-bottom, 0px));
}

.bottom-safe-margin {
    margin-bottom: max(8px, env(safe-area-inset-bottom, 0px));
}


.radial-black-upload{
    background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
}

.radial-green{
    background: radial-gradient(50% 50% at 50% 50%, rgba(128, 255, 249, 0.2) 0%, rgba(128, 255, 249, 0.1) 100%);
}


.radial-green-signup{
    background: radial-gradient(50% 50% at 50% 50%, rgba(95, 229, 92, 0.15) 0%, rgba(95, 229, 92, 0.075) 100%);

}

.golden-text{
    background: rgba(243, 202, 95, 1);
background-blend-mode: overlay;
background: linear-gradient(180deg, #FFFFFF 0%, #000000 100%);
}

.pro-green{
    background: linear-gradient(180deg, rgba(0, 255, 102, 0.1) 0%, rgba(98, 236, 254, 0.1) 100%);
}

.blue-shadow{
    text-shadow: 0px 0px 20px 0px rgba(128, 255, 249, 0.2);
}