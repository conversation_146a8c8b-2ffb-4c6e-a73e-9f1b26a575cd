import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, Check, Clipboard, ClipboardCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type FeedbackType = 'text' | 'icon' | 'tooltip' | 'border' | 'none' | 'icon-text';

interface IconProps {
  size?: number;
  className?: string;
  color?: string;
  clipboard?: boolean;
  [key: string]: any;
}

interface CopyButtonProps {
  /** The value to copy (string or function returning string) */
  value: string | (() => string);

  /** Feedback type to show when copied */
  feedbackType?: FeedbackType;
  
  /** Default button text */
  defaultText?: string;
  
  /** Text shown after copying (for 'text' and 'icon-text' feedback types) */
  copiedText?: string;
  
  /** Text shown in tooltip before copy */
  tooltipText?: string;
  
  /** Text shown in tooltip after copying */
  copiedTooltipText?: string;
  
  /** Duration of feedback in ms */
  feedbackDuration?: number;
  
  /** Additional CSS classes for the container */
  className?: string;
  
  /** CSS classes for the button element */
  buttonClassName?: string;
  
  /** CSS classes for tooltip content */
  tooltipClassName?: string;
  
  /** Custom content to render inside button */
  children?: React.ReactNode;
  
  /** Whether to show an icon */
  showIcon?: boolean;
  
  /** Whether to show only an icon without text */
  iconOnly?: boolean;
  
  /** Props for the icon component */
  iconProps?: IconProps;

  /** Additional class when copied */
  copiedClassName?: string;
  
  /** Callback function after successful copy */
  onCopy?: (copiedText: string) => void;
  
  /** Whether to show tooltip on hover */
  showTooltipOnHover?: boolean;
  
  /** Whether tooltip is enabled */
  tooltipEnabled?: boolean;
  
  /** Control tooltip open state externally */
  tooltipOpen?: boolean;
  
  /** Callback when tooltip open state changes */
  onTooltipOpenChange?: (open: boolean) => void;
}

/**
 * Enhanced CopyButton with shadcn Tooltip support
 */
const CopyButton: React.FC<CopyButtonProps> = ({
  value,
  feedbackType = 'tooltip',
  defaultText = 'Copy',
  copiedText = 'Copied!',
  tooltipText = 'Copy',
  copiedTooltipText = 'Copied',
  feedbackDuration = 1000,
  className = '',
  buttonClassName = '',
  tooltipClassName = '',
  children,
  showIcon = true,
  iconOnly = false,
  iconProps = { size: 16 },
  copiedClassName,
  onCopy,
  showTooltipOnHover = true,
  tooltipEnabled = true,
  tooltipOpen,
  onTooltipOpenChange,
  ...rest
}) => {
  const [copied, setCopied] = useState<boolean>(false);
  const [internalTooltipOpen, setInternalTooltipOpen] = useState<boolean | undefined>(undefined);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, []);

  const handleCopy = async (): Promise<void> => {
    try {
      const textToCopy = typeof value === 'function' ? value() : value;
      await navigator.clipboard.writeText(textToCopy);

      // Clear any existing timeout
      if (timeoutRef.current) clearTimeout(timeoutRef.current);

      // Show feedback
      setCopied(true);
      
      // Force open tooltip on copy if tooltip is enabled
      if (tooltipEnabled && feedbackType === 'tooltip') {
        setInternalTooltipOpen(true);
      }

      // Call onCopy callback if provided
      if (onCopy) onCopy(textToCopy);

      // Reset after duration
      timeoutRef.current = setTimeout(() => {
        setCopied(false);
        
        // Close tooltip after feedback duration if we opened it
        if (tooltipEnabled && feedbackType === 'tooltip' && internalTooltipOpen) {
          setInternalTooltipOpen(false);
        }
      }, feedbackDuration);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Determine which icon to show
  const CopyIcon = copied ? Check : Copy;
  const ClipboardIcon = copied ? ClipboardCheck : Clipboard;
  const Icon = iconProps.clipboard ? ClipboardIcon : CopyIcon;

  // Base button styles - can be overridden with buttonClassName
  const defaultButtonClasses = cn(
    'inline-flex items-center justify-center rounded-md',
    'transition-all duration-200 focus:outline-none',
    buttonClassName || 'px-3 py-1 text-sm bg-gray-800 text-white hover:bg-gray-700',
    copied && feedbackType === 'border' && 'ring-2 ring-green-400 ring-offset-1'
  );

  // Content inside the button
  const buttonContent = () => {
    if (children) return children;

    if (iconOnly) {
      return <Icon {...iconProps} />;
    }

    if (showIcon) {
      return (
        <>
          <Icon {...iconProps} />
          {defaultText && <span className="ml-1">
            {(feedbackType === 'text' || feedbackType === 'icon-text') && copied
              ? copiedText
              : defaultText}
          </span>}
        </>
      );
    }

    return (feedbackType === 'text' || feedbackType === 'icon-text') && copied
      ? copiedText
      : defaultText;
  };

  // Handle tooltip open state (controlled or uncontrolled)
  const isTooltipOpen = tooltipOpen !== undefined ? tooltipOpen : internalTooltipOpen;
  
  const handleTooltipOpenChange = (open: boolean) => {
    // If tooltip is controlled externally, call the callback
    if (onTooltipOpenChange) {
      onTooltipOpenChange(open);
    } else {
      // Otherwise update internal state
      setInternalTooltipOpen(open);
    }
  };

  // If tooltip is disabled, return just the button
  if (!tooltipEnabled || feedbackType !== 'tooltip') {
    return (
      <button
        className={cn(
          defaultButtonClasses,
          className,
          (feedbackType === 'text' || feedbackType === 'icon-text') && copied && copiedClassName
        )}
        onClick={handleCopy}
        type="button"
        {...rest}
      >
        {buttonContent()}
      </button>
    );
  }

  // With tooltip
  return (
    <TooltipProvider>
      <Tooltip 
        open={isTooltipOpen}
        onOpenChange={handleTooltipOpenChange}
        delayDuration={showTooltipOnHover ? 0 : 9999999} 
      >
        <TooltipTrigger asChild>
          <button
            className={cn(
              defaultButtonClasses,
              className,
              copied && copiedClassName
            )}
            onClick={handleCopy}
            type="button"
            {...rest}
          >
            {buttonContent()}
          </button>
        </TooltipTrigger>
        <TooltipContent className={cn(tooltipClassName, "bg-[#DDDDE6] text-black border-0")}>
          {copied ? copiedTooltipText : tooltipText}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default CopyButton;