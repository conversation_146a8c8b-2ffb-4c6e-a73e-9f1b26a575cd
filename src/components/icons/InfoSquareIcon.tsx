import React from 'react';

interface InfoSquareIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  fillOpacity?: number;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const InfoSquareIcon: React.FC<InfoSquareIconProps> = ({
  size = 24,
  width,
  height,
  color = 'white',
  fill,
  fillOpacity = 0.2,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 7C20 5.34315 18.6569 4 17 4H7C5.34315 4 4 5.34315 4 7V17.0001C4 18.6569 5.34315 20.0001 7 20.0001H17C18.6569 20.0001 20 18.6569 20 17.0001L20 7ZM11.9997 10.0834C12.69 10.0834 13.2497 9.52377 13.2497 8.83342C13.2497 8.14306 12.69 7.58342 11.9997 7.58342C11.3093 7.58342 10.7497 8.14306 10.7497 8.83342C10.7497 9.52377 11.3093 10.0834 11.9997 10.0834ZM10.7497 11.3334C10.2894 11.3334 9.91634 11.7065 9.91634 12.1667C9.91634 12.627 10.2894 13.0001 10.7497 13.0001H11.5833L11.5833 16.1667C11.5833 16.627 11.9564 17.0001 12.4167 17.0001C12.8769 17.0001 13.25 16.627 13.25 16.1667L13.25 12.1667C13.25 11.7065 12.8769 11.3334 12.4167 11.3334H10.7497Z"
        fill={iconFill}
        fillOpacity={fillOpacity}
      />
    </svg>
  );
};

export default InfoSquareIcon;
