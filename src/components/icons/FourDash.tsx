import React from 'react';

interface FourDashIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  fillOpacity?: number;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const FourDashIcon: React.FC<FourDashIconProps> = ({
  size = 24,
  width,
  height,
  color = 'white',
  fill,
  fillOpacity = 0.2,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <mask id="mask0_118_2904" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
        <rect width="24" height="24" fill="#D9D9D9"/>
      </mask>
      <g mask="url(#mask0_118_2904)">
        <path
          d="M5.77778 10.6667C5.28889 10.6667 4.87037 10.4926 4.52222 10.1444C4.17407 9.7963 4 9.37778 4 8.88889V5.77778C4 5.28889 4.17407 4.87037 4.52222 4.52222C4.87037 4.17407 5.28889 4 5.77778 4H8.88889C9.37778 4 9.7963 4.17407 10.1444 4.52222C10.4926 4.87037 10.6667 5.28889 10.6667 5.77778V8.88889C10.6667 9.37778 10.4926 9.7963 10.1444 10.1444C9.7963 10.4926 9.37778 10.6667 8.88889 10.6667H5.77778ZM5.77778 20C5.28889 20 4.87037 19.8259 4.52222 19.4778C4.17407 19.1296 4 18.7111 4 18.2222V15.1111C4 14.6222 4.17407 14.2037 4.52222 13.8556C4.87037 13.5074 5.28889 13.3333 5.77778 13.3333H8.88889C9.37778 13.3333 9.7963 13.5074 10.1444 13.8556C10.4926 14.2037 10.6667 14.6222 10.6667 15.1111V18.2222C10.6667 18.7111 10.4926 19.1296 10.1444 19.4778C9.7963 19.8259 9.37778 20 8.88889 20H5.77778ZM15.1111 10.6667C14.6222 10.6667 14.2037 10.4926 13.8556 10.1444C13.5074 9.7963 13.3333 9.37778 13.3333 8.88889V5.77778C13.3333 5.28889 13.5074 4.87037 13.8556 4.52222C14.2037 4.17407 14.6222 4 15.1111 4H18.2222C18.7111 4 19.1296 4.17407 19.4778 4.52222C19.8259 4.87037 20 5.28889 20 5.77778V8.88889C20 9.37778 19.8259 9.7963 19.4778 10.1444C19.1296 10.4926 18.7111 10.6667 18.2222 10.6667H15.1111ZM15.1111 20C14.6222 20 14.2037 19.8259 13.8556 19.4778C13.5074 19.1296 13.3333 18.7111 13.3333 18.2222V15.1111C13.3333 14.6222 13.5074 14.2037 13.8556 13.8556C14.2037 13.5074 14.6222 13.3333 15.1111 13.3333H18.2222C18.7111 13.3333 19.1296 13.5074 19.4778 13.8556C19.8259 14.2037 20 14.6222 20 15.1111V18.2222C20 18.7111 19.8259 19.1296 19.4778 19.4778C19.1296 19.8259 18.7111 20 18.2222 20H15.1111Z"
          fill={iconFill}
          fillOpacity={fillOpacity}
        />
      </g>
    </svg>
  );
};

export default FourDashIcon;
