const MoreIcon = ({
    size = 24,
    color = "#626266",
    backgroundColor = "white",
    backgroundOpacity = 0.02,
    className = "",
    ...props
  }) => {
    return (
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        {...props}
      >
        <rect
          width="24"
          height="24"
          rx="6"
          fill={backgroundColor}
          fillOpacity={backgroundOpacity}
        />
        <path
          d="M6.75 13.75C6.26875 13.75 5.85677 13.5786 5.51406 13.2359C5.17135 12.8932 5 12.4813 5 12C5 11.5187 5.17135 11.1068 5.51406 10.7641C5.85677 10.4214 6.26875 10.25 6.75 10.25C7.23125 10.25 7.64323 10.4214 7.98594 10.7641C8.32865 11.1068 8.5 11.5187 8.5 12C8.5 12.4813 8.32865 12.8932 7.98594 13.2359C7.64323 13.5786 7.23125 13.75 6.75 13.75ZM12 13.75C11.5188 13.75 11.1068 13.5786 10.7641 13.2359C10.4214 12.8932 10.25 12.4813 10.25 12C10.25 11.5187 10.4214 11.1068 10.7641 10.7641C11.1068 10.4214 11.5188 10.25 12 10.25C12.4812 10.25 12.8932 10.4214 13.2359 10.7641C13.5786 11.1068 13.75 11.5187 13.75 12C13.75 12.4813 13.5786 12.8932 13.2359 13.2359C12.8932 13.5786 12.4812 13.75 12 13.75ZM17.25 13.75C16.7688 13.75 16.3568 13.5786 16.0141 13.2359C15.6714 12.8932 15.5 12.4813 15.5 12C15.5 11.5187 15.6714 11.1068 16.0141 10.7641C16.3568 10.4214 16.7688 10.25 17.25 10.25C17.7312 10.25 18.1432 10.4214 18.4859 10.7641C18.8286 11.1068 19 11.5187 19 12C19 12.4813 18.8286 12.8932 18.4859 13.2359C18.1432 13.5786 17.7312 13.75 17.25 13.75Z"
          fill={color}
        />
      </svg>
    );
  };

export default MoreIcon;
