import React from 'react';

interface ExpandIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const ExpandIcon: React.FC<ExpandIconProps> = ({
  size = 16,
  width,
  height,
  color = 'white',
  fill,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <path
        d="M11.586 3L8.586 6L10 7.414L13 4.414V7H15V1H9V3H11.586ZM4.414 13L7.414 10L6 8.586L3 11.586V9H1V15H7V13H4.414Z"
        fill={iconFill}
      />
    </svg>
  );
};

export default ExpandIcon;
