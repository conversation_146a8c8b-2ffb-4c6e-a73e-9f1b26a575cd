import React from 'react';

interface DeleteIconProps {
  width?: number;
  height?: number;
  className?: string;
  fill?: string;
}

const DeleteIcon: React.FC<DeleteIconProps> = ({
  width = 16,
  height = 16,
  className,
  fill = "#CCCCCC"
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.19913 3.2C7.06585 3.1998 6.9363 3.24398 6.83092 3.32557C6.72553 3.40716 6.65031 3.52152 6.61713 3.6506L6.44193 4.3346H9.69453L9.51873 3.6506C9.48557 3.52162 9.41044 3.40734 9.30518 3.32576C9.19991 3.24418 9.0705 3.19993 8.93733 3.2H7.19913ZM10.9335 4.3352L10.6809 3.3518C10.5815 2.96496 10.3562 2.62219 10.0405 2.37745C9.72485 2.13272 9.33675 1.99993 8.93733 2H7.19913C6.79971 1.99993 6.41161 2.13272 6.09595 2.37745C5.78028 2.62219 5.55498 2.96496 5.45553 3.3518L5.20293 4.3352H3.26553C3.1064 4.3352 2.95379 4.39841 2.84126 4.51094C2.72874 4.62346 2.66553 4.77607 2.66553 4.9352C2.66553 5.09433 2.72874 5.24694 2.84126 5.35946C2.95379 5.47199 3.1064 5.5352 3.26553 5.5352H3.32553L4.05453 12.3902C4.10151 12.8324 4.31049 13.2415 4.64118 13.5387C4.97188 13.8359 5.40088 14.0003 5.84553 14H10.2855C10.7301 14.0001 11.1589 13.8357 11.4895 13.5385C11.8201 13.2413 12.029 12.8323 12.0759 12.3902L12.8043 5.5352H12.8655C13.0247 5.5352 13.1773 5.47199 13.2898 5.35946C13.4023 5.24694 13.4655 5.09433 13.4655 4.9352C13.4655 4.77607 13.4023 4.62346 13.2898 4.51094C13.1773 4.39841 13.0247 4.3352 12.8655 4.3352H10.9335ZM11.5983 5.5352H4.53273L5.24793 12.2636C5.26365 12.4111 5.33341 12.5475 5.44376 12.6465C5.55412 12.7456 5.69724 12.8002 5.84553 12.8H10.2855C10.4337 12.8001 10.5767 12.7453 10.6869 12.6463C10.7971 12.5473 10.8668 12.4109 10.8825 12.2636L11.5977 5.5352H11.5983ZM6.86553 6.8C7.02466 6.8 7.17727 6.86321 7.28979 6.97574C7.40231 7.08826 7.46553 7.24087 7.46553 7.4V10.4C7.46553 10.5591 7.40231 10.7117 7.28979 10.8243C7.17727 10.9368 7.02466 11 6.86553 11C6.7064 11 6.55379 10.9368 6.44126 10.8243C6.32874 10.7117 6.26553 10.5591 6.26553 10.4V7.4C6.26553 7.24087 6.32874 7.08826 6.44126 6.97574C6.55379 6.86321 6.7064 6.8 6.86553 6.8ZM9.26553 6.8C9.42466 6.8 9.57727 6.86321 9.68979 6.97574C9.80231 7.08826 9.86553 7.24087 9.86553 7.4V10.4C9.86553 10.5591 9.80231 10.7117 9.68979 10.8243C9.57727 10.9368 9.42466 11 9.26553 11C9.1064 11 8.95379 10.9368 8.84126 10.8243C8.72874 10.7117 8.66553 10.5591 8.66553 10.4V7.4C8.66553 7.24087 8.72874 7.08826 8.84126 6.97574C8.95379 6.86321 9.1064 6.8 9.26553 6.8Z"
        fill={fill}
      />
    </svg>
  );
};

export default DeleteIcon;
