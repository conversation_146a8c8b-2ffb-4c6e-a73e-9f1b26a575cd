import React from 'react';

interface SettingsIconProps {
  width?: number | string;
  height?: number | string;
  size?: number | string;
  fill?: string;
  color?: string;
  className?: string;
  onClick?: () => void;
}

const SettingsIcon: React.FC<SettingsIconProps> = ({
  width,
  height,
  size = 16,
  fill,
  color = '#808080',
  className,
  onClick,
}) => {
  // Use size for both width and height if provided, otherwise use individual width/height
  const iconWidth = width ?? size;
  const iconHeight = height ?? size;
  const iconColor = fill ?? color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.01781 1.77114C5.85109 1.55645 6.72751 1.57629 7.55021 1.82849C8.37292 2.08068 9.1099 2.5554 9.67971 3.2002C10.2495 3.845 10.63 4.63477 10.7791 5.48225C10.9281 6.32973 10.84 7.20193 10.5245 8.00247L13.5905 10.5978C13.823 10.7943 14.0122 11.0369 14.1461 11.3103C14.2801 11.5837 14.3558 11.882 14.3685 12.1861C14.3813 12.4903 14.3307 12.7938 14.2201 13.0775C14.1095 13.3611 13.9412 13.6187 13.7259 13.8339C13.5106 14.0492 13.253 14.2174 12.9693 14.328C12.6857 14.4385 12.3822 14.489 12.078 14.4762C11.7738 14.4634 11.4756 14.3876 11.2022 14.2536C10.9289 14.1196 10.6863 13.9304 10.4898 13.6978L7.89581 10.6331C7.09517 10.9492 6.22272 11.0377 5.37491 10.8889C4.5271 10.74 3.73697 10.3596 3.09187 9.78974C2.44677 9.21985 1.97184 8.48266 1.71958 7.65968C1.46732 6.83669 1.44756 5.95998 1.66248 5.12647C1.69278 5.00936 1.75408 4.90257 1.83993 4.81733C1.92577 4.7321 2.03299 4.67156 2.15032 4.6421C2.26765 4.61263 2.39075 4.61531 2.50669 4.64987C2.62262 4.68442 2.7271 4.74958 2.80915 4.83847L4.83915 7.04314L6.37048 6.47981L6.93515 4.94647L4.72915 2.91981C4.63972 2.83779 4.57411 2.73312 4.53928 2.61688C4.50444 2.50065 4.50166 2.37715 4.53124 2.25947C4.56081 2.14178 4.62164 2.03427 4.70729 1.94831C4.79294 1.86235 4.90023 1.80114 5.01781 1.77114Z"
        fill={iconColor}
      />
    </svg>
  );
};

export default SettingsIcon;
