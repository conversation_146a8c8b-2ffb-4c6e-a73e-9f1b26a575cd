import React from 'react';

interface EditIconProps {
  width?: number;
  height?: number;
  className?: string;
  fill?: string;
  fillOpacity?: number;
}

const EditIcon: React.FC<EditIconProps> = ({
  width = 16,
  height = 16,
  className,
  fill = "white",
  fillOpacity = 0.6
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M2.3999 13.5999V9.77324L9.27546 2.92879C9.46212 2.74212 9.66953 2.60731 9.89768 2.52435C10.1258 2.44138 10.354 2.3999 10.5821 2.3999C10.831 2.3999 11.0695 2.44657 11.2977 2.5399C11.5258 2.63324 11.7332 2.77324 11.9199 2.9599L13.071 4.11101C13.2369 4.29768 13.3666 4.50509 13.4599 4.73324C13.5532 4.96138 13.5999 5.18953 13.5999 5.41768C13.5999 5.64583 13.5584 5.87916 13.4755 6.11768C13.3925 6.3562 13.2577 6.56879 13.071 6.75546L6.22657 13.5999H2.3999ZM4.26657 11.7332H5.44879L9.21324 7.93768L8.65324 7.34657L8.06212 6.78657L4.26657 10.551V11.7332ZM8.65324 7.34657L8.06212 6.78657L9.21324 7.93768L8.65324 7.34657Z"
        fill={fill}
        fillOpacity={fillOpacity}
      />
    </svg>
  );
};

export default EditIcon;
