import PropTypes from 'prop-types';

const SettingsIcon = ({
    size = 16,
    color = "#E8E8E6",
    className = "",
    ...props
  }) => {
    return (
      <svg
        width={size}
        height={size}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        {...props}
      >
        <path
          d="M8.43865 1.6001C9.07985 1.6001 9.59961 2.11988 9.59961 2.76103C9.59961 2.91653 9.70969 3.08344 9.90745 3.16182C9.96233 3.18356 10.0168 3.20625 10.0707 3.22979L10.1434 3.25557C10.3127 3.30343 10.4748 3.26076 10.5714 3.16416C11.0255 2.71063 11.7614 2.71115 12.2152 3.16494L12.8347 3.78447L12.9145 3.87275C13.2869 4.32919 13.2605 5.00287 12.8347 5.42823C12.7245 5.53847 12.6851 5.73408 12.7699 5.92823L12.8379 6.09228L12.8714 6.16182C12.9581 6.31554 13.1026 6.40008 13.2386 6.4001C13.8798 6.4001 14.3996 6.91988 14.3996 7.56103V8.43914C14.3996 9.08034 13.8798 9.6001 13.2386 9.6001C13.1026 9.6001 12.9582 9.68466 12.8714 9.83842L12.8379 9.90794C12.8161 9.96274 12.7934 10.0174 12.7699 10.0712C12.685 10.2655 12.7243 10.4615 12.8347 10.5719C13.2886 11.0259 13.2886 11.7618 12.8347 12.2157L12.2152 12.8352C11.7613 13.2891 11.0254 13.2891 10.5714 12.8352C10.4749 12.7387 10.3127 12.6961 10.1434 12.7439L10.0707 12.7704C10.0171 12.7938 9.96273 12.8159 9.90817 12.8376C9.71041 12.916 9.59977 13.0828 9.59961 13.2384C9.59961 13.8798 9.07929 14.4001 8.43793 14.4001H7.56133C6.91993 14.4001 6.39961 13.8798 6.39961 13.2384C6.39946 13.1022 6.31496 12.9576 6.16133 12.8712L6.09179 12.8376C6.03681 12.8159 5.98177 12.7932 5.92774 12.7696C5.73338 12.685 5.53792 12.7251 5.42774 12.8352C5.00238 13.261 4.32871 13.2874 3.87227 12.915L3.78399 12.8352L3.16445 12.2157C2.71067 11.7619 2.71015 11.026 3.16367 10.5719L3.20195 10.5267C3.27081 10.4288 3.29609 10.289 3.25508 10.1439L3.2293 10.0712C3.20576 10.0173 3.18307 9.96282 3.16133 9.90794C3.08295 9.71018 2.91604 9.6001 2.76055 9.6001C2.11939 9.6001 1.59961 9.08034 1.59961 8.43914V7.56103C1.59961 6.91988 2.11939 6.4001 2.76055 6.4001C2.91602 6.40008 3.0823 6.28987 3.16055 6.09228L3.2293 5.92823L3.25508 5.85557C3.29607 5.71039 3.27089 5.5705 3.20195 5.47275L3.16367 5.42823C2.70998 4.97453 2.70998 4.23817 3.16367 3.78447L3.78399 3.16416L3.87227 3.08447C4.32858 2.71224 5.00237 2.7388 5.42774 3.16416C5.53798 3.27438 5.73335 3.31456 5.92774 3.22979C5.98196 3.20613 6.03661 3.18288 6.09179 3.16103C6.28938 3.08279 6.39959 2.91651 6.39961 2.76103C6.39961 2.11988 6.91939 1.6001 7.56055 1.6001H8.43865ZM7.56055 2.4001C7.36123 2.4001 7.19961 2.56171 7.19961 2.76103C7.19959 3.29991 6.83431 3.72823 6.38633 3.90557C6.33973 3.92402 6.2931 3.94261 6.24727 3.96259C5.80447 4.15569 5.24263 4.11185 4.86133 3.73057C4.7377 3.60693 4.54707 3.59094 4.40664 3.68369L4.35039 3.73057L3.73008 4.35088C3.5888 4.49215 3.5888 4.72054 3.73008 4.86182C4.0875 5.21926 4.14847 5.73546 3.99571 6.16338L3.96211 6.24775C3.94212 6.29359 3.92353 6.34022 3.90508 6.38682C3.72775 6.8348 3.29942 7.20008 2.76055 7.2001C2.56123 7.2001 2.39961 7.36171 2.39961 7.56103V8.43914C2.39961 8.6385 2.56123 8.8001 2.76055 8.8001C3.29942 8.8001 3.7276 9.16562 3.90508 9.61338C3.92339 9.65954 3.94228 9.70546 3.96211 9.7509C4.15545 10.1938 4.11127 10.756 3.73008 11.1376C3.58863 11.2791 3.58858 11.5086 3.73008 11.6501L4.34961 12.2696L4.40664 12.3165C4.54705 12.4091 4.73779 12.3932 4.86133 12.2696C5.24267 11.888 5.80509 11.8436 6.24805 12.0368C6.29363 12.0567 6.33998 12.0755 6.38633 12.0939L6.46914 12.1306C6.87835 12.3258 7.19946 12.7333 7.19961 13.2384C7.19961 13.4379 7.36175 13.6001 7.56133 13.6001H8.43793C8.63745 13.6001 8.79961 13.4379 8.79961 13.2384C8.79977 12.6995 9.16505 12.2714 9.61289 12.0939C9.65905 12.0755 9.70497 12.0567 9.75041 12.0368L9.83473 12.0032C10.263 11.8502 10.7794 11.9119 11.1371 12.2696C11.2786 12.4111 11.5081 12.4111 11.6496 12.2696L12.2691 11.6501C12.4106 11.5086 12.4106 11.2791 12.2691 11.1376C11.8876 10.7561 11.8433 10.194 12.0371 9.7509C12.057 9.7053 12.0758 9.6589 12.0942 9.61258L12.1309 9.52978C12.3263 9.12114 12.7336 8.8001 13.2386 8.8001C13.438 8.8001 13.5996 8.6385 13.5996 8.43914V7.56103C13.5996 7.36171 13.438 7.2001 13.2386 7.2001C12.7334 7.20008 12.3261 6.8787 12.1309 6.46963L12.0942 6.38682L12.0363 6.24854C11.8426 5.8055 11.8875 5.24315 12.2691 4.86182C12.3927 4.73827 12.4086 4.54754 12.316 4.40713L12.2691 4.3501L11.6496 3.73057C11.5259 3.60685 11.3348 3.59112 11.1942 3.68369L11.1371 3.73057C10.7555 4.11176 10.1933 4.15594 9.75041 3.96259L9.61289 3.90557C9.16513 3.72809 8.79961 3.29991 8.79961 2.76103C8.79961 2.56171 8.63801 2.4001 8.43865 2.4001H7.56055ZM7.99961 5.6001C9.32513 5.6001 10.3996 6.67462 10.3996 8.0001C10.3996 9.32562 9.32513 10.4001 7.99961 10.4001C6.67413 10.4001 5.59961 9.32562 5.59961 8.0001C5.59961 6.67462 6.67413 5.6001 7.99961 5.6001ZM7.99961 6.4001C7.11595 6.4001 6.39961 7.11644 6.39961 8.0001C6.39961 8.88378 7.11595 9.6001 7.99961 9.6001C8.88329 9.6001 9.59961 8.88378 9.59961 8.0001C9.59961 7.11644 8.88329 6.4001 7.99961 6.4001Z"
          fill={color}
        />
      </svg>
    );
  };

// PropTypes definition
SettingsIcon.propTypes = {
    size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    color: PropTypes.string,
    className: PropTypes.string,
    onClick: PropTypes.func,
    onMouseEnter: PropTypes.func,
    onMouseLeave: PropTypes.func,
    style: PropTypes.object,
    'aria-label': PropTypes.string,
    'aria-hidden': PropTypes.bool,
    role: PropTypes.string,
    id: PropTypes.string,
    'data-testid': PropTypes.string,
  };

export default SettingsIcon;
