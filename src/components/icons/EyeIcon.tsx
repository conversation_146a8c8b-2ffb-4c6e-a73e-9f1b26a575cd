import PropTypes from 'prop-types';


const EyeIcon = ({ 
    width = 57, 
    height = 38, 
    className = "",
    fill = "white",
    ...props 
  }) => {
    return (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width={width} 
        height={height} 
        viewBox="0 0 57 38" 
        fill="none"
        className={className}
        {...props}
      >
        <defs>
          <filter 
            id="filter0_d_11791_110933" 
            x="-0.503906" 
            y="-7.60205" 
            width="58" 
            height="53.2012" 
            filterUnits="userSpaceOnUse" 
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feColorMatrix 
              in="SourceAlpha" 
              type="matrix" 
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" 
              result="hardAlpha"
            />
            <feOffset/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix 
              type="matrix" 
              values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"
            />
            <feBlend 
              mode="normal" 
              in2="BackgroundImageFix" 
              result="effect1_dropShadow_11791_110933"
            />
            <feBlend 
              mode="normal" 
              in="SourceGraphic" 
              in2="effect1_dropShadow_11791_110933" 
              result="shape"
            />
          </filter>
        </defs>
        <g filter="url(#filter0_d_11791_110933)">
          <path 
            d="M28.4967 12.3979C30.4524 12.4715 32.344 13.1167 33.9371 14.2534C35.5302 15.3902 36.7548 16.969 37.4605 18.7944C37.5082 18.9263 37.5082 19.0708 37.4605 19.2026C36.7548 21.028 35.5302 22.6069 33.9371 23.7437C32.344 24.8804 30.4524 25.5255 28.4967 25.5991C26.541 25.5256 24.6494 24.8803 23.0562 23.7437C21.4631 22.6069 20.2376 21.0281 19.5318 19.2026C19.4842 19.0708 19.4842 18.9262 19.5318 18.7944C20.2376 16.9689 21.463 15.3902 23.0562 14.2534C24.6494 13.1167 26.5409 12.4715 28.4967 12.3979ZM29.9889 15.395C29.2763 15.0999 28.4924 15.023 27.7359 15.1733C26.9794 15.3238 26.2843 15.6953 25.7389 16.2407C25.1934 16.7862 24.822 17.4812 24.6715 18.2378C24.5211 18.9942 24.598 19.7782 24.8932 20.4907C25.1884 21.2034 25.6883 21.8131 26.3297 22.2417C26.971 22.6701 27.7254 22.8989 28.4967 22.8989C29.5305 22.8973 30.5215 22.4854 31.2525 21.7544C31.9835 21.0233 32.3954 20.0324 32.3971 18.9985C32.3971 18.2271 32.1674 17.4729 31.7389 16.8315C31.3103 16.1903 30.7015 15.6902 29.9889 15.395ZM28.4976 16.5972C29.8232 16.5972 30.898 17.672 30.898 18.9976C30.898 20.3231 29.8232 21.3979 28.4976 21.3979C27.1723 21.3977 26.0983 20.323 26.0982 18.9976C26.0982 17.6721 27.1723 16.5974 28.4976 16.5972Z" 
            fill={fill}
          />
        </g>
      </svg>
    );
  };
  
// PropTypes definition
EyeIcon.propTypes = {
    width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    className: PropTypes.string,
    fill: PropTypes.string,
    onClick: PropTypes.func,
    onMouseEnter: PropTypes.func,
    onMouseLeave: PropTypes.func,
    style: PropTypes.object,
    'aria-label': PropTypes.string,
    'aria-hidden': PropTypes.bool,
    role: PropTypes.string,
    id: PropTypes.string,
    'data-testid': PropTypes.string,
  };

  export default EyeIcon;
