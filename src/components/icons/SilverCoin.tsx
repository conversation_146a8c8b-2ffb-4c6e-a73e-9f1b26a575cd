
const SilverCoin = ({ width = 20, height = 20, className = "" }) => {
    return (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width={width} 
        height={height} 
        viewBox="0 0 20 20" 
        fill="none"
        className={className}
      >
        <g style={{ mixBlendMode: "luminosity" }} clipPath="url(#clip0_12024_101153)">
          <circle 
            opacity="0.1" 
            cx="10" 
            cy="10" 
            r="10" 
            fill="#F3CA5F"
          />
          <path 
            d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10.4082 6.58984C10.1446 5.93661 9.92302 5.71814 9.60645 6.49805C8.91401 8.15984 8.0872 8.95183 6.45996 9.6377C6.29657 9.71319 6.00191 9.84453 6 10C6.00211 10.1554 6.29471 10.2868 6.45996 10.3623C8.08527 11.0462 8.91405 11.8399 9.60645 13.502C9.9268 14.2958 10.1491 14.0465 10.4082 13.4102C11.1026 11.8036 11.8788 11.0584 13.5166 10.3623C13.6883 10.2804 13.9713 10.1709 14 10.0078V9.98926C13.9709 9.8277 13.6901 9.71755 13.5166 9.63574V9.6377C11.8789 8.9416 11.1026 8.19694 10.4082 6.58984Z" 
            fill="#F3CA5F"
          />
          <path 
            d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10.4082 6.58984C10.1446 5.93661 9.92302 5.71814 9.60645 6.49805C8.91401 8.15984 8.0872 8.95183 6.45996 9.6377C6.29657 9.71319 6.00191 9.84453 6 10C6.00211 10.1554 6.29471 10.2868 6.45996 10.3623C8.08527 11.0462 8.91405 11.8399 9.60645 13.502C9.9268 14.2958 10.1491 14.0465 10.4082 13.4102C11.1026 11.8036 11.8788 11.0584 13.5166 10.3623C13.6883 10.2804 13.9713 10.1709 14 10.0078V9.98926C13.9709 9.8277 13.6901 9.71755 13.5166 9.63574V9.6377C11.8789 8.9416 11.1026 8.19694 10.4082 6.58984Z" 
            fill="url(#paint0_linear_12024_101153)" 
            style={{ mixBlendMode: "overlay" }}
          />
        </g>
        <defs>
          <linearGradient 
            id="paint0_linear_12024_101153" 
            x1="10" 
            y1="2" 
            x2="10" 
            y2="18" 
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="white"/>
            <stop offset="1"/>
          </linearGradient>
          <clipPath id="clip0_12024_101153">
            <rect width="20" height="20" fill="white"/>
          </clipPath>
        </defs>
      </svg>
    );
  };
  

  export default SilverCoin;