import React from 'react';

interface CircleDashedIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  fillOpacity?: number;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const CircleDashedIcon: React.FC<CircleDashedIconProps> = ({
  size = 24,
  width,
  height,
  color = 'white',
  fill,
  fillOpacity = 0.2,
  className = "",
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <mask id="mask0_180_3078" style={{maskType: "alpha"}} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
        <rect width="24" height="24" fill="#D9D9D9"/>
      </mask>
      <g mask="url(#mask0_180_3078)">
        <path
          d="M4.755 17.355C4.2 16.605 3.76875 15.7762 3.46125 14.8687C3.15375 13.9612 3 13.005 3 12C3 10.995 3.15 10.0425 3.45 9.1425C3.75 8.2425 4.1775 7.4175 4.7325 6.6675L6.0375 7.95C5.6475 8.52 5.34375 9.14625 5.12625 9.82875C4.90875 10.5113 4.8 11.235 4.8 12C4.8 12.765 4.90875 13.4925 5.12625 14.1825C5.34375 14.8725 5.6475 15.5025 6.0375 16.0725L4.755 17.355ZM12 21C10.995 21 10.0425 20.85 9.1425 20.55C8.2425 20.25 7.4175 19.8225 6.6675 19.2675L7.95 17.9625C8.52 18.3525 9.14625 18.6562 9.82875 18.8738C10.5113 19.0912 11.235 19.2 12 19.2C12.765 19.2 13.4888 19.0912 14.1713 18.8738C14.8538 18.6562 15.48 18.3525 16.05 17.9625L17.3325 19.2675C16.5825 19.8225 15.7575 20.25 14.8575 20.55C13.9575 20.85 13.005 21 12 21ZM19.245 17.355L17.9625 16.0725C18.3525 15.5025 18.6562 14.8725 18.8738 14.1825C19.0912 13.4925 19.2 12.765 19.2 12C19.2 11.235 19.0912 10.5113 18.8738 9.82875C18.6562 9.14625 18.3525 8.52 17.9625 7.95L19.2675 6.6675C19.8225 7.4175 20.25 8.2425 20.55 9.1425C20.85 10.0425 21 10.995 21 12C21 13.005 20.8463 13.9612 20.5387 14.8687C20.2313 15.7762 19.8 16.605 19.245 17.355ZM7.9275 6.0375L6.645 4.755C7.395 4.2 8.22375 3.76875 9.13125 3.46125C10.0387 3.15375 10.995 3 12 3C13.02 3 13.98 3.15375 14.88 3.46125C15.78 3.76875 16.605 4.2 17.355 4.755L16.0725 6.0375C15.5025 5.6475 14.8725 5.34375 14.1825 5.12625C13.4925 4.90875 12.765 4.8 12 4.8C11.235 4.8 10.5075 4.90875 9.8175 5.12625C9.1275 5.34375 8.4975 5.6475 7.9275 6.0375ZM12 16.5C10.755 16.5 9.69375 16.0612 8.81625 15.1838C7.93875 14.3062 7.5 13.245 7.5 12C7.5 10.755 7.93875 9.69375 8.81625 8.81625C9.69375 7.93875 10.755 7.5 12 7.5C13.245 7.5 14.3062 7.93875 15.1838 8.81625C16.0612 9.69375 16.5 10.755 16.5 12C16.5 13.245 16.0612 14.3062 15.1838 15.1838C14.3062 16.0612 13.245 16.5 12 16.5Z"
          fill={iconFill}
          fillOpacity={fillOpacity}
        />
      </g>
    </svg>
  );
};

export default CircleDashedIcon;
