import React from 'react';

interface GlobeIconProps {
  /** Width of the icon */
  width?: number | string;
  /** Height of the icon */
  height?: number | string;
  /** Fill color for the icon */
  fill?: string;
  /** Opacity of the icon */
  opacity?: number;
  /** CSS class name */
  className?: string;
  /** Additional inline styles */
  style?: React.CSSProperties;
  /** Click handler */
  onClick?: (event: React.MouseEvent<SVGSVGElement>) => void;
  /** Accessibility label */
  'aria-label'?: string;
  /** Whether the icon is focusable */
  focusable?: boolean;
  /** Custom viewBox */
  viewBox?: string;
}

const GlobeIcon: React.FC<GlobeIconProps> = ({
  width = 20,
  height = 20,
  fill = 'white',
  opacity = 0.5,
  className,
  style,
  onClick,
  'aria-label': ariaLabel = 'Globe icon',
  focusable = false,
  viewBox = '0 0 20 20',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox={viewBox}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      aria-label={ariaLabel}
      focusable={focusable}
      {...props}
    >
      <path
        opacity={opacity}
        d="M10 2.00006C14.4184 2.00006 18 5.58161 18 10.0001C18 14.4185 14.4184 18.0001 10 18.0001C5.58154 18.0001 2 14.4185 2 10.0001C2 5.58161 5.58154 2.00007 10 2.00006ZM3.62568 10.788C3.94054 13.3618 5.77655 15.4633 8.20806 16.1684C7.17013 14.556 6.53788 12.7103 6.37178 10.788H3.62568ZM13.6282 10.788C13.4621 12.7105 12.8291 14.5559 11.791 16.1684C14.223 15.4635 16.0594 13.3621 16.3743 10.788H13.6282ZM7.95312 10.788C8.14159 12.6763 8.85009 14.4748 10 15.9827C11.1499 14.4748 11.8584 12.6763 12.0469 10.788H7.95312ZM11.791 3.8308C12.8293 5.44342 13.4621 7.28937 13.6282 9.21216H16.3743C16.0594 6.63797 14.2231 4.53562 11.791 3.8308ZM10 4.01646C8.84982 5.52452 8.14162 7.32361 7.95312 9.21216H12.0469C11.8584 7.32362 11.1501 5.52453 10 4.01646ZM8.20806 3.8308C5.77642 4.53587 3.94055 6.63827 3.62568 9.21216H6.37178C6.5379 7.28954 7.16988 5.44333 8.20806 3.8308Z"
        fill={fill}
      />
    </svg>
  );
};

export default GlobeIcon;