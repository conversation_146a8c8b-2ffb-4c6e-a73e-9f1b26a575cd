import React from 'react';

interface NormalRobotProps extends React.SVGProps<SVGSVGElement> {
  size?: number | string;
  primaryColor?: string;
  secondaryColor?: string;
}

const NormalRobot: React.FC<NormalRobotProps> = ({
  size = 20,
  primaryColor = "#80FFF9",
  secondaryColor = "#1D1D1E",
  className,
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M14.5039 4C15.8846 4 17.0039 5.11929 17.0039 6.5V13.5C17.0039 14.8807 15.8846 16 14.5039 16H5.50391C4.12319 16 3.00391 14.8807 3.00391 13.5V6.5C3.00391 5.11929 4.12319 4 5.50391 4H14.5039Z"
        fill={primaryColor}
      />
      <path
        d="M5 12C5 10.8954 5.89543 10 7 10H13C14.1046 10 15 10.8954 15 12C15 13.1046 14.1046 14 13 14H7C5.89543 14 5 13.1046 5 12Z"
        fill={secondaryColor}
      />
      <rect
        width="2"
        height="2"
        rx="1"
        transform="matrix(1 0 0 -1 7 13)"
        fill={primaryColor}
      />
      <rect
        width="2"
        height="2"
        rx="1"
        transform="matrix(1 0 0 -1 11 13)"
        fill={primaryColor}
      />
      <rect
        y="9.00195"
        width="2"
        height="4"
        rx="1"
        fill={primaryColor}
      />
      <rect
        x="18"
        y="9.00195"
        width="2"
        height="4"
        rx="1"
        fill={primaryColor}
      />
    </svg>
  );
};

export default NormalRobot;
