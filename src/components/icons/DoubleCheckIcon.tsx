import React from 'react';

interface DoubleCheckIconProps {
  /** Width of the icon */
  width?: number | string;
  /** Height of the icon */
  height?: number | string;
  /** Stroke color for the icon */
  stroke?: string;
  /** Stroke width */
  strokeWidth?: number | string;
  /** Stroke line cap style */
  strokeLinecap?: 'butt' | 'round' | 'square';
  /** Stroke line join style */
  strokeLinejoin?: 'miter' | 'round' | 'bevel';
  /** CSS class name */
  className?: string;
  /** Additional inline styles */
  style?: React.CSSProperties;
  /** Click handler */
  onClick?: (event: React.MouseEvent<SVGSVGElement>) => void;
  /** Accessibility label */
  'aria-label'?: string;
  /** Whether the icon is focusable */
  focusable?: boolean;
  /** Custom viewBox */
  viewBox?: string;
}

const DoubleCheckIcon: React.FC<DoubleCheckIconProps> = ({
  width = 16,
  height = 16,
  stroke = '#66C2FF',
  strokeWidth = 2.66667,
  strokeLinecap = 'round',
  strokeLinejoin = 'round',
  className,
  style,
  onClick,
  'aria-label': ariaLabel = 'Double check icon',
  focusable = false,
  viewBox = '0 0 16 16',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox={viewBox}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      aria-label={ariaLabel}
      focusable={focusable}
      {...props}
    >
      <path
        d="M1.33337 7.99998L4.00004 10.6666L9.33337 5.33331M9.33337 10.6666L14.6667 5.33331"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap={strokeLinecap}
        strokeLinejoin={strokeLinejoin}
      />
    </svg>
  );
};

export default DoubleCheckIcon;