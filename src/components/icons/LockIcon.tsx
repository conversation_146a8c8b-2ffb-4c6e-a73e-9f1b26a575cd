import PropTypes from 'prop-types';

const LockIcon = ({
    size = 24,
    color = "white",
    opacity = 0.3,
    className = "",
    ...props
  }) => {
    // Generate unique mask ID to avoid conflicts when multiple instances are rendered
    const maskId = `mask_lock_${Math.random().toString(36).substring(2, 11)}`;

    return (
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        {...props}
      >
        <mask
          id={maskId}
          style={{maskType: "alpha"}}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="24"
        >
          <rect width="24" height="24" fill="#D9D9D9"/>
        </mask>
        <g mask={`url(#${maskId})`}>
          <path
            d="M8.19099 10.8571C7.58478 10.8571 7.0034 11.098 6.57474 11.5266C6.14609 11.9553 5.90527 12.5366 5.90527 13.1429V17.7143C5.90527 18.3205 6.14609 18.9019 6.57474 19.3305C7.0034 19.7592 7.58478 20 8.19099 20H15.8093C16.4155 20 16.9969 19.7592 17.4255 19.3305C17.8542 18.9019 18.095 18.3205 18.095 17.7143V13.1429C18.095 12.5366 17.8542 11.9553 17.4255 11.5266C16.9969 11.098 16.4155 10.8571 15.8093 10.8571H8.19099ZM12.0001 4C10.9899 4 10.021 4.40132 9.30666 5.11567C8.59231 5.83003 8.19099 6.7989 8.19099 7.80914V9.33257C8.18702 9.43508 8.20378 9.53734 8.24027 9.63322C8.27676 9.7291 8.33223 9.81663 8.40335 9.89056C8.47447 9.9645 8.55979 10.0233 8.65418 10.0635C8.74858 10.1037 8.85011 10.1244 8.9527 10.1244C9.05529 10.1244 9.15682 10.1037 9.25122 10.0635C9.34562 10.0233 9.43093 9.9645 9.50205 9.89056C9.57318 9.81663 9.62865 9.7291 9.66514 9.63322C9.70163 9.53734 9.71839 9.43508 9.71442 9.33257V7.80914C9.71442 7.20293 9.95523 6.62155 10.3839 6.1929C10.8125 5.76424 11.3939 5.52343 12.0001 5.52343C12.6063 5.52343 13.1877 5.76424 13.6164 6.1929C14.045 6.62155 14.2858 7.20293 14.2858 7.80914V9.33257C14.2935 9.52949 14.3771 9.71579 14.5191 9.85241C14.6611 9.98903 14.8505 10.0653 15.0476 10.0653C15.2446 10.0653 15.434 9.98903 15.576 9.85241C15.7181 9.71579 15.8016 9.52949 15.8093 9.33257V7.80914C15.8093 6.7989 15.408 5.83003 14.6936 5.11567C13.9792 4.40132 13.0104 4 12.0001 4Z"
            fill={color}
            fillOpacity={opacity}
          />
        </g>
      </svg>
    );
  };

// PropTypes definition
LockIcon.propTypes = {
    size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    color: PropTypes.string,
    opacity: PropTypes.number,
    className: PropTypes.string,
    onClick: PropTypes.func,
    onMouseEnter: PropTypes.func,
    onMouseLeave: PropTypes.func,
    style: PropTypes.object,
    'aria-label': PropTypes.string,
    'aria-hidden': PropTypes.bool,
    role: PropTypes.string,
    id: PropTypes.string,
    'data-testid': PropTypes.string,
  };

export default LockIcon;
