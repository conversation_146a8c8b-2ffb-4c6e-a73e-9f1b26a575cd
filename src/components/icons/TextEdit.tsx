import React from 'react';

interface TextEditIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  fillOpacity?: number;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const TextEditIcon: React.FC<TextEditIconProps> = ({
  size = 24,
  width,
  height,
  color = 'white',
  fill,
  fillOpacity = 0.2,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <mask id="mask0_118_2584" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
        <rect width="24" height="24" fill="#D9D9D9"/>
      </mask>
      <g mask="url(#mask0_118_2584)">
        <path
          d="M2 15.1111V12.8889H9.77778V15.1111H2ZM2 10.6667V8.44444H14.2222V10.6667H2ZM2 6.22222V4H14.2222V6.22222H2ZM12 21.7778V18.3611L18.1389 12.25C18.3056 12.0833 18.4907 11.963 18.6944 11.8889C18.8981 11.8148 19.1019 11.7778 19.3056 11.7778C19.5278 11.7778 19.7407 11.8194 19.9444 11.9028C20.1481 11.9861 20.3333 12.1111 20.5 12.2778L21.5278 13.3056C21.6759 13.4722 21.7917 13.6574 21.875 13.8611C21.9583 14.0648 22 14.2685 22 14.4722C22 14.6759 21.963 14.8843 21.8889 15.0972C21.8148 15.3102 21.6944 15.5 21.5278 15.6667L15.4167 21.7778H12ZM19.3056 15.5556L20.3333 14.4722L19.3056 13.4444L18.25 14.5L19.3056 15.5556Z"
          fill={iconFill}
          fillOpacity={fillOpacity}
        />
      </g>
    </svg>
  );
};

export default TextEditIcon;
