import { cn } from "@/lib/utils";
import { useState } from "react";
import { ImagePreviewUrl } from "./ImagePreviewUrl";
import { File, FileText, Image, Video, Archive, Download } from "lucide-react";
// Import all file type icons from assets/files
import PDFIcon from "@/assets/files/pdf.svg";
import CSVIcon from "@/assets/files/csv.svg";
import DOCIcon from "@/assets/files/doc.svg";
import PPTIcon from "@/assets/files/ppt.svg";
import TXTIcon from "@/assets/files/txt.svg";
import VideoIcon from "@/assets/files/video.svg";
import SVGIcon from "@/assets/files/svg.svg";
import OtherIcon from "@/assets/files/other.svg";
import { downloadFileFromUrl } from "@/lib/utils/downloadFile";
import ZipIcon from "@/assets/files/zip.svg";

interface ArtifactData {
  name: string;
  url: string;
  artifact_type?: string;
  mime_type?: string;
  file_size?: number;
  visibility?: 'public' | 'private';
}

interface ArtifactGalleryProps {
  images: ArtifactData[];
  className?: string;
}

// Helper function to check if file type is previewable as image
const isPreviewableImage = (fileName: string, url: string, mimeType?: string) => {
  // Basic validation - need at least fileName or url to determine if it's an image
  if (!fileName && !url && !mimeType) {
    return false;
  }

  // Support all browser-displayable image types
  const imageExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    '.bmp', '.tiff', '.tif', '.ico', '.avif', '.heic', '.heif'
  ];
  const hasImageExtension = imageExtensions.some(ext => {
    const fileNameMatch = fileName ? fileName.toLowerCase().endsWith(ext) : false;
    const urlMatch = url ? url.toLowerCase().includes(ext) : false;
    return fileNameMatch || urlMatch;
  });

  // Check mime type if available - support all image mime types
  if (mimeType && mimeType.startsWith('image/')) {
    return true;
  }

  // Also check if URL contains common image hosting patterns
  const imageHostingPatterns = [
    'image', 'img', 'photo', 'picture', 'pic',
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp',
    '.tiff', '.tif', '.ico', '.avif', '.heic', '.heif'
  ];
  const hasImagePattern = url ? imageHostingPatterns.some(pattern =>
    url.toLowerCase().includes(pattern)
  ) : false;

  return hasImageExtension || hasImagePattern;
};

// Helper function to get file icon from assets/files directory
const getFileIcon = (mimeType?: string, fileName?: string) => {
  if (!fileName && !mimeType) return OtherIcon;

  const lowerFileName = fileName?.toLowerCase() || '';

  // Debug logging to see what values we're getting
  console.log('ImageGalleryUrl getFileIcon - fileName:', fileName, 'mimeType:', mimeType, 'lowerFileName:', lowerFileName);

  // PDF files
  if (lowerFileName.endsWith('.pdf') || mimeType === 'application/pdf') {
    console.log('Detected as PDF');
    return PDFIcon;
  }

  // CSV files
  if (lowerFileName.endsWith('.csv') || mimeType === 'text/csv') {
    console.log('Detected as CSV');
    return CSVIcon;
  }

  // Document files (Word, etc.)
  if (lowerFileName.endsWith('.doc') ||
      lowerFileName.endsWith('.docx') ||
      mimeType === 'application/msword' ||
      mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return DOCIcon;
  }

  if (
    lowerFileName.endsWith('.zip') ||
    lowerFileName.endsWith('.tar') ||
    lowerFileName.endsWith('.rar') ||
    lowerFileName.endsWith('.7z') ||
    mimeType?.includes('zip') ||
    mimeType?.includes('tar') ||
    mimeType?.includes('rar') ||
    mimeType?.includes('7z')
  ) {
    return ZipIcon;
  }

  // Presentation files (PowerPoint, etc.)
  if (lowerFileName.endsWith('.ppt') ||
      lowerFileName.endsWith('.pptx') ||
      mimeType === 'application/vnd.ms-powerpoint' ||
      mimeType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
    return PPTIcon;
  }

  // Text files
  if (lowerFileName.endsWith('.txt') ||
      lowerFileName.endsWith('.md') ||
      lowerFileName.endsWith('.log') ||
      mimeType === 'text/plain' ||
      mimeType === 'text/markdown') {
    return TXTIcon;
  }

  // Video files
  if (lowerFileName.endsWith('.mp4') ||
      lowerFileName.endsWith('.avi') ||
      lowerFileName.endsWith('.mov') ||
      lowerFileName.endsWith('.wmv') ||
      lowerFileName.endsWith('.mkv') ||
      mimeType?.startsWith('video/')) {
    return VideoIcon;
  }

  // SVG files (as documents, not images)
  if (lowerFileName.endsWith('.svg') || mimeType === 'image/svg+xml') {
    return SVGIcon;
  }

  // Excel files
  if (lowerFileName.endsWith('.xls') ||
      lowerFileName.endsWith('.xlsx') ||
      mimeType === 'application/vnd.ms-excel' ||
      mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    return CSVIcon; // Use CSV icon for Excel files
  }

  // Default for unknown file types
  return OtherIcon;
};

// Helper function to get Lucide file icon based on mime type or extension
const getLucideFileIcon = (mimeType?: string, fileName?: string) => {
  if (mimeType?.startsWith('image/')) return Image;
  if (mimeType?.startsWith('video/')) return Video;
  if (mimeType?.startsWith('text/') || fileName?.endsWith('.txt')) return FileText;
  if (mimeType?.includes('zip') || mimeType?.includes('tar') || mimeType?.includes('rar')) return Archive;
  if (fileName?.endsWith('.pdf')) return FileText;
  if (fileName?.endsWith('.csv') || fileName?.endsWith('.xlsx')) return FileText;
  return File;
};

// Helper function to format file size
const formatFileSize = (bytes?: number) => {
  if (!bytes) return 'Unknown size';
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to truncate filename
const truncateFileName = (fileName: string, maxLength: number = 15) => {
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

  if (nameWithoutExt.length <= 10) return fileName;

  return `${nameWithoutExt.substring(0, 15)}...${extension}`;
};

/**
 * A reusable component for displaying a gallery of artifacts (images and files) from URLs
 * Used for displaying artifacts in message items
 */
function ArtifactGalleryComponent({ images, className }: ArtifactGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<ArtifactData | null>(null);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  if (!images || images.length === 0) return null;

  const handleDownload = async (artifact: ArtifactData, e: React.MouseEvent) => {
    e.stopPropagation();

    // Don't allow download for private artifacts without URLs
    if (artifact.visibility === 'private' && !artifact.url) {
      console.log('Cannot download private artifact without URL');
      return;
    }

    await downloadFileFromUrl(artifact.url, artifact.name || 'download');
  };

  const handleArtifactClick = (artifact: ArtifactData) => {
    // Don't allow interaction with private artifacts without URLs
    if (artifact.visibility === 'private' && !artifact.url) {
      console.log('Cannot interact with private artifact without URL');
      return;
    }

    if (isPreviewableImage(artifact.name, artifact.url, artifact.mime_type)) {
      setSelectedImage(artifact);
    } else {
      // For non-image files, show a message that preview is not supported
      // You could implement a custom modal here or just download the file
      console.log('Preview not supported for this file type');
    }
  };

  return (
    <>
      <div
        className={cn(
          "flex flex-wrap max-w-4xl overflow-x-auto gap-[10px] relative my-2 mb-0 rounded-lg w-full",
          className
        )}
      >
        {images.map((artifact, index) => {
          const isImage = isPreviewableImage(artifact.name, artifact.url, artifact.mime_type);
          const customIcon = getFileIcon(artifact.mime_type, artifact.name);
          const LucideIcon = getLucideFileIcon(artifact.mime_type, artifact.name);
          const isPrivateWithoutUrl = artifact.visibility === 'private' && !artifact.url;

          return (
            <div
              key={index}
              className={`relative rounded-[12px] p-[6px] min-h-[180px] max-h-[180px] min-w-[180px] max-w-[180px] overflow-hidden bg-[#FFFFFF0F] flex flex-col items-center justify-center transition-colors ${
                isPrivateWithoutUrl
                  ? ''
                  : 'cursor-pointer hover:bg-[#FFFFFF15]'
              }`}
              onClick={() => handleArtifactClick(artifact)}
              onMouseEnter={() => !isPrivateWithoutUrl && setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              {isImage && artifact.url ? (
                <img
                  src={artifact.url}
                  alt={artifact.name || `Image ${index + 1}`}
                  className="object-cover w-full h-full rounded-lg"
                  onError={(e) => {
                    // Fallback to file icon if image fails to load
                    const target = e.target as HTMLImageElement;
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full p-4">
                          <img src="${customIcon}" alt="File" class="w-12 h-12 mb-2" />
                          <span class="text-[12px] text-white/70 text-center break-words">${truncateFileName(artifact.name)}</span>
                          <span class="text-[10px] text-white/50 mt-1">${formatFileSize(artifact.file_size)}</span>
                        </div>
                      `;
                    }
                  }}
                />
              ) : (
                <div className="flex flex-col items-start justify-between w-full h-full p-3 bg-[#FFFFFF05] rounded-[8px]">
                  <div className="flex items-center w-full " >
                    {customIcon ? (
                    <img src={customIcon} alt="File" className={cn("w-12 h-12 mb-2", customIcon === ZipIcon && "p-1")} />
                  ) : (
                    <LucideIcon className="w-12 h-12 mb-2 text-[#FFFFFF60]" />
                  )}
                  </div>
                  <div className="flex flex-col items-start justify-center">
                    <span className="text-[12px] text-white/70 text-nowrap text-center break-words mb-1">
                    {truncateFileName(artifact.name)}
                  </span>
                  {(artifact.file_size || 0) > 0 && <span className="text-[10px] text-white/50">
                    {formatFileSize(artifact.file_size)}
                  </span>}
                  </div>
                </div>
              )}

              {/* Download button - appears on hover, disabled for private artifacts */}
              <button
                type="button"
                onClick={(e) => handleDownload(artifact, e)}
                disabled={isPrivateWithoutUrl}
                className={`absolute top-2 right-2 p-1.5 bg-black/50 rounded-lg transition-opacity ${
                  isPrivateWithoutUrl
                    ? 'opacity-0'
                    : `hover:bg-black/70 ${hoveredIndex === index ? 'opacity-100' : 'opacity-0'}`
                }`}
                title={isPrivateWithoutUrl ? "Private file - Download not available" : "Download file"}
              >
                <Download className="w-4 h-4 text-white" />
              </button>
            </div>
          );
        })}
      </div>

      {/* Image preview modal - only for images */}
      {selectedImage && isPreviewableImage(selectedImage.name, selectedImage.url, selectedImage.mime_type) && (
        <ImagePreviewUrl
          imageUrl={selectedImage.url}
          imageName={selectedImage.name}
          isOpen={!!selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </>
  );
}

// Export with both names for backward compatibility
export { ArtifactGalleryComponent as ImageGalleryUrl };
export { ArtifactGalleryComponent as ArtifactGallery };
export default ArtifactGalleryComponent;
