import { cn } from "@/lib/utils";

const Divider = ({text}:{
  text?: string;
}) => {
  return (
    <div className="relative flex items-center justify-center w-full my-4 md:my-5">
      <div className="w-full border border-t-[0.5px] border-b-0 border-white/20"></div>
      <span className={
        cn("px-3 text-[14px]  md:text-[14px] font-['Inter'] text-white/30 bg-none whitespace-nowrap", text ? "":"uppercase")
      }>{text ? text:"or"}</span>
      <div className="w-full border border-t-[0.5px] border-b-0 border-white/20"></div>
      
    </div>
  );
};

export default Divider;