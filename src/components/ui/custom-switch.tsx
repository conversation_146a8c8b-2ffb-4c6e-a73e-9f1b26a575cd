"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"
import { cn } from "@/lib/utils"

interface CustomSwitchProps extends React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> {
  className?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
}

const CustomSwitch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  CustomSwitchProps
>(({ className, checked, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer relative inline-flex h-7 w-12 shrink-0 cursor-pointer items-center rounded-full transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed",
      "bg-[#131314]", // Dark background from SVG
      "data-[state=unchecked]:opacity-50", // Disabled state opacity
      className
    )}
    checked={checked}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none relative h-5 w-5 rounded-full transition-all duration-200 ease-in-out flex items-center justify-center",
        "data-[state=checked]:translate-x-6 data-[state=unchecked]:translate-x-1",
        // Base styles for the thumb - enabled state
        "data-[state=checked]:bg-white data-[state=checked]:shadow-lg",
        // Disabled state - semi-transparent white with custom shadow
        "data-[state=unchecked]:bg-white/10",
        // Custom shadow effects for checked state (active eye)
        "data-[state=checked]:shadow-[0px_0.71px_1.77px_0px_rgba(0,0,0,0.36),0px_3px_3px_0px_rgba(0,0,0,0.31),0px_6.89px_4.24px_0px_rgba(0,0,0,0.18),0px_12.36px_4.94px_0px_rgba(0,0,0,0.05),0px_19.25px_5.47px_0px_rgba(0,0,0,0.01)]",
        // Custom shadow for unchecked state
        "data-[state=unchecked]:shadow-[0px_0.71px_1.77px_0px_rgba(0,0,0,0.36),0px_3px_3px_0px_rgba(0,0,0,0.31),0px_6.89px_4.24px_0px_rgba(0,0,0,0.18),0px_12.36px_4.94px_0px_rgba(0,0,0,0.05),0px_19.25px_5.47px_0px_rgba(0,0,0,0.01)]",
        // Inner shadow effects for checked state (active eye)
        "data-[state=checked]:before:absolute data-[state=checked]:before:inset-0 data-[state=checked]:before:rounded-full data-[state=checked]:before:shadow-[inset_0px_1.24px_2.47px_0px_rgba(255,255,255,0.03),inset_0px_1.06px_0.71px_0px_rgba(255,255,255,0.02),inset_0px_0.18px_0.18px_0px_rgba(255,255,255,0.1)]",
        // Inner shadow effects for unchecked state
        "data-[state=unchecked]:before:absolute data-[state=unchecked]:before:inset-0 data-[state=unchecked]:before:rounded-full data-[state=unchecked]:before:shadow-[inset_0px_1.24px_2.47px_0px_rgba(255,255,255,0.03),inset_0px_1.06px_0.71px_0px_rgba(255,255,255,0.02),inset_0px_0.18px_0.18px_0px_rgba(255,255,255,0.1)]"
      )}
    >
      {/* Eye Icon for checked state - show only when checked */}
      {checked && (
        <svg
          className="absolute inset-0 z-10 w-3 h-3 m-auto transition-opacity duration-200"
          width="12"
          height="12"
          viewBox="0 0 14.3 12"
          fill="none"
        >
          <path
            d="M7.15273 0.867188C8.67391 0.924388 10.145 1.42639 11.3842 2.31055C12.6232 3.19471 13.5765 4.42207 14.1254 5.84184C14.1624 5.94424 14.1623 6.05674 14.1254 6.15924C13.5765 7.57901 12.6233 8.80721 11.3842 9.69141C10.145 10.5756 8.67391 11.0776 7.15273 11.1348C5.63156 11.0776 4.16043 10.5756 2.92129 9.69141C1.68215 8.80721 0.729004 7.57901 0.180078 6.15924C0.143078 6.05674 0.143078 5.94424 0.180078 5.84184C0.729004 4.42207 1.68227 3.19471 2.92129 2.31055C4.16043 1.42639 5.63156 0.924388 7.15273 0.867188ZM8.31387 3.19824C7.75957 2.96874 7.14941 2.90844 6.56094 3.02544C5.97266 3.14244 5.43242 3.43144 5.00824 3.85554C4.58406 4.27974 4.29414 4.82074 4.17715 5.40924C4.06016 5.99754 4.12035 6.60794 4.34996 7.16214C4.57957 7.71634 4.96836 8.19024 5.46719 8.52344C5.96602 8.85674 6.55273 9.03424 7.15273 9.03424C7.95684 9.03294 8.72773 8.71314 9.29629 8.14454C9.86484 7.57604 10.1847 6.80514 10.1859 6.00104C10.1859 5.40104 10.0084 4.81434 9.67524 4.31544C9.34191 3.81664 8.86816 3.42794 8.31387 3.19824ZM7.14785 4.125C8.17879 4.125 9.01504 4.96124 9.01504 5.99224C9.01484 7.02304 8.17871 7.85844 7.14785 7.85844C6.11719 7.85824 5.28184 7.02284 5.28164 5.99224C5.28164 4.96134 6.11707 4.12524 7.14785 4.125Z"
            fill="#0F0F10"
          />
        </svg>
      )}

      {/* Crossed Eye Icon for unchecked state - show only when not checked */}
      {!checked && (
        <svg
          className="absolute inset-0 z-10 w-3 h-3 m-auto transition-opacity duration-200 opacity-60"
          width="12"
          height="12"
          viewBox="0 0 12 11"
          fill="none"
        >
          <path
            d="M1.85127 2.51855L0.117676 0.784211L0.901758 0L11.8824 10.9801L11.0982 11.7648L9.26245 9.92908C8.28691 10.5479 7.15498 10.8755 5.99971 10.8736C3.00928 10.8736 0.521289 8.72168 0 5.88208C0.239014 4.59092 0.888184 3.41123 1.85127 2.51855ZM7.52928 8.19604L6.71738 7.38404C6.40684 7.53264 6.05781 7.58124 5.71855 7.52314C5.37928 7.46504 5.06621 7.30314 4.82275 7.05964C4.57928 6.81624 4.41738 6.50324 4.35928 6.16394C4.30117 5.82464 4.34977 5.47564 4.49844 5.16504L3.68652 4.35314C3.33359 4.88634 3.17578 5.52524 3.23984 6.16144C3.30391 6.79764 3.58594 7.39214 4.03809 7.84434C4.49023 8.29644 5.08477 8.57844 5.72098 8.64254C6.35723 8.70664 6.99609 8.54884 7.52928 8.19604ZM3.76689 1.31224C4.45854 1.04044 5.21221 0.890703 5.99971 0.890703C8.99014 0.890703 11.4781 3.04264 12 5.88208C11.8301 6.80614 11.4481 7.67814 10.8841 8.42954L8.74336 6.28874C8.76299 6.15564 8.77271 6.02014 8.77271 5.88208C8.77275 5.48364 8.68701 5.08984 8.52109 4.72744C8.35518 4.36514 8.11318 4.04274 7.81152 3.78244C7.50986 3.52204 7.15557 3.32974 6.77275 3.21864C6.39014 3.10754 5.98799 3.08024 5.59385 3.13854L3.76689 1.31224Z"
            fill="white"
            fillOpacity="0.6"
          />
        </svg>
      )}
    </SwitchPrimitives.Thumb>
  </SwitchPrimitives.Root>
))

CustomSwitch.displayName = "CustomSwitch"

export { CustomSwitch }
