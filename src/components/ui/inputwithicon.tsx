import React, { ChangeEvent, useState } from 'react';

interface InputWithIconProps {
  id: string;
  name: string;
  type: string;
  value: string;
  placeholder?: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  disabled?: boolean;
  iconSrc?: string;
  iconAlt?: string;
  className?: string;
}

const InputWithIcon: React.FC<InputWithIconProps> = ({
  id,
  name,
  type,
  value,
  placeholder,
  onChange,
  required = false,
  disabled = false,
  iconSrc,
  iconAlt = 'Input icon',
  className = '',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = value.length > 0;
  
  return (
    <div className="relative flex items-center w-full">
      {iconSrc && (
        <div className={`absolute left-3 transition-opacity duration-200 ${
          isFocused ? 'opacity-100' : hasValue ? 'opacity-50' : 'opacity-40'
        }`}>
          <img 
            src={iconSrc}
            alt={iconAlt}
            className="w-5 h-5"
          />
        </div>
      )}
      <input
        id={id}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`w-full h-12 p-4 bg-transparent border border-[#ffffff]/10 
                 rounded-[8px] text-white placeholder-[#4d4d4d] focus:outline-none focus:border-[#ffffff]
                 transition-all duration-200 text-[14px] ${disabled ? 'opacity-70' : ''} font-inter
                 ${iconSrc ? 'pl-10' : 'pl-4'} ${className} ${
                  hasValue && !isFocused ? 'text-opacity-50 text-white' : 'text-opacity-100'
                }`}
      />
    </div>
  );
};

export default InputWithIcon;