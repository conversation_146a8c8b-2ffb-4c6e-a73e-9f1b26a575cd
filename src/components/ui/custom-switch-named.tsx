import React from 'react';
import { motion } from 'framer-motion';

interface CustomSwitchNamedProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  checkedColor?: string;
  uncheckedColor?: string;
  title?: string;
}

export const CustomSwitchNamed: React.FC<CustomSwitchNamedProps> = ({
  checked,
  onCheckedChange,
  disabled = false,
  className = '',
  checkedColor = '#29BCCC',
  uncheckedColor = '#131314',
  title = 'Toggle Switch',
}) => {
  return (
    <button
      title={title}
      type="button"
      role="switch"
      aria-checked={checked ? "true" : "false"}
      disabled={disabled}
      onClick={() => !disabled && onCheckedChange(!checked)}
      className={`relative inline-flex h-8 w-14 items-center rounded-full transition-colors focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      style={{
        backgroundColor: checked ? checkedColor : uncheckedColor,
      }}
    >
      <motion.div
        className="relative"
        animate={{
          x: checked ? 28 : 4,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 30,
        }}
      >
        {/* Switch thumb with complex shadow and gradient effects */}
        <div className="relative w-6 h-6">
          {/* Background circle with white opacity */}
          <div 
            className="absolute inset-0 rounded-full"
            style={{
              background: 'rgba(255, 255, 255, 0.05)',
            }}
          />
          
          {/* Main circle with gradients and shadows */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: `
                linear-gradient(180deg, #242528 0%, #111113 100%),
                linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%)
              `,
              filter: `
                drop-shadow(0px 0.85px 2.12px rgba(0, 0, 0, 0.36))
                drop-shadow(0px 3.6px 3.6px rgba(0, 0, 0, 0.31))
                drop-shadow(0px 8.27px 5.09px rgba(0, 0, 0, 0.18))
                drop-shadow(0px 14.84px 5.93px rgba(0, 0, 0, 0.05))
                drop-shadow(0px 23.1px 6.57px rgba(0, 0, 0, 0.01))
              `,
            }}
          />
          
          {/* Inner shadows and highlights */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: `
                radial-gradient(circle at center, 
                  rgba(255, 255, 255, 0.1) 0%, 
                  rgba(255, 255, 255, 0.03) 50%, 
                  transparent 100%
                )
              `,
              boxShadow: `
                inset 0px 1.48px 2.97px rgba(255, 255, 255, 0.03),
                inset 0px 1.27px 0.85px rgba(255, 255, 255, 0.02),
                inset 0px 0.21px 0.21px rgba(255, 255, 255, 0.1)
              `,
            }}
          />
        </div>
      </motion.div>
    </button>
  );
};
