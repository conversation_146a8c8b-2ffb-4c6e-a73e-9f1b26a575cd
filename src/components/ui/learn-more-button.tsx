import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import BookSVG from "@/assets/pro/book.svg";
import ExternalSVG from "@/assets/pro/external.svg";

interface LearnMoreButtonProps {
  /**
   * The text to display in the button
   */
  text: string;
  /**
   * Click handler for the button
   */
  onClick?: () => void;
  /**
   * Additional CSS classes to apply to the button
   */
  className?: string;
  /**
   * Whether the button is disabled
   */
  disabled?: boolean;
  /**
   * Custom book icon (optional, defaults to BookSVG)
   */
  bookIcon?: string;
  /**
   * Custom external icon (optional, defaults to ExternalSVG)
   */
  externalIcon?: string;
  /**
   * Size variant of the button
   */
  size?: "sm" | "md" | "lg";
  /**
   * Whether to show the external icon
   */
  showExternalIcon?: boolean;
}

const sizeClasses = {
  sm: "px-3 py-1.5 text-xs gap-1.5",
  md: "px-4 py-2 text-sm gap-2", 
  lg: "px-6 py-3 text-base gap-3"
};

const iconSizes = {
  sm: "w-4 h-4",
  md: "w-5 h-5",
  lg: "w-6 h-6"
};

export function LearnMoreButton({
  text,
  onClick,
  className,
  disabled = false,
  bookIcon = BookSVG,
  externalIcon = ExternalSVG,
  size = "md",
  showExternalIcon = true,
  ...props
}: LearnMoreButtonProps & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "flex items-center border-[#2B2B2E] border bg-[#1E1E1F] hover:bg-[#1E1E1F90] rounded-[8px] transition-colors text-white font-medium",
        sizeClasses[size],
        disabled && "opacity-50 cursor-not-allowed hover:bg-[#1E1E1F]",
        className
      )}
      whileHover={disabled ? {} : { scale: 1.02 }}
      whileTap={disabled ? {} : { scale: 0.98 }}
      transition={{ duration: 0.15 }}
      {...props}
    >
      <img
        src={bookIcon}
        alt="Book"
        className={cn("inline-block", iconSizes[size])}
      />
      <span>{text}</span>
      {showExternalIcon && (
        <img
          src={externalIcon}
          alt="External"
          className={cn("inline-block", iconSizes[size])}
        />
      )}
    </motion.button>
  );
}
