import { PendingArtifact } from "@/types/artifact";
import { File, FileText, Image, Video, Archive } from "lucide-react";
// Import all file type icons from assets/files
import PDFIcon from "@/assets/files/pdf.svg";
import CSVIcon from "@/assets/files/csv.svg";
import DOCIcon from "@/assets/files/doc.svg";
import PPTIcon from "@/assets/files/ppt.svg";
import TXTIcon from "@/assets/files/txt.svg";
import VideoIcon from "@/assets/files/video.svg";
import SVGIcon from "@/assets/files/svg.svg";
import OtherIcon from "@/assets/files/other.svg";
import ZipIcon from "@/assets/files/zip.svg";
import { cn } from "@/lib/utils";

// Helper function to check if file type is previewable as image
const isPreviewableImage = (fileName: string, mimeType?: string) => {
  // Only allow the same image types that are supported for base64 conversion
  const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];

  // Check mime type first if available
  if (mimeType && allowedTypes.includes(mimeType)) {
    return true;
  }

  // Fallback to file extension check for supported types only
  const allowedExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
  return allowedExtensions.some((ext) => fileName.toLowerCase().endsWith(ext));
};

// Helper function to check if file is an SVG
const isPreviewableSVG = (fileName: string, mimeType?: string) => {
  return (
    mimeType === "image/svg+xml" || fileName.toLowerCase().endsWith(".svg")
  );
};

// Helper function to format file size
const formatFileSize = (bytes?: number): string => {
  if (!bytes) return "";
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// Helper function to truncate filename while preserving extension
const truncateFileName = (fileName: string, maxLength: number = 20): string => {
  if (fileName.length <= maxLength) return fileName;

  const lastDotIndex = fileName.lastIndexOf(".");
  if (lastDotIndex === -1) {
    // No extension, just truncate
    return fileName.substring(0, maxLength - 3) + "...";
  }

  const extension = fileName.substring(lastDotIndex);
  const nameWithoutExt = fileName.substring(0, lastDotIndex);

  // Calculate available space for the name part (accounting for ... and extension)
  const availableSpace = maxLength - extension.length - 3; // 3 for "..."

  if (availableSpace <= 0) {
    // Extension is too long, just show extension
    return "..." + extension;
  }

  return nameWithoutExt.substring(0, availableSpace) + "..." + extension;
};

// Helper function to get file icon from assets/files directory
const getFileIcon = (fileName: string, mimeType?: string) => {
  if (!fileName && !mimeType) return OtherIcon;

  const lowerFileName = fileName?.toLowerCase() || "";

  // Debug logging to see what values we're getting
  console.log(
    "getFileIcon - fileName:",
    fileName,
    "mimeType:",
    mimeType,
    "lowerFileName:",
    lowerFileName
  );

  // PDF files
  if (lowerFileName.endsWith(".pdf") || mimeType === "application/pdf") {
    console.log("Detected as PDF");
    return PDFIcon;
  }

  // CSV files
  if (lowerFileName.endsWith(".csv") || mimeType === "text/csv") {
    console.log("Detected as CSV, returning CSVIcon:", CSVIcon);
    return CSVIcon;
  }

  // Document files (Word, etc.)
  if (
    lowerFileName.endsWith(".doc") ||
    lowerFileName.endsWith(".docx") ||
    mimeType === "application/msword" ||
    mimeType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  ) {
    return DOCIcon;
  }

  // Presentation files (PowerPoint, etc.)
  if (
    lowerFileName.endsWith(".ppt") ||
    lowerFileName.endsWith(".pptx") ||
    mimeType === "application/vnd.ms-powerpoint" ||
    mimeType ===
      "application/vnd.openxmlformats-officedocument.presentationml.presentation"
  ) {
    return PPTIcon;
  }

  // Text files
  if (
    lowerFileName.endsWith(".txt") ||
    lowerFileName.endsWith(".md") ||
    lowerFileName.endsWith(".log") ||
    mimeType === "text/plain" ||
    mimeType === "text/markdown"
  ) {
    return TXTIcon;
  }

    if (
    lowerFileName.endsWith(".zip") ||
    lowerFileName.endsWith(".tar") ||
    lowerFileName.endsWith(".rar") ||
    lowerFileName.endsWith(".7z")
  )
    return ZipIcon;

  // Video files
  if (
    lowerFileName.endsWith(".mp4") ||
    lowerFileName.endsWith(".avi") ||
    lowerFileName.endsWith(".mov") ||
    lowerFileName.endsWith(".wmv") ||
    lowerFileName.endsWith(".mkv") ||
    mimeType?.startsWith("video/")
  ) {
    return VideoIcon;
  }

  // SVG files (as documents, not images)
  if (lowerFileName.endsWith(".svg") || mimeType === "image/svg+xml") {
    return SVGIcon;
  }

  // Excel files
  if (
    lowerFileName.endsWith(".xls") ||
    lowerFileName.endsWith(".xlsx") ||
    mimeType === "application/vnd.ms-excel" ||
    mimeType ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ) {
    return CSVIcon; // Use CSV icon for Excel files
  }

  // Default for unknown file types
  return OtherIcon;
};

function ChatPreview({ artifact }: { artifact: PendingArtifact }) {
  const isImage = isPreviewableImage(artifact.file_name, artifact.mime_type);
  const isSVG = isPreviewableSVG(artifact.file_name, artifact.mime_type);
  const customIcon = getFileIcon(artifact.file_name, artifact.mime_type);
  const isZip = customIcon === ZipIcon;

  const imageUrl = artifact.local_preview_url || artifact.file_path;

  // Handle regular images
  if (isImage) {
    return (
      <div className="flex items-center gap-2 min-h-[44px] max-h-[44px] max-w-[44px] min-w-[44px]  rounded-[8px] overflow-clip">
        <img
          src={imageUrl}
          alt={artifact.file_name}
          className="flex h-[44px] object-cover w-[44px] items-center justify-center"
        />
      </div>
    );
  }

  // Handle SVG files - show the actual SVG content
  if (isSVG && imageUrl) {
    return (
      <div className="flex items-center gap-2 min-h-[44px] max-h-[44px] max-w-[44px] min-w-[44px] rounded-[8px] overflow-clip">
        <img
          src={imageUrl}
          alt={artifact.file_name}
          className="flex h-[44px] object-contain w-[44px] items-center justify-center"
          onError={(e) => {
            // Fallback to SVG icon if the SVG fails to load
            const target = e.target as HTMLImageElement;
            target.src = SVGIcon;
            target.className = "w-8 h-8";
          }}
        />
      </div>
    );
  }

  return (
    <div
      key={artifact.artifact_id}
      className="flex items-center gap-2 min-h-[44px] max-h-[44px] min-w-[100px] p-1 pr-4 bg-[#FFFFFF0D] rounded-[8px]"
    >
      <div className="flex items-center flex-1 h-full min-w-0 gap-2">
        {/* File Icon/Preview */}
        <img src={customIcon} alt="File" className={cn("w-8 h-8",isZip && "p-1" )} />

        {/* File Info */}
        <div className="flex-1 min-w-0">
          <div className="text-[14px] font-medium font-['Inter'] text-[#FFFFFFCC] truncate text-nowrap">
            {truncateFileName(artifact.file_name, 20)}
          </div>
          {artifact.file_size && (
            <div className="text-[12px] font-['Inter'] font-medium text-white/40 mt-0.5">
              {formatFileSize(artifact.file_size)}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ChatPreview;
