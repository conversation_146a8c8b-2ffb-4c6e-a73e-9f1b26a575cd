
import { useState, useRef, useEffect } from "react"
import { createPortal } from "react-dom"
import MoreIcon from "@/assets/upload/more_icon.svg"
import DeleteIcon from "@/assets/upload/delete.svg"
import { Image, Video, FileText, Archive, File } from "lucide-react"
// Import all file type icons from assets/files
import PDFIcon from "@/assets/files/pdf.svg";
import CSVIcon from "@/assets/files/csv.svg";
import DOCIcon from "@/assets/files/doc.svg";
import PPTIcon from "@/assets/files/ppt.svg";
import TXTIcon from "@/assets/files/txt.svg";
import VideoIcon from "@/assets/files/video.svg";
import ZipIcon from "@/assets/files/zip.svg";
import OtherIcon from "@/assets/files/other.svg";
import SVGIcon from "@/assets/files/svg.svg";
import { ImagePreviewUrl } from "../ImagePreviewUrl"
import DownloadWhiteIcon from "@/assets/upload/download_white.svg"
import DeleteRed from "@/assets/upload/deleteActive.svg"

// Helper function to get custom file icon from assets/files directory
const getCustomFileIcon = (mimeType: string, fileName: string) => {
  const lowerFileName = fileName?.toLowerCase() || '';

  // PDF files
  if (lowerFileName.endsWith('.pdf') || mimeType === 'application/pdf') {
    return PDFIcon;
  }

  // CSV files
  if (lowerFileName.endsWith('.csv') || mimeType === 'text/csv') {
    return CSVIcon;
  }

  if (
    lowerFileName.endsWith('.zip') ||
    lowerFileName.endsWith('.tar') ||
    lowerFileName.endsWith('.rar') ||
    lowerFileName.endsWith('.7z') ||
    mimeType?.includes('zip') ||
    mimeType?.includes('tar') ||
    mimeType?.includes('rar') ||
    mimeType?.includes('7z')
  ) {
    return ZipIcon;
  }

  // Document files (Word, etc.)
  if (lowerFileName.endsWith('.doc') ||
      lowerFileName.endsWith('.docx') ||
      mimeType === 'application/msword' ||
      mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return DOCIcon;
  }

  // Presentation files (PowerPoint, etc.)
  if (lowerFileName.endsWith('.ppt') ||
      lowerFileName.endsWith('.pptx') ||
      mimeType === 'application/vnd.ms-powerpoint' ||
      mimeType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
    return PPTIcon;
  }

  // Text files
  if (lowerFileName.endsWith('.txt') ||
      lowerFileName.endsWith('.md') ||
      lowerFileName.endsWith('.log') ||
      mimeType === 'text/plain' ||
      mimeType === 'text/markdown') {
    return TXTIcon;
  }

  // Video files
  if (lowerFileName.endsWith('.mp4') ||
      lowerFileName.endsWith('.avi') ||
      lowerFileName.endsWith('.mov') ||
      lowerFileName.endsWith('.wmv') ||
      lowerFileName.endsWith('.mkv') ||
      mimeType?.startsWith('video/')) {
    return VideoIcon;
  }

  // SVG files (as documents, not images)
  if (lowerFileName.endsWith('.svg') || mimeType === 'image/svg+xml') {
    return SVGIcon;
  }

  // Excel files
  if (lowerFileName.endsWith('.xls') ||
      lowerFileName.endsWith('.xlsx') ||
      mimeType === 'application/vnd.ms-excel' ||
      mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    return CSVIcon; // Use CSV icon for Excel files
  }

  // Return null to use Lucide icons as fallback
  return OtherIcon;
};

// Helper function to get Lucide file icon based on mime type or extension
const getLucideFileIcon = (mimeType: string, fileName: string) => {
  if (mimeType.startsWith('image/')) return Image;
  if (mimeType.startsWith('video/')) return Video;
  if (mimeType.startsWith('text/') || fileName.endsWith('.txt')) return FileText;
  if (mimeType.includes('zip') || mimeType.includes('tar') || mimeType.includes('rar')) return Archive;
  if (fileName.endsWith('.pdf')) return FileText;
  if (fileName.endsWith('.csv') || fileName.endsWith('.xlsx')) return FileText;
  return File;
};

// Helper function to check if file type is previewable as image
const isPreviewableImage = (mimeType: string) => {
  return mimeType.startsWith('image');
};

// Helper function to truncate filename to 15 characters + extension
const truncateFilename = (filename: string, maxLength: number = 15) => {
  const lastDotIndex = filename.lastIndexOf('.');

  if (lastDotIndex === -1) {
    // No extension found, just truncate the filename
    return filename.length > maxLength ? filename.substring(0, maxLength) + '...' : filename;
  }

  const name = filename.substring(0, lastDotIndex);
  const extension = filename.substring(lastDotIndex);

  if (name.length <= maxLength) {
    return filename; // No truncation needed
  }

  return name.substring(0, maxLength) + '...' + extension;
};

interface IAssetCardProps {
    title: string;
    size: string;
    id: string;
    type: string;
    url: string;
    visibility?: 'public' | 'private';
    onRemove?: (id: string) => void;
    onDownload?: (id: string) => void;
}

function AssetCard({
    title,
    size,
    id,
    type,
    url,
    visibility = 'public',
    onRemove,
    onDownload,
}: IAssetCardProps) {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false)
    const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false)
    const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 })
    const dropdownRef = useRef<HTMLDivElement>(null)
    const moreButtonRef = useRef<HTMLDivElement>(null)

    const isZip = getCustomFileIcon(type, title) === ZipIcon;

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false)
            }
        }

        if (isDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside)
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [isDropdownOpen])

    const handleMoreClick = (e: React.MouseEvent) => {
        e.stopPropagation()

        if (!isDropdownOpen && moreButtonRef.current) {
            const rect = moreButtonRef.current.getBoundingClientRect()
            setDropdownPosition({
                top: rect.bottom + 4,
                right: window.innerWidth - rect.right
            })
        }

        setIsDropdownOpen(!isDropdownOpen)
    }

    const handleRemove = (e: React.MouseEvent) => {
        e.stopPropagation()
        onRemove?.(id)
        setIsDropdownOpen(false)
    }

    const handleDownload = (e: React.MouseEvent) => {
        e.stopPropagation()
        // Don't allow download for private artifacts without URLs
        if (visibility === 'private' && !url) {
            return;
        }
        onDownload?.(id)
        setIsDropdownOpen(false)
    }

    const handleCardClick = (e: React.MouseEvent) => {
        // Don't trigger preview if clicking on the more options area
        if (dropdownRef.current && dropdownRef.current.contains(e.target as Node)) {
            return;
        }

        // Don't allow interaction with private artifacts without URLs
        if (visibility === 'private' && !url) {
            return;
        }

        // For images, show preview
        if (showImagePreview) {
            setIsImagePreviewOpen(true)
        } else {
            // For non-image files, trigger download
            onDownload?.(id)
        }
    }

    // Get the appropriate icon component
    const customIcon = getCustomFileIcon(type, title);
    const LucideIcon = getLucideFileIcon(type, title);
    const showImagePreview = isPreviewableImage(type) && url;
    const isPrivateWithoutUrl = visibility === 'private' && !url;

    return (
        <>
            <div
                className={`flex items-center justify-between gap-4 group rounded-[12px] p-2 ${
                    isPrivateWithoutUrl
                        ? 'opacity-60'
                        : 'hover:bg-[#FFFFFF0A] cursor-pointer'
                }`}
                onClick={handleCardClick}
            >
                <div
                    className={`min-w-[40px] min-h-[40px] max-h-[40px] max-w-[40px] backdrop-blur-sm rounded-[8px] flex items-center justify-center overflow-hidden ${showImagePreview ? 'hover:bg-[#FFFFFF15] transition-colors' : ''}`}
                >
                    {showImagePreview && !isPrivateWithoutUrl ? (
                        <img
                            src={url}
                            alt={title}
                            className="w-full h-full object-cover rounded-[6px]"
                            onError={(e) => {
                                // Fallback to icon if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                            }}
                        />
                    ) : null}
                    {customIcon ? (
                        <img
                            src={customIcon}
                            alt="File"
                            className={`w-10 h-10 ${showImagePreview ? 'hidden' : ''} ${isZip && "p-1"}`}
                        />
                    ) : (
                        <LucideIcon
                            className={`w-5 h-5 text-[#FFFFFF60] ${showImagePreview ? 'hidden' : ''}`}
                        />
                    )}
                </div>
            <div className="flex flex-col flex-1">
                <span className="text-[#FFFFFF] font-['Inter'] font-medium max-w-[60%] text-[16px] opacity-60 transition-all duration-100 ease-in-out">{truncateFilename(title)}</span>
                <div className="flex flex-col">
                    <span className="text-[#FFFFFF40] font-['Inter'] font-medium text-[12px] ">{size}</span>
                </div>
            </div>
            <div className="relative">
                <div
                    ref={moreButtonRef}
                    className={`hover:bg-[#FFFFFF0F] backdrop-blur-lg rounded-[8px] cursor-pointer p-1 transition-opacity duration-200 ${
                        isDropdownOpen
                            ? 'opacity-100'
                            : 'opacity-0 group-hover:opacity-60 hover:!opacity-100'
                    }`}
                    onClick={handleMoreClick}
                >
                    <img src={MoreIcon} alt="More" className='w-6 h-6' />
                </div>
            </div>
        </div>

        {isDropdownOpen && createPortal(
            <div
                ref={dropdownRef}
                className="fixed z-[9999] p-[6px] bg-[#18181A] backdrop-blur-sm rounded-[12px] border border-[#242424] shadow-lg min-w-[140px]"
                style={{
                    top: dropdownPosition.top,
                    right: dropdownPosition.right
                }}
            >
                <button
                    type="button"
                    onClick={handleRemove}
                    className="flex items-center gap-2 rounded-[8px] w-full p-2 text-left text-[#808080] hover:text-[#CCCCCC] hover:bg-[#ED5B5B0F] transition-colors duration-150 group/remove"
                >
                    <img src={DeleteIcon} alt="Remove" className="w-5 h-5 group-hover/remove:hidden" />
                    <img src={DeleteRed} alt="Remove" className="hidden w-5 h-5 group-hover/remove:block" />
                    <span className=" font-medium text-[14px] group-hover/remove:text-[#ED5B5B]">Remove</span>
                </button>
                <button
                    type="button"
                    onClick={handleDownload}
                    className="flex items-center gap-2 w-full p-2 rounded-[8px] py-2 text-left text-[#808080] hover:text-[#CCCCCC] hover:bg-[#FFFFFF0A] transition-colors duration-150 group/download"
                >
                    <img src={DownloadWhiteIcon} alt="Download" className="w-5 h-5 group-hover/download:hidden opacity-40" />
                    <img src={DownloadWhiteIcon} alt="Download" className="hidden w-5 h-5 group-hover/download:block" />
                    <span className=" font-medium text-[14px]">Download</span>
                </button>
            </div>,
            document.body
        )}

        {showImagePreview && (
            <ImagePreviewUrl
                imageUrl={url}
                imageName={title}
                isOpen={isImagePreviewOpen}
                onClose={() => setIsImagePreviewOpen(false)}
            />
        )}
    </>
    )
}

export default AssetCard