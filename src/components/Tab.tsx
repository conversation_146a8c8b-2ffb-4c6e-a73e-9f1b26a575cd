import React, { useMemo, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'motion/react';
import Lottie, { LottieRefCurrentProps } from 'lottie-react';
import * as Tooltip from "@radix-ui/react-tooltip";

// Assets
import { ITab } from './TabBar';
import HomeLottie from "@/assets/lottie/home.json"
import { TooltipProvider } from './ui/tooltip';

// Create memoized Tab component to prevent re-renders
const Tab = React.memo(({
    tab,
    isActive,
    tabWidth,
    getTabWidth,
    onTabClick,
    onTabClose
}: {
    tab: ITab;
    isActive: boolean;
    tabWidth: number;
    getTabWidth?: (tab: ITab) => number;
    onTabClick: (tab: ITab) => void;
    onTabClose: (e: React.MouseEvent, tab: ITab) => void;
}) => {
    // Calculate whether this tab should be hidden
    const isHidden = useMemo(() => {
        return localStorage.getItem('embeddedTaskTabId') === tab.id || tab.path === "/not-defined";
    }, [tab.id, tab.path]);

    const isHomeTab = tab.id === 'home';
    const lottieRef = useRef<LottieRefCurrentProps>(null);
    const prevIsActiveRef = useRef(isActive);

    const isCreateNewAgentTab = tab.title.toLowerCase().includes("create new agent") || tab.path === "/create-agent";
    const isCreateSubagentTab = tab.title.toLowerCase().includes("create subagent") || tab.path === "/create-subagent";
    const isCreateTab = isCreateNewAgentTab || isCreateSubagentTab;

    // Get the actual width for this specific tab
    const actualTabWidth = getTabWidth ? getTabWidth(tab) : (isCreateTab ? 300 : tabWidth);

    useEffect(() => {
        if (isHomeTab && lottieRef.current) {
            if (isActive && !prevIsActiveRef.current) {
                lottieRef.current.play();
            } else if (!isActive) {
                lottieRef.current.goToAndStop(45, true);
            }

            prevIsActiveRef.current = isActive;
        }
    }, [isActive, isHomeTab]);

    // Return null after all hooks are defined
    if (isHidden) {
        return null;
    }

    return (
      <div className="flex-shrink-0">
        <motion.div
          onClick={() => onTabClick(tab)}
          style={
            !isHomeTab
              ? {
                  width: `${actualTabWidth}px`,
                  minWidth: `${Math.min(actualTabWidth, isCreateTab ? actualTabWidth : 100)}px`,
                  maxWidth: `${actualTabWidth}px`,
                }
              : undefined
          }
          className={cn(
            "group flex justify-between items-center space-x-2 px-4 cursor-pointer transition-colors font-brockmann text-[14px] leading-[20px] font-medium",
            isActive
              ? "bg-[#0F0F10] rounded-t-xl py-2 pb-3"
              : "hover:bg-white/5 rounded-md py-1.5 bg-white/0",
            !isHomeTab && "overflow-hidden", isActive && isCreateTab && "bg-[#161617] rounded-t-xl py-2 pb-3 border border-[#F8FF99]/30 border-b-0", isActive && isCreateSubagentTab && "bg-[#161617] rounded-t-xl py-2 pb-3 border border-[#DD99FF]/30 border-b-0"
          )}
          animate={{
            color: isActive ? "#ffffff" : "#B3B3B3",
          }}
          transition={{
            duration: 0.2,
            ease: "easeOut",
          }}
        >
          {isHomeTab ? (
            <div className="flex items-center space-x-0 md:space-x-2">
              <Lottie
                style={{
                  opacity: isActive ? 1 : 0.5,
                }}
                lottieRef={lottieRef}
                animationData={HomeLottie}
                loop={true}
                autoplay={false}
                className="w-6 h-6"
              />
              <p className="hidden md:block">Home</p>
            </div>
          ) : (
            <>
              <TooltipProvider>
                <Tooltip.Root delayDuration={300}>
                  <Tooltip.Trigger asChild>
                    <div className="flex items-center space-x-2 overflow-hidden">
                      {isActive && (
                        <motion.span
                          className={cn(
                            "rounded-full w-2 h-2 pr-2",
                            isCreateNewAgentTab && "bg-[#F8FF99]",
                            isCreateSubagentTab && "bg-[#DD99FF]", // Different color for subagent
                            !isCreateTab && "bg-[#00CCAF]"
                          )}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.15 }}
                        />
                      )}

                      <span className={cn(
                        "text-sm font-medium cursor-default",
                        isCreateNewAgentTab && "text-[#F8FF99] text-nowrap",
                        isCreateSubagentTab && "text-[#DD99FF] text-nowrap", // Different color for subagent
                        !isCreateTab && "truncate" , !isActive && "text-[#B3B3B3]"
                      )}>
                        {tab.title}
                      </span>

                      <Tooltip.Content
                        className="max-w-xs bg-[#fff] text-[#0f0f10] font-medium tracking-[-0.3px] text-sm px-4 py-2.5 rounded-lg shadow-lg z-[9999] truncate"
                        sideOffset={5}
                        side="bottom"
                        asChild
                      >
                        <motion.div
                          initial={{ opacity: 0, y: 2, scale: 1 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 1 }}
                          transition={{
                            type: "spring",
                            stiffness: 400,
                            damping: 25,
                            mass: 0.5,
                            duration: 0.0,
                          }}
                        >
                          {tab.title}
                          <Tooltip.Arrow className="fill-[#fff]" />
                        </motion.div>
                      </Tooltip.Content>
                    </div>
                  </Tooltip.Trigger>
                  <button
                    type="button"
                    title="Close Tab"
                    onClick={(e) => onTabClose(e, tab)}
                    className="hover:bg-destructive/10 rounded-full p-0.5 flex-shrink-0"
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="text-[#7B7B80]"
                    >
                      <path
                        d="M18 6L6 18M6 6L18 18"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </Tooltip.Root>
              </TooltipProvider>
            </>
          )}
        </motion.div>
      </div>
    );
});

export default Tab;