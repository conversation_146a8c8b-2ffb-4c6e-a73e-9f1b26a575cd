import { X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ProModalAnalytics,
  createTrackingData,
} from "@/services/proModalTracking";
import { useAuth } from "@/contexts/AuthContext";

import MobileSheet from "@/assets/pro/intro cover-mobile.png";
import NextSVG from "@/assets/pro/next.svg";
import BackSVG from "@/assets/pro/back.svg";
import TvSVG from "@/assets/pro/tv.svg";
import TvSVGMobile from "@/assets/pro/tvMobile.svg";

import GemSVG from "@/assets/pro/gem.svg";
import GemSVGMobile from "@/assets/pro/gemMobile.svg";

import CommentGlow from "@/assets/pro/comment.svg";
import DoubleGlow from "@/assets/pro/2x.svg";
import AgentGlow from "@/assets/pro/agentGlow.svg";

import FirstMobileNewApp from "@/assets/pro/mobile asset_mobile.png"


import CommentGlowMobile from "@/assets/pro/agentGlowMobile.svg";
import DoubleGlowMobile from "@/assets/pro/2xmobile.svg";
import AgentGlowMobile from "@/assets/pro/commentGlowMobile.svg";


import ResearchSVG from "@/assets/pro/resarch.svg";
import DataAnalyst from "@/assets/pro/dataanalyst.svg";
import EngineerSVG from "@/assets/pro/engineer.svg";
import PlusSVG from "@/assets/pro/plus.svg";
import HowItWorks from "@/assets/pro/howitworks.svg";
import HowItWorksMobile from "@/assets/pro/howitworksMobile.svg";
import FirstMobileApp from "@/assets/pro/appFirst.png"

import MobileIntegration from "@/assets/pro/features.svg"
import MobileExpertIcon from "@/assets/pro/mobile-expert.svg"
import  NativeAppIcon from "@/assets/pro/mobile.svg"

import FirstSVG from "@/assets/pro/1.svg";
import SecondSVG from "@/assets/pro/2.svg";
import ThirdSVG from "@/assets/pro/3.svg";


import FirstSVGMobile from "@/assets/pro/1mobile.svg";
import SecondSVGMobile from "@/assets/pro/2mobile.svg";
import ThirdSVGMobile from "@/assets/pro/3mobile.svg";


import FirstBanner from "@/assets/pro/firstBanner.png";
import SecondBanner from "@/assets/pro/secondBanner.png";
import BG from "@/assets/pro/bg.png";
import OtherFieldBG from "@/assets/pro/other-field-bg.png";
import FirstScreen from "@/assets/pro/firstScreen.png";
import { cn } from "@/lib/utils";

import FirstMobile from "@/assets/pro/FirstMobile.png";
import SecondMobile from "@/assets/pro/SecondMobile.png";
import { URL_LINKS } from "@/constants/constants";
import { useProModePayment } from "@/hooks/useProModePayment";
interface ProModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  shouldShowThankYou?: boolean;
  isBookedUserFlow?: boolean;
}

interface InfoInterface {
  title: string;
  description: string;
  icon: string;
   iconMobile: string,
}

interface StepContentConfig {
  title: string;
  description: string;
  features: InfoInterface[];
  desktopImage: string;
  mobileImage: string;
  iconScale?: string;
  mobileMaxHeight?: string;
  cloudId?: {
    desktop: string;
    mobile: string;
  };
}

const PRO_DIFFERENT: InfoInterface[] = [
  {
    title: "Modify System Prompts",
    description:
      "Make Emergent work YOUR way. Edit system prompt to match your exact needs and preferences.",
    icon: CommentGlow,
    iconMobile: CommentGlowMobile,
  },

  {
    title: "Custom Agents",
    description:
      "Build your own AI dream team — main agents to lead, sub-agents to execute. Mix, match, reuse.",
    icon: AgentGlow,
     iconMobile: AgentGlowMobile,
  },
  {
    title: "2x Faster Machines",
    description:
      "Scale up with lightning-fast builds and seamless multi-agent execution.",
    icon: DoubleGlow,
     iconMobile: DoubleGlowMobile,
  },
];

const PRO_DIFFERENT_MOBILE: InfoInterface[] = [
  {
    title: "Native App Development",
    description:
      "Generate iOS and Android apps that feel completely native - with smooth animations, UI components, and device-specific capabilities.",
    icon: NativeAppIcon,
    iconMobile: NativeAppIcon,
  },

  {
    title: "Mobile-Expert AI Agents",
    description:
      "Purpose-built agents trained on mobile frameworks, responsive design principles, and Mobile development best practices.",
    icon: MobileExpertIcon,
     iconMobile: MobileExpertIcon,
  },
  {
    title: "Advanced Mobile Integrations",
    description:
      "Tap into native device features - camera, push notifications, etc - without worrying about complex native code.",
    icon: MobileIntegration,
     iconMobile: MobileIntegration,
  },
];

const HOW_IT_WORKS: any[] = [
  {
    title: "Define Your Agent’s \n Personality & Role",
    description: "Describe who your agent is and what they’re great at.",
    icon: FirstSVG,
    comment:
      "“You're an expert e-commerce dev who builds high-converting stores...”",
       iconMobile: FirstSVGMobile,
  },

  {
    title: "Map Out the \n Workflow & Logic",
    description: "Break down how they should approach tasks step by step.",
    icon: SecondSVG,
    comment: "“Start by analyzing the product, then craft the hero section...”",
     iconMobile: SecondSVGMobile,
  },
  {
    title: "Add Guardrails \n for Better Decisions",
    description:
      "Establish do’s and don’ts so your agent makes the right decisions.",
    icon: ThirdSVG,
    comment: "“Always design mobile-first. Never skip responsiveness.”",
     iconMobile: ThirdSVGMobile,
  },
];

const ABILITIES: any[] = [
  {
    title: "Research Agent",
    description:
      "Scans the web, compiles insights, and generates professional reports.",
    icon: ResearchSVG,
    color: "#FFAA80",
  },

  {
    title: "Data Analyst Agent",
    description:
      "Analyzes trends, models data, and visualizes it with clear dashboards.",
    icon: DataAnalyst,
    color: "#80FFF9",
  },
  {
    title: "Automation Engineer",
    description:
      "Connects APIs, automates workflows, and handles scheduled tasks.",
    icon: EngineerSVG,
    color: "#DD99FF",
  },
  {
    title: "And many more",
    description:
      "From SaaS builders to legacy upgraders - your agent team does it all.",
    icon: PlusSVG,
    color: "#D9D9DB",
  },
];

// Step content configurations
const STEP_CONFIGS: Record<number, StepContentConfig> = {
  0: {
    title: "What Makes Pro Different",
    description: "Unlock Mobile App Development's Full Potential with Emergent Pro Mode.",
    features: PRO_DIFFERENT_MOBILE,
    desktopImage: FirstMobileApp,
    mobileImage: FirstMobileNewApp,
    mobileMaxHeight: "max-md:max-h-[250px]",
    cloudId: {
      desktop: "mobile_asset_aofbgp",
      mobile: "mobile_asset_mobile_qetgdl",
    },
  },
  1: {
    title: "What Makes Pro Different",
    description: "Unlock Emergent's full power with advanced agents, faster machines, and exclusive pro tools.",
    features: PRO_DIFFERENT,
    desktopImage: FirstBanner,
    mobileImage: FirstMobile,
    iconScale: "scale-[3]",
    mobileMaxHeight: "max-md:max-h-[200px]",
    cloudId: {
      desktop: "first_mobile_app",
      mobile: "first_mobile_app",
    },
  },
};

// Reusable component for steps 0 and 1
function ProDifferentStep({
  config,
  containerVariants,
  itemVariants
}: {
  config: StepContentConfig;
  containerVariants: any;
  itemVariants: any;
}) {

  //  const {isMobile} = useScreenSize();
   
  return (
    <>
      <div className="flex flex-col justify-between flex-1 gap-4 md:px-[28px] md:py-12">
        <motion.div
          key="form-step-content"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-4 md:space-y-8"
        >
          <div className="flex flex-col md:gap-2">
            <div className="flex flex-col gap-2 md:gap-4 max-md:px-5 max-md:pt-2">
              {/* Title */}
              <h1
                style={{
                  textShadow: "0 0 10px rgba(255, 255, 255, 0.2)",
                }}
                className="text-[16px] md:text-[28px] leading-[32px] font-semibold tracking-[-2%] text-white "
              >
                {config.title}
              </h1>
            </div>

            {/* Description */}
            <p className="text-[#737780]  max-md:px-5  text-[13px] font-['Inter'] font-medium  md:text-[15px] text-wrap whitespace-pre-wrap break-words leading-[20px]  md:leading-[24px]">
              {config.description}
            </p>
          </div>

          {/* Features List */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="flex md:grid md:grid-cols-1 gap-[20px] max-md:pl-5 overflow-x-auto hide-scrollbar"
          >
            {config.features.map((item, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex rounded-[8px] max-md:min-w-[70%] max-md:my-2 max-md:flex-col-reverse items-start max-md:items-start p-3 md:px-5 md:py-4 w-full text-center bg-[#80FFF908] gap-4"
              >
                <div className="flex flex-col items-start w-full gap-[2px] md:gap-[6px]">
                  <h3 className="text-[14px] md:text-[16px] text-[#80FFF9] font-medium blue-shadow">
                    {item.title}
                  </h3>
                  <p className="text-[12px] md:text-[14px] font-[500] text-start text-[#80FFF966] font-['Inter']">
                    {item.description}
                  </p>
                </div>
                <img
                  src={item.icon}
                  alt={item.title}
                  className={`w-5 h-5 mt-1 max-md:hidden ${config.iconScale || ''}`}
                />
                <img
                  src={item.iconMobile}
                  alt={item.title}
                  className="w-5 h-5 mt-1 md:hidden"
                />
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Right Side - Preview/Illustration */}
      <div className={`relative ${config.mobileMaxHeight || 'max-md:max-h-[250px]'} flex-1 overflow-hidden bg-[#131314]`}>
        {/* <AdvancedImage
          cldImg={createOptimizedImage(isMobile ? config.mobileImage : config.desktopImage)}
          className="object-cover w-full h-full md:object-cover  max-md:scale-[1.05] max-md:hidden"
          loading="eager"
          plugins={[placeholder({mode: "blur"})]}
        /> */}
        <img
          alt="Banner"
          src={config.desktopImage}
          className="object-cover w-full h-full md:object-cover  max-md:scale-[1.05] max-md:hidden"
          loading="eager"
        />
        <img
          alt="Banner"
          src={config.mobileImage}
          className="object-cover w-full h-full md:object-fill  max-md:scale-[1.05] max-md:block md:hidden"
          loading="eager"
        />
        {/* Decorative Elements */}
        <div className="absolute top-1/4 right-8 w-32 h-32 bg-gradient-radial from-[#80FFF9]/20 to-transparent rounded-full blur-2xl" />
        <div className="absolute bottom-1/4 right-16 w-24 h-24 bg-gradient-radial from-[#80FFF9]/15 to-transparent rounded-full blur-xl" />
      </div>
    </>
  );
}

export function ProModal({ isOpen, onOpenChange, shouldShowThankYou = false, isBookedUserFlow = false }: ProModalProps) {
  const [view, setView] = useState<"intro" | "form">("intro");
  const [formStep, setFormStep] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [formValue, setFormValue] = useState("");
  // Tracking state
  const { user } = useAuth();
  const modalOpenTimeRef = useRef<number | null>(null);
  const stepTimestampsRef = useRef<Record<number, number>>({});
  const hasTrackedModalOpenRef = useRef(false);

  // Pro mode payment state
  const { isProModeUser, isLoading: isPaymentLoading } = useProModePayment();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut" as const,
      },
    },
  };

  // Tracking functions
  const trackModalOpen = () => {
    if (!hasTrackedModalOpenRef.current) {
      modalOpenTimeRef.current = Date.now();
      hasTrackedModalOpenRef.current = true;

      ProModalAnalytics.trackModalOpened(
        createTrackingData(user?.id, user?.email)
      );
    }
  };

  const trackModalClose = (
    reason: "user_close" | "completion" | "abandonment"
  ) => {
    const timeSpent = modalOpenTimeRef.current
      ? Date.now() - modalOpenTimeRef.current
      : 0;

    ProModalAnalytics.trackModalClosed({
      ...createTrackingData(user?.id, user?.email),
      reason,
      timeSpentMs: timeSpent,
      timeSpentSeconds: Math.round(timeSpent / 1000),
      currentView: view,
      currentStep: formStep,
      formValue: formValue.trim(),
      formValueLength: formValue.trim().length,
    });
  };

  const trackStepProgression = (
    fromStep: number,
    toStep: number,
    direction: "forward" | "backward"
  ) => {
    const now = Date.now();
    const stepStartTime = stepTimestampsRef.current[fromStep];
    const timeOnStep = stepStartTime ? now - stepStartTime : 0;

    // Record timestamp for the new step
    stepTimestampsRef.current[toStep] = now;

    ProModalAnalytics.trackStepProgression({
      ...createTrackingData(user?.id, user?.email),
      fromStep,
      toStep,
      direction,
      timeOnStepMs: timeOnStep,
      timeOnStepSeconds: Math.round(timeOnStep / 1000),
      currentView: view,
    });
  };

  const trackFormCompletion = () => {
    const totalTime = modalOpenTimeRef.current
      ? Date.now() - modalOpenTimeRef.current
      : 0;

    ProModalAnalytics.trackFormCompleted({
      ...createTrackingData(user?.id, user?.email),
      formValue: formValue.trim(),
      formValueLength: formValue.trim().length,
      totalTimeMs: totalTime,
      totalTimeSeconds: Math.round(totalTime / 1000),
      stepsCompleted: 4,
    });
  };

  const handleNext = () => {
    const currentStep = formStep;
    let nextStep = currentStep + 1;

    // For booked users, skip text input step (step 2) and payment step (step 3)
    if (isBookedUserFlow && currentStep === 2) {
      nextStep = 4; // Skip steps 2 and 3, go directly to thank you
    }

    // Track step progression
    trackStepProgression(currentStep, nextStep, "forward");

    // Track form completion when moving to final step
    if (nextStep === 5) {
      if (isBookedUserFlow) {
        // For booked users, just go to thank you step
        trackFormCompletion();
      } else {
        // For new users, redirect to payment
        const baseUrl = URL_LINKS.buyProStripe;
        const email = user?.email ? encodeURIComponent(user.email) : '';
        const clientReferenceId = user?.id || 'unknown_user';
        const utmTerm = 'pro_mode';

        const paymentUrl = `${baseUrl}${email}&client_reference_id=${clientReferenceId}&utm_term=${utmTerm}`;

        // Open payment link in same window instead of new tab
        window.location.href = paymentUrl;
        trackFormCompletion();
        return; // Don't proceed to next step since we're redirecting
      }
    }

    setFormStep(nextStep);
  };

  const handlePrev = () => {
    if (formStep > 0) {
      const currentStep = formStep;
      const prevStep = currentStep - 1;

      trackStepProgression(currentStep, prevStep, "backward");
      setFormStep(prevStep);
    } else if (formStep == 0 && view == "form") {
      ProModalAnalytics.trackViewChanged({
        ...createTrackingData(user?.id, user?.email),
        fromView: "form",
        toView: "intro",
        currentStep: formStep,
      });
      setView("intro");
    }
  };

  useEffect(() => {
    if (formStep == 4) {
      textareaRef.current?.focus();
    }
  }, [formStep]);

  // Track modal open/close
  useEffect(() => {
    if (isOpen) {
      trackModalOpen();
      // Record timestamp for initial step
      stepTimestampsRef.current[0] = Date.now();

      // Only show thank you step when shouldShowThankYou is explicitly true (from localStorage)
      if (shouldShowThankYou) {
        setView("form");
        setFormStep(5);
      }
    }
  }, [isOpen, isProModeUser, isPaymentLoading, shouldShowThankYou]);


  // Track view changes
  useEffect(() => {
    if (isOpen && view === "form") {
      ProModalAnalytics.trackViewChanged({
        ...createTrackingData(user?.id, user?.email),
        fromView: "intro",
        toView: "form",
        currentStep: formStep,
      });
    }
  }, [view, isOpen, user?.id, user?.email, formStep]);

  // Track form value changes (for abandonment analysis)
  useEffect(() => {
    if (formStep === 4 && formValue.trim().length > 0) {
      ProModalAnalytics.trackFormInput({
        ...createTrackingData(user?.id, user?.email),
        formValueLength: formValue.trim().length,
        hasContent: formValue.trim().length > 0,
      });
    }
  }, [formValue, formStep, user?.id, user?.email]);

  const handleClose = () => {
    // Determine close reason
    let closeReason: "user_close" | "completion" | "abandonment" = "user_close";

    if (formStep === 5) {
      closeReason = "completion";
    } else if (formStep > 0 || view === "form") {
      closeReason = "abandonment";
    }

    trackModalClose(closeReason);

    onOpenChange(false);
    setFormStep(0);
    setView("intro");
    setFormValue("");

    // Reset tracking state
    hasTrackedModalOpenRef.current = false;
    modalOpenTimeRef.current = null;
    stepTimestampsRef.current = {};
  };

  if (!isOpen) return null;

  return (
    <div className="fixed top-0 left-0 bottom-0 right-0 w-full h-full z-[999] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]">
      <div className="max-w-[95vw] md:max-w-[900px] md:h-[700px] max-md:h-[800px] max-h-[90dvh]  relative w-full bg-[#131314] text-white border border-[#1A1A1A] rounded-2xl overflow-hidden">
        {view === "intro" && (
          <>
            <img
              src={FirstScreen}
              alt="Pro"
              className="object-fill w-full h-full max-md:hidden"
              loading="eager"
            />

            <img
              src={MobileSheet}
              alt="Pro"
              className="absolute left-0 right-0 w-full cursor-pointer max-md:block md:hidden"
              loading="eager"
            />

            <button
              title="Close"
              className="absolute top-4 right-4 md:top-7 md:right-7 z-[99] bg-[#FFFFFF05] backdrop-blur-lg hover:bg-[#FFFFFF12] p-2 rounded-full"
              onClick={handleClose}
            >
              <X className="w-6 h-6" />
            </button>

            <motion.button
              onClick={() => {
                ProModalAnalytics.trackInterestedClicked(
                  createTrackingData(user?.id, user?.email)
                );
                setView("form");
              }}
              className="absolute  max-md:bottom-0 max-md:left-4 max-md:right-4 max-md:items-center max-md:mb-[4px] max-md:justify-center max-md:rounded-[8px] interested-button mt-[6rem] md:mt-[8rem] max-h-[48px] md:top-1/2 md:left-1/2 hover:scale-[1.03] md:hover:mt-[7.5rem] transform md:-translate-x-1/2 md:-translate-y-1/2 flex max-md:text-[14px] items-center gap-2 p-3 px-5 bg-white text-black rounded-full font-semibold text-[16px] cursor-pointer"
            >
              I'm Interested
              <img src={NextSVG} alt="Next" className="w-6 h-6" />
            </motion.button>
          </>
        )}

        {view === "form" && (
          <>
            <div className="relative flex w-[inherit] h-full max-md:flex-col-reverse">
              {/* Left Side - Content */}

              <button
                title="Close"
                className="absolute top-4 right-4 md:top-7 md:right-7 z-[99] bg-[#FFFFFF05] backdrop-blur-lg hover:bg-[#FFFFFF12] p-2 rounded-full"
                onClick={handleClose}
              >
                <X className="w-6 h-6" />
              </button>

               {formStep == 0 && (
                <ProDifferentStep
                  config={STEP_CONFIGS[0]}
                  containerVariants={containerVariants}
                  itemVariants={itemVariants}
                />
              )}

              {formStep == 1 && (
                <ProDifferentStep
                  config={STEP_CONFIGS[1]}
                  containerVariants={containerVariants}
                  itemVariants={itemVariants}
                />
              )}

              {formStep == 2 && (
                <>
                  <div className="flex flex-col justify-between flex-1 gap-4 md:px-[28px] md:py-12">
                    <motion.div
                      key="form-step-1"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="space-y-4 md:space-y-8"
                    >
                      <div className="flex flex-col gap-2">
                        <div className="flex flex-col gap-2 md:gap-4 max-md:px-5 max-md:pt-3">
                          {/* Title */}
                          <h1 className="text-[20px] md:text-[28px] leading-[32px] font-semibold tracking-[-2%] text-white ">
                            What You'll Be Able to Do
                          </h1>
                        </div>

                        {/* Description */}
                        <p className="text-[#737780]  max-md:px-5  text-[14px] font-['Inter']  md:text-[15px] text-wrap whitespace-pre-wrap break-words leading-[20px]  md:leading-[24px]">
                          Create and monetize expert-level AI agents tailored to
                          custom use cases.
                        </p>
                      </div>

                      {/* Features List */}
                      <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        className="flex md:grid md:grid-cols-2 gap-[10px] max-md:pl-5 overflow-x-auto hide-scrollbar"
                      >
                        {ABILITIES.map((item, index) => (
                          <motion.div
                            key={index}
                            variants={itemVariants}
                            className="flex rounded-[8px] max-md:min-w-[70%] flex-col items-start max-md:items-start p-3 md:px-5 md:py-4 w-full text-center gap-8"
                            style={{
                              background: `${item.color}08`,
                            }}
                          >
                            <img
                              src={item.icon}
                              alt={item.title}
                              className="w-7 h-7"
                            />
                            <div className="flex flex-col items-start w-full gap-[2px] md:gap-[6px]">
                              <h3
                                className="text-[14px] text-nowrap md:text-[16px] font-medium blue-shadow"
                                style={{
                                  color: item.color,
                                }}
                              >
                                {item.title}
                              </h3>
                              <p
                                className="text-[12px] md:text-[14px] font-[500] text-start font-['Inter']"
                                style={{
                                  color: item.color,
                                  opacity: 0.4,
                                }}
                              >
                                {item.description}
                              </p>
                            </div>
                          </motion.div>
                        ))}
                      </motion.div>
                    </motion.div>
                  </div>

                  {/* Right Side - Preview/Illustration */}
                  <div className="relative max-md:max-h-[200px] flex-1 overflow-hidden bg-[#131314]">
                    <img
                      alt="Second Banner"
                      src={SecondBanner}
                      className="object-cover w-full h-full md:object-fill  max-md:scale-[1.05] max-md:hidden"
                      loading="eager"
                    />

                    <img
                      alt="Second Banner"
                      src={SecondMobile}
                      className="object-cover w-full h-full md:object-fill  max-md:scale-[1.05] max-md:block md:hidden"
                      loading="eager"
                    />
                    {/* Decorative Elements */}
                    <div className="absolute top-1/4 right-8 w-32 h-32 bg-gradient-radial from-[#80FFF9]/20 to-transparent rounded-full blur-2xl" />
                    <div className="absolute bottom-1/4 right-16 w-24 h-24 bg-gradient-radial from-[#80FFF9]/15 to-transparent rounded-full blur-xl" />
                  </div>
                </>
              )}

              {formStep == 3 && (
                <>
                  <img
                    src={OtherFieldBG}
                    alt="BG"
                    className="absolute w-full h-full max-md:hidden"
                  />
                  {/* <img src={MobileNormalBG} alt="BG" className="absolute inset-0 z-0 object-fill w-full h-full max-md:block md:hidden" /> */}
                  <div className="flex flex-col justify-between flex-1 gap-4 md:px-[28px] z-[10] md:py-12">
                    <motion.div
                      key="form-step-2"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="space-y-4 md:space-y-8"
                    >
                      <div className="flex flex-col gap-2">
                        <div className="flex flex-col gap-2 md:gap-4 max-md:px-5 max-md:pt-3">
                          <img
                            src={HowItWorks}
                            alt="How It Works"
                            className="w-10 h-10 scale-[2] max-md:hidden"
                          />
                          <img
                            src={HowItWorksMobile}
                            alt="How It Works"
                            className="w-10 h-10 md:hidden"
                          />
                          {/* Title */}
                          <h1 className="text-[20px] md:text-[28px] leading-[32px] font-semibold tracking-[-2%] text-white ">
                            How It Works ?
                          </h1>
                        </div>

                        {/* Description */}
                        <p className="text-[#737780] max-md:px-5  text-[14px] font-['Inter']  md:text-[15px] text-wrap whitespace-pre-wrap break-words leading-[20px]  md:leading-[24px]">
                          Create a powerful custom agent in just a few guided
                          steps. It's simpler than you think:
                        </p>
                      </div>

                      {/* Features List */}
                      <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        className="grid max-md:grid max-md:grid-flow-col max-md:auto-cols-[70%] max-md:overflow-x-auto max-md:gap-[10px] md:grid-cols-3 gap-[10px] max-md:pl-5 hide-scrollbar"
                      >
                        {HOW_IT_WORKS.map((item, index) => (
                          <motion.div
                            key={index}
                            variants={itemVariants}
                            className="flex h-full gap-[20px] max-md:pb-4 md:min-h-[300px] rounded-[8px] flex-col justify-between p-3 md:px-6 md:py-6 w-full text-center bg-[#19191A]"
                          >
                            <div className="flex flex-col gap-6 md:gap-[40px]">
                              <img
                                src={item.icon}
                                alt={item.title}
                                className="w-12 md:scale-[1.5] h-12 max-md:hidden"
                              />

                              <img
                                src={item.iconMobile}
                                alt={item.title}
                                className="w-12 h-12 mt-1 md:hidden"
                              />
                              <div className="flex flex-col items-start w-full gap-[2px] md:gap-[12px]">
                                <h3
                                  className="text-[14px] text-start md:text-[16px] text-[#D9D9DB] font-medium"
                                  style={{
                                    textShadow:
                                      "0 0 10px rgba(217, 217, 219, 0.5)",
                                  }}
                                >
                                  {item.title}
                                </h3>
                                <p className="text-[13px] md:text-[14px] font-[500] text-start text-[#FFFFFF66] font-['Inter']">
                                  {item.description}
                                </p>
                              </div>
                            </div>
                            <span className="items-start text-[10px]  md:text-sm text-start text-[#FFFFFF40] font-['Inter']">
                              {item.comment}
                            </span>
                          </motion.div>
                        ))}
                      </motion.div>
                    </motion.div>
                  </div>
                </>
              )}

              {formStep == 4 && (
                <>
                  <img
                    src={OtherFieldBG}
                    alt="BG"
                    className="absolute w-full h-full max-md:hidden"
                  />
                  {/* <img src={MobileNormalBG} alt="BG" className="absolute inset-0 z-0 object-fill w-full h-full max-md:block md:hidden" /> */}
                  <div className="flex flex-col justify-center flex-1 gap-4 md:px-[40px] z-[10]  md:py-12">
                    <motion.div
                      key="form-step-3"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="space-y-4 md:space-y-8 pb-[40px]"
                    >
                      <div className="flex flex-col items-center gap-4 max-md:px-4 md:gap-8">
                        <img
                          src={TvSVG}
                          alt="TV"
                          className="w-10 h-10 scale-[2] max-md:hidden"
                        />
                        <img
                          src={TvSVGMobile}
                          alt="TV"
                          className="w-10 h-10 md:hidden"
                        />
                        <div className="flex flex-col gap-4">
                          {/* Title */}
                          <span className="animate-text text-center bg-gradient-to-r text-[20px] md:text-[24px] md:mt-[8px] from-[#80FFF9] to-[#F7E7D9] text-transparent bg-clip-text font-medium">
                            What Agents will you create with System Prompt edit
                            feature?
                          </span>
                        </div>

                        <div
                          className="rounded-[16px] w-full p-1  max-w-[600px] bg-red-300 backdrop-blur-lg"
                          style={{
                            height: "160px",
                            background:
                              "linear-gradient(180deg, rgba(128, 255, 249, 0.02) 0%, rgba(128, 255, 249, 0.1) 100%)",
                          }}
                        >
                          <textarea
                            ref={textareaRef}
                            value={formValue}
                            onChange={(e) => setFormValue(e.target.value)}
                            className="w-full h-full rounded-[12px] focus:ring-0 focus:outline-none p-3 bg-[#131314] border border-[#242424] text-white resize-none placeholder:text-[#4A4A4D] font-['Inter']"
                            placeholder="Tell us your use case in detail..."
                          />
                        </div>

                        <div className="flex items-center flex-col justify-between w-full gap-4 max-w-[600px]">
                          <div className="flex items-center justify-between w-full">
                            <span className="opacity-40 max-md:text-[12px] font-nothing tracking-[2px]">
                              {">>>"}
                            </span>
                            <span className="font-nothing text-white/60 max-md:text-[12px] text-center tracking-[2px] uppercase max-md:mt-3 flex max-md:flex-col gap-1 md:gap-2">
                              We will open access gradually
                            </span>
                            <span className="font-nothing opacity-40 tracking-[2px] max-md:text-[12px]">
                              {"<<<"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </>
              )}

              {formStep == 5 && (
                <>
                  <img
                    src={BG}
                    alt="BG"
                    className="absolute w-full h-full max-md:hidden"
                  />
                  {/* <img src={MobileNormalBG} alt="BG" className="absolute inset-0 z-0 object-fill w-full h-full max-md:block md:hidden" /> */}
                  <div className="flex flex-col justify-center items-center z-[10] flex-1 gap-4 md:px-[40px] md:py-12">
                    <motion.div
                      key="form-step-4"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="space-y-4 md:space-y-8 mb-[50px]"
                    >
                      <div className="flex flex-col items-center gap-8 text-center">
                        <img
                          src={GemSVG}
                          alt="TV"
                          className="w-10 h-10 scale-[4] max-md:hidden"
                        />
                        <img
                          src={GemSVGMobile}
                          alt="TV"
                          className="w-10 h-10 md:hidden"
                        />
                        <div className="flex flex-col gap-4 max-md:px-5 max-md:pt-3">
                          {/* Title */}
                          <span
                            className=" uppercase font-nothing backdrop-blur-lg bg-gradient-to-r text-[30px] md:text-[48px] md:mt-[8px] text-[#00FF66] "
                            style={{
                              textShadow: "0 0 40px rgba(0, 255, 102, 0.4)",
                            }}
                          >
                            Thank YOU
                          </span>
                        </div>

                        <span
                          className="font-medium tracking-[0.5px] max-md:text-[14px]"
                          style={{
                            maxWidth: "400px",
                          }}
                        >
                          We are slowly rolling it out! <br /> You have made it
                          to the front of the line
                        </span>
                      </div>
                    </motion.div>
                  </div>
                </>
              )}

              {/* Footer */}

              {formStep == 5 && (
                <div className="absolute flex justify-center px-8 z-[99] left-0 right-0 bottom-12 text-[#FFFFFF50]">
                  <span
                    className="font-nothing text-center tracking-[10px] max-md:text-[12px] text-[14px] uppercase"
                    style={{
                      letterSpacing: "1.5px",
                    }}
                  >
                    Welcome to the Era of hyper personalised AI AGENTS
                  </span>
                </div>
              )}

              {formStep < 5 && (
                <div className="absolute flex justify-between px-[16px] md:px-[28px] z-[99] left-0 right-0 bottom-4 items-center">
                  <div
                    className={cn("flex items-center justify-center gap-2", {
                      "max-md:hidden": formStep == 4,
                    })}
                  >
                    {(isBookedUserFlow ? [0, 1,2, 4] : [0, 1, 2, 3,4]).map((step) => (
                      <div
                        key={step}
                        style={{
                          background:
                            step === formStep ? "#FFFFFF" : "#FFFFFF66",
                          height: "8px",
                          width: step === formStep ? "28px" : "8px",
                        }}
                        className={`rounded-full min-h-2 transition-all duration-300 ease-in-out ${
                          step === formStep
                            ? "w-7 min-w-7 bg-white"
                            : "min-w-2 max-h-2 max-w-2 bg-white/40"
                        }`}
                      />
                    ))}
                  </div>

                  <div className="flex items-center gap-2 md:gap-4 z-[40]">
                    <motion.button
                      type="button"
                      style={{
                        background: "#1F1F21",
                      }}
                      onClick={handlePrev}
                      title="Previous"
                      className=" py-[10px] px-5 backdrop-blur-lg rounded-full hover:bg-[#FFFFFF20] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <img src={BackSVG} alt="Back" className="w-6 h-6" />
                    </motion.button>
                    <motion.button
                      type="button"
                      title="Next"
                      onClick={handleNext}
                      disabled={formStep == 4 && !formValue}
                      animate={{
                        width:  "auto",
                      }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="bg-white flex py-[10px] px-5 gap-1 rounded-full disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden"
                    >
                      <AnimatePresence mode="wait">
                        {formStep == 4 && (
                          <motion.span
                            key="secure-text"
                            initial={{ opacity: 0, width: 0 }}
                            animate={{ opacity: 1, width: "auto" }}
                            exit={{ opacity: 0, width: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="font-semibold text-black whitespace-nowrap max-md:text-[14px]"
                          >
                            Prebook Your Spot at $100
                          </motion.span>
                        )}
                      </AnimatePresence>
                      <img
                        src={NextSVG}
                        alt="Next"
                        className="flex-shrink-0 w-6 h-6"
                      />
                    </motion.button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
