import { motion } from "framer-motion";
import ProSVG from "@/assets/pro/pro.svg";
import ProSVGMobile from "@/assets/pro/proMobile.svg";
import { useProModePayment } from "@/hooks/useProModePayment";

function ProBanner({ modalChange, isAlreadyBooked, onBookedUserClick }: {
  modalChange: (open: boolean) => void;
  isAlreadyBooked?: boolean;
  onBookedUserClick?: () => void;
}) {
  const { isLoading } = useProModePayment();
  return (
    <motion.div
      className="flex items-center justify-center w-full md:p-[10px] sticky top-0 z-[19] max-md:absolute"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: -100, opacity: 0 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.6
      }}
    >
      <motion.div
        className="flex items-center justify-center h-[48px] p-2 max-md:w-full md:pl-5 md:rounded-full pro-green backdrop-blur-lg w-fit gap-4"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{
          delay: 0.3,
          type: "spring",
          stiffness: 300,
          damping: 25
        }}
      >
        <img src={ProSVG} alt="pro" className=" w-6 h-6 scale-[2.5]  md:scale-[3] max-md:hidden" />
        <img src={ProSVGMobile} alt="pro" className="w-6 h-6 md:hidden" />
        <span
          className="inline-block font-medium max-md:hidden"
          style={{
            background:
              "linear-gradient(135deg, rgba(0, 255, 102, 1) 0%, rgba(98, 236, 254, 1) 100%)",
            WebkitBackgroundClip: "text",
            backgroundClip: "text",
            WebkitTextFillColor: "transparent",
            textShadow: `
              0 0 22.43px rgba(0, 255, 102, 0.5),
              0 0 22.43px rgba(98, 236, 254, 0.2)
            `,
          }}
        >
          Launching Emergent Pro, Get Early Access to Mobile App Development
        </span>
         <span
          className="inline-block font-medium md:hidden text-nowrap text-[12px]"
          style={{
            background:
              "linear-gradient(135deg, rgba(0, 255, 102, 1) 0%, rgba(98, 236, 254, 1) 100%)",
            WebkitBackgroundClip: "text",
            backgroundClip: "text",
            WebkitTextFillColor: "transparent",
            textShadow: `
              0 0 22.43px rgba(0, 255, 102, 0.5),
              0 0 22.43px rgba(98, 236, 254, 0.2)
            `,
          }}
        >
          Launching Emergent Pro
        </span>

        <button
          type="button"
          className="relative flex items-center justify-center  max-md:text-[13px] md:text-[15px] md:ml-8"
          aria-label={isAlreadyBooked ? "Already booked" : "Join waitlist"}
          title={isAlreadyBooked ? "Already booked" : "Join waitlist"}
          onClick={() => isAlreadyBooked ? onBookedUserClick?.() : modalChange(true)}
          disabled={isLoading}
          style={{
            height: "32px",
            borderRadius: "16px",
            padding: "8px 8px 8px 12px",
            gap: "6px",
            background: isAlreadyBooked
              ? "linear-gradient(180deg, rgba(0, 255, 102, 0.8) 0%, rgba(98, 236, 254, 0.8) 100%)"
              : "linear-gradient(180deg, rgba(0, 255, 102, 1) 0%, rgba(98, 236, 254, 1) 100%)",
            boxShadow: `
              0 0 20px rgba(98, 236, 254, 0.4),
              inset 0 2.18px 3.64px rgba(255, 255, 255, 0.4),
              inset 0 -1.45px 1.45px rgba(255, 255, 255, 0.2)
            `,
            border: "none",
            cursor: "pointer",
            color: "#0F0F10",
            fontWeight: "600",
            opacity: isLoading ? 0.7 : 1,
            // fontFamily: "system-ui, -apple-system, sans-serif",
          }}
        >
          <span>{isLoading ? "Checking..." : isAlreadyBooked ? "Already Booked" : "Prebook Now"}</span>
          {!isAlreadyBooked && (
            <div
              style={{
                width: "20px",
                height: "20px",
                borderRadius: "10px",
                backgroundColor: "black",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M7.775 3.3821C8.075 3.6825 8.075 4.1696 7.775 4.47L4.313 7.9315C4.013 8.2319 3.526 8.2319 3.225 7.9315C2.925 7.6311 2.925 7.1441 3.225 6.8437L5.374 4.6953L-0.231 4.6953C-0.656 4.6953 -1 4.3509 -1 3.926C-1 3.5013 -0.656 3.1568 -0.231 3.1568L5.374 3.1568L3.225 1.0085C2.925 0.7081 2.925 0.221 3.225 -0.0794C3.526 -0.3798 4.013 -0.3798 4.313 -0.0794L7.775 3.3821Z"
                  fill="#35F4B8"
                  transform="translate(0.5, 0.5)"
                />
              </svg>
            </div>
          )}
          {isAlreadyBooked && (
            <div
              style={{
                width: "20px",
                height: "20px",
                borderRadius: "10px",
                backgroundColor: "black",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M6.5 2L3 5.5L1.5 4"
                  stroke="#35F4B8"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          )}
        </button>
      </motion.div>
    </motion.div>
  );
}

export default ProBanner;
