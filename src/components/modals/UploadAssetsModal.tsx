import React, { useState, useCallback, useRef } from "react";
import {
  X,
  FileText,
  File,
  Image,
  Video,
  Archive,
  Loader2,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useImageCompression } from "@/hooks/useImageCompression";
import { FileAttachmentData, PendingArtifact } from "@/types/artifact";
import { useToast } from "@/hooks/use-toast";
import { useAppDispatch, useAppSelector } from "@/hooks/reduxHooks";
import { addPendingArtifacts, selectActiveTab } from "@/store/tabSlice";
import { agentApi } from "@/services/agentApi";
import { useUploadFileStreamMutation } from "@/store/api/apiSlice";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import { CustomSwitch } from "@/components/ui/custom-switch";
import { getFileTypeLabel } from "@/lib/utils/fileTypeLabels";
import AttachIcon from "@/assets/attach.svg";

import CommentIcon from "@/assets/upload/comment.svg";
import CommentedIcon from "@/assets/upload/commented.svg";

import DeleteIcon from "@/assets/upload/delete.svg";
import DeleteActiveIcon from "@/assets/upload/deleteActive.svg";
import ShareUp from "@/assets/upload/arrow_circle_up.svg";

// Import all file type icons from assets/files
import PDFIcon from "@/assets/files/pdf.svg";
import CSVIcon from "@/assets/files/csv.svg";
import DOCIcon from "@/assets/files/doc.svg";
import PPTIcon from "@/assets/files/ppt.svg";
import TXTIcon from "@/assets/files/txt.svg";
import VideoIcon from "@/assets/files/video.svg";
import SVGIcon from "@/assets/files/svg.svg";
import OtherIcon from "@/assets/files/other.svg";
import ZipIcon from "@/assets/files/zip.svg";

import UploadNormal from "@/assets/upload/upload_normal.svg";

import GoldCoinSVG from "@/assets/new_coin.svg";
import InfoSVG from "@/assets/upload/info.svg";

interface UploadAssetsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  jobId?: string;
  onUploadComplete?: (artifactId: string, fileName: string) => void;
  onUploadError?: (fileName: string, error: string) => void;
  handleSendMessage?: (content: string, images: any[]) => void;
  initialFiles?: File[]; // New prop to pre-populate files
}

// Extended file data with description and agent reading capability
interface ExtendedFileData extends FileAttachmentData {
  description?: string;
  readByAgent?: boolean;
  visibility?: "public" | "private";
}

// Helper function to check if file type is previewable as image
const isPreviewableImage = (mimeType: string, fileName: string) => {
  const imageTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/bmp",
    "image/svg+xml",
  ];
  return (
    imageTypes.includes(mimeType.toLowerCase()) ||
    /\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(fileName)
  );
};

// Helper function to check if file is an image (for determining default visibility)
const isImageFile = (mimeType: string, fileName: string) => {
  return (
    mimeType.startsWith("image/") ||
    /\.(jpg|jpeg|png|gif|webp|bmp|svg|ico)$/i.test(fileName)
  );
};

// Helper function to check if file is a video
const isVideoFile = (mimeType: string, fileName: string) => {
  return (
    mimeType.startsWith("video/") ||
    /\.(mp4|avi|mov|wmv|mkv|webm|flv|m4v)$/i.test(fileName)
  );
};

// Helper function to check if file is an archive/zip
const isArchiveFile = (mimeType: string, fileName: string) => {
  return (
    mimeType.includes("zip") ||
    mimeType.includes("tar") ||
    mimeType.includes("rar") ||
    mimeType.includes("7z") ||
    /\.(zip|rar|tar|gz|7z|bz2|xz)$/i.test(fileName)
  );
};

// Helper function to check if file can be compressed
const isCompressibleImage = (mimeType: string, fileName: string) => {
  // Only specific image types can be compressed
  const compressibleTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  const compressibleExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];

  const isSupportedType = mimeType && compressibleTypes.includes(mimeType);
  const isSupportedExtension = compressibleExtensions.some((ext) =>
    fileName.toLowerCase().endsWith(ext)
  );

  return isSupportedType || isSupportedExtension;
};

// Helper function to determine file handling strategy
const getFileHandlingStrategy = (file: File) => {
  const { size, type, name } = file;
  const isVideo = isVideoFile(type, name);
  const isImage = isImageFile(type, name);
  const isCompressibleImg = isCompressibleImage(type, name);

  // Videos: upload to public, any size allowed, don't send to agent
  if (isVideo) {
    return {
      strategy: "public_only" as const,
      visibility: "public" as const,
      readByAgent: false,
      needsCompression: false,
      maxSize: Infinity, // No size limit for videos
    };
  }

  // Compressible images: 10MB limit, compress if needed, can send to agent
  if (isImage && isCompressibleImg) {
    if (size > 10 * 1024 * 1024) {
      // Over 10MB: reject
      return {
        strategy: "reject" as const,
        visibility: "public" as const,
        readByAgent: false,
        needsCompression: false,
        maxSize: 10 * 1024 * 1024,
      };
    }

    return {
      strategy:
        size > 5 * 1024 * 1024 ? "compress_and_upload" : ("standard" as const),
      visibility: "public" as const,
      readByAgent: true, // Can be sent to agent
      needsCompression: size > 5 * 1024 * 1024,
      maxSize: 10 * 1024 * 1024,
    };
  }

  // Non-compressible images: 5MB hard limit, if >5MB upload publicly but don't send to agent
  if (isImage && !isCompressibleImg) {
    if (size > 5 * 1024 * 1024) {
      return {
        strategy: "public_only" as const,
        visibility: "public" as const,
        readByAgent: false, // Don't send to agent if over 5MB
        needsCompression: false,
        maxSize: Infinity, // No size limit, just upload publicly
      };
    }

    return {
      strategy: "standard" as const,
      visibility: "public" as const,
      readByAgent: true, // Can be sent to agent if under 5MB
      needsCompression: false,
      maxSize: 5 * 1024 * 1024,
    };
  }

  // All other files: no size limit, upload based on type
  return {
    strategy: "no_limit" as const,
    visibility: "public" as const,
    readByAgent: false, // Non-image files can't be sent to agent as base64
    needsCompression: false,
    maxSize: Infinity, // No size limit for other files
  };
};

// Helper function to check if file type is previewable as SVG
const isPreviewableSVG = (mimeType: string, fileName: string) => {
  return (
    mimeType === "image/svg+xml" || fileName.toLowerCase().endsWith(".svg")
  );
};

// Helper function to get file icon based on mime type or extension
const getFileIcon = (mimeType: string, fileName: string) => {
  if (mimeType.startsWith("image/")) return Image;
  if (mimeType.startsWith("video/")) return Video;
  if (mimeType.startsWith("text/") || fileName.endsWith(".txt"))
    return FileText;
  if (
    mimeType.includes("zip") ||
    mimeType.includes("tar") ||
    mimeType.includes("rar")
  )
    return Archive;
  if (fileName.endsWith(".pdf")) return FileText;
  if (fileName.endsWith(".csv") || fileName.endsWith(".xlsx")) return FileText;
  return File;
};

// Helper function to get custom file icon for specific types
const getCustomFileIcon = (mimeType: string, fileName: string) => {
  const lowerFileName = fileName?.toLowerCase() || "";

  // PDF files
  if (lowerFileName.endsWith(".pdf") || mimeType === "application/pdf") {
    return PDFIcon;
  }

    if (
    mimeType.includes("zip") ||
    mimeType.includes("tar") ||
    mimeType.includes("rar")
  ) {
    return ZipIcon;
  }

  // CSV files
  if (lowerFileName.endsWith(".csv") || mimeType === "text/csv") {
    return CSVIcon;
  }

  // Document files (Word, etc.)
  if (
    lowerFileName.endsWith(".doc") ||
    lowerFileName.endsWith(".docx") ||
    mimeType === "application/msword" ||
    mimeType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  ) {
    return DOCIcon;
  }

  // Presentation files (PowerPoint, etc.)
  if (
    lowerFileName.endsWith(".ppt") ||
    lowerFileName.endsWith(".pptx") ||
    mimeType === "application/vnd.ms-powerpoint" ||
    mimeType ===
      "application/vnd.openxmlformats-officedocument.presentationml.presentation"
  ) {
    return PPTIcon;
  }

  // Text files
  if (
    lowerFileName.endsWith(".txt") ||
    lowerFileName.endsWith(".md") ||
    lowerFileName.endsWith(".log") ||
    mimeType === "text/plain" ||
    mimeType === "text/markdown"
  ) {
    return TXTIcon;
  }

  // Video files
  if (
    lowerFileName.endsWith(".mp4") ||
    lowerFileName.endsWith(".avi") ||
    lowerFileName.endsWith(".mov") ||
    lowerFileName.endsWith(".wmv") ||
    lowerFileName.endsWith(".mkv") ||
    mimeType?.startsWith("video/")
  ) {
    return VideoIcon;
  }

  // SVG files (as documents, not images)
  if (lowerFileName.endsWith(".svg") || mimeType === "image/svg+xml") {
    return SVGIcon;
  }

  // Excel files
  if (
    lowerFileName.endsWith(".xls") ||
    lowerFileName.endsWith(".xlsx") ||
    mimeType === "application/vnd.ms-excel" ||
    mimeType ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ) {
    return CSVIcon; // Use CSV icon for Excel files
  }

  // Default for unknown file types
  return OtherIcon;
};

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// Helper function to truncate file name with extension preservation
const truncateFileName = (fileName: string, maxLength: number = 20): string => {
  if (fileName.length <= maxLength) {
    return fileName;
  }

  const lastDotIndex = fileName.lastIndexOf('.');

  if (lastDotIndex === -1) {
    // No extension, just truncate with ellipsis
    return fileName.substring(0, maxLength - 3) + "...";
  }

  const extension = fileName.substring(lastDotIndex);
  const nameWithoutExt = fileName.substring(0, lastDotIndex);
  const maxNameLength = maxLength - extension.length - 3; // Reserve 3 chars for "..."

  if (maxNameLength <= 0) {
    // Extension is too long, just show first chars with ellipsis
    return fileName.substring(0, maxLength - 3) + "...";
  }

  return nameWithoutExt.substring(0, maxNameLength) + "..." + extension;
};



export function UploadAssetsModal({
  isOpen,
  onOpenChange,
  jobId,
  onUploadComplete: _onUploadComplete,
  onUploadError: _onUploadError,
  handleSendMessage: _handleSendMessage,
  initialFiles,
}: UploadAssetsModalProps) {
  const [selectedFiles, setSelectedFiles] = useState<ExtendedFileData[]>([]);
  const [showCommentInput, setShowCommentInput] = useState<Set<number>>(
    new Set()
  );
  const [isUploading, setIsUploading] = useState(false);
  const [currentUploadingIndex, setCurrentUploadingIndex] = useState<
    number | null
  >(null);
  const [uploadedFiles, setUploadedFiles] = useState<Set<number>>(new Set());
  const [isSettingInitialFiles, setIsSettingInitialFiles] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const objectUrlsRef = useRef<Set<string>>(new Set());
  const { toast } = useToast();
  const dispatch = useAppDispatch();
  const activeTabId = useAppSelector(selectActiveTab);
  const [uploadFileStream] = useUploadFileStreamMutation();

  // Initialize image compression hook
  const { compressImage } = useImageCompression({
    maxWidth: 2000,
    maxHeight: 2000,
    quality: 30, // 30% quality for good compression
    format: "jpeg", // Convert all images to JPEG for consistency
  });

  // Helper function to create and track object URLs
  const createObjectURL = useCallback((file: File) => {
    const url = URL.createObjectURL(file);
    objectUrlsRef.current.add(url);
    return url;
  }, []);

  // Don't cleanup object URLs immediately when modal closes
  // The URLs need to persist for image display in chat until artifacts_shared is received
  // We'll let the browser handle cleanup when the page unloads or when the URLs are no longer referenced
  React.useEffect(() => {
    return () => {
      // Clear the reference set but don't revoke URLs
      // The URLs will be cleaned up when pending artifacts are cleared from Redux
      // or when the browser garbage collects them
      objectUrlsRef.current.clear();
    };
  }, []);

  // Note: We no longer use the useFileAttachments hook as we handle file validation manually

  // Handle file selection with new size limits and compression logic
  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      // Prevent file selection during upload or when setting initial files
      if (isUploading || isSettingInitialFiles) return;

      if (files && files.length > 0) {
        const maxFiles = 5;

        // Check if adding these files would exceed the limit
        const totalFilesAfterAdd = selectedFiles.length + files.length;
        if (totalFilesAfterAdd > maxFiles) {
          toast({
            title: "Too many files selected",
            description: `You can only select up to ${maxFiles} assets at a time. Currently selected: ${selectedFiles.length}`,
            variant: "destructive",
            duration: 4000,
          });
          return;
        }

        const oversizedFiles: string[] = [];
        const validFiles: ExtendedFileData[] = [];
        const filesToCompress: File[] = [];

        // First pass: categorize files
        Array.from(files).forEach((file) => {
          const strategy = getFileHandlingStrategy(file);

          if (strategy.strategy === "reject") {
            oversizedFiles.push(
              `${file.name} (${formatFileSize(file.size)} > ${formatFileSize(
                strategy.maxSize
              )})`
            );
          } else if (strategy.needsCompression) {
            filesToCompress.push(file);
          } else {
            // File is within limits and doesn't need compression
            validFiles.push({
              file,
              name: file.name,
              size: file.size,
              type: file.type,
              lastModified: file.lastModified,
              description: "",
              readByAgent: strategy.readByAgent,
              visibility: strategy.visibility,
            });
          }
        });

        // Compress files that need compression
        if (filesToCompress.length > 0) {
          setIsSettingInitialFiles(true); // Show loading state

          try {
            for (const file of filesToCompress) {
              const strategy = getFileHandlingStrategy(file);

              if (isCompressibleImage(file.type, file.name)) {
                const compressionResult = await compressImage(file);

                if (compressionResult) {
                  // Use the compressed blob as a file
                  const compressedFileName = file.name.replace(
                    /\.[^/.]+$/,
                    ".jpg"
                  );
                  const compressedBlob = compressionResult.blob;

                  // Create a file-like object from the blob
                  const compressedFile = Object.assign(compressedBlob, {
                    name: compressedFileName,
                    lastModified: Date.now(),
                    webkitRelativePath: "",
                  }) as File;

                  validFiles.push({
                    file: compressedFile,
                    name: compressedFileName,
                    size: compressedBlob.size,
                    type: "image/jpeg",
                    lastModified: Date.now(),
                    description: "",
                    readByAgent: strategy.readByAgent,
                    visibility: strategy.visibility,
                  });

                  console.log(
                    `Compressed ${file.name}: ${formatFileSize(
                      file.size
                    )} → ${formatFileSize(compressedFile.size)}`
                  );
                } else {
                  // Compression failed, treat as oversized
                  oversizedFiles.push(`${file.name} (compression failed)`);
                }
              }
            }
          } catch (error) {
            console.error("Compression error:", error);
            toast({
              title: "Compression Error",
              description:
                "Some files could not be compressed and were not added.",
              variant: "destructive",
              duration: 4000,
            });
          } finally {
            setIsSettingInitialFiles(false);
          }
        }

        // Show error for oversized files
        if (oversizedFiles.length > 0) {
          toast({
            title: "Files too large",
            description: `The following files exceed limits: ${oversizedFiles.join(
              ", "
            )}. Compressible images: 10MB max, other images: 5MB for agent sharing.`,
            variant: "destructive",
            duration: 8000,
          });
        }

        // Add valid files
        if (validFiles.length > 0) {
          setSelectedFiles((prev) => [...prev, ...validFiles]);
        }
      }
    },
    [
      selectedFiles.length,
      isUploading,
      isSettingInitialFiles,
      compressImage,
      toast,
    ]
  );

  // Handle initial files when modal opens
  React.useEffect(() => {
    if (isOpen && initialFiles && initialFiles.length > 0) {
      setIsSettingInitialFiles(true);

      // Check file limit before processing
      const maxFiles = 5;
      if (initialFiles.length > maxFiles) {
        toast({
          title: "Too many files",
          description: `You can only select up to ${maxFiles} assets at a time. Only the first ${maxFiles} files will be added.`,
          variant: "destructive",
          duration: 4000,
        });
      }

      // Take only the first maxFiles files and process them with new logic
      const filesToProcess = initialFiles.slice(0, maxFiles);

      // Process files using the new file handling strategy
      const processedFiles = filesToProcess.map((file, index) => {
        const strategy = getFileHandlingStrategy(file);
        const objectUrl = URL.createObjectURL(file);
        objectUrlsRef.current.add(objectUrl);

        const isImage = isPreviewableImage(file.type, file.name);

        return {
          id: `initial-${index}-${Date.now()}`,
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          preview: isImage ? objectUrl : null,
          readByAgent: strategy.readByAgent,
          visibility: strategy.visibility,
          description: "",
        };
      });

      setSelectedFiles(processedFiles);

      // Reset the flag after a short delay to allow state to settle
      setTimeout(() => {
        setIsSettingInitialFiles(false);
      }, 100);
    } else if (isOpen && (!initialFiles || initialFiles.length === 0)) {
      // When modal opens without initial files, trigger file selection immediately
      setSelectedFiles([]);
      setIsSettingInitialFiles(false);
      // Trigger file selection dialog immediately
      setTimeout(() => {
        fileInputRef.current?.click();
      }, 100);
    }
  }, [isOpen, initialFiles, toast]);

  // Handle browse files button click
  const handleBrowseClick = useCallback(() => {
    // Prevent browse during upload
    if (isUploading) return;
    fileInputRef.current?.click();
  }, [isUploading]);

  // Handle file input change
  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleFileSelect(e.target.files);
    },
    [handleFileSelect]
  );

  // Handle description change for a file
  const handleDescriptionChange = useCallback(
    (index: number, description: string) => {
      // Prevent description changes during upload
      if (isUploading) return;
      setSelectedFiles((prev) =>
        prev.map((file, i) => (i === index ? { ...file, description } : file))
      );
    },
    [isUploading]
  );


  // Helper function to check if file type supports base64 conversion
  const supportsBase64Conversion = useCallback((file: ExtendedFileData) => {
    // Use the file handling strategy to determine if file can be sent to agent
    const strategy = getFileHandlingStrategy(file.file);
    return strategy.readByAgent;
  }, []);

  // Handle send to agent toggle for a file (only for supported file types)
  const handleSendToAgentToggle = useCallback(
    (index: number, sendToAgent: boolean) => {
      // Prevent toggle changes during upload
      if (isUploading) return;

      const file = selectedFiles[index];
      if (!supportsBase64Conversion(file)) return; // Only allow toggle for supported files

      setSelectedFiles((prev) =>
        prev.map((file, i) =>
          i === index
            ? {
                ...file,
                readByAgent: sendToAgent,
              }
            : file
        )
      );
    },
    [isUploading, selectedFiles, supportsBase64Conversion]
  );

  // Handle file removal
  const handleRemoveFile = useCallback(
    (index: number) => {
      // Prevent file removal during upload
      if (isUploading) return;

      setSelectedFiles((prev) => {
        // Clean up object URL for the removed file if it was previewable
        const fileToRemove = prev[index];
        if (
          fileToRemove &&
          (isPreviewableImage(fileToRemove.type, fileToRemove.name) ||
            isPreviewableSVG(fileToRemove.type, fileToRemove.name))
        ) {
          // Note: We can't easily track individual URLs, but they'll be cleaned up on unmount
          // This is acceptable for the current use case
        }
        const newFiles = prev.filter((_, i) => i !== index);

        // Auto-close modal if all files are removed
        if (newFiles.length === 0) {
          setTimeout(() => {
            onOpenChange(false);
          }, 100); // Small delay to allow state to update
        }

        return newFiles;
      });

      // Also remove from comment input visibility set
      setShowCommentInput((prev) => {
        const newSet = new Set(prev);
        newSet.delete(index);
        // Adjust indices for remaining files
        const adjustedSet = new Set<number>();
        newSet.forEach((i) => {
          if (i > index) {
            adjustedSet.add(i - 1);
          } else {
            adjustedSet.add(i);
          }
        });
        return adjustedSet;
      });
    },
    [isUploading, onOpenChange]
  );

  // Handle comment button toggle
  const handleCommentToggle = useCallback(
    (index: number, e?: React.MouseEvent) => {
      // Prevent comment toggle during upload
      if (isUploading) return;

      // Prevent event propagation to avoid conflicts with onBlur
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }

      setShowCommentInput((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(index)) {
          newSet.delete(index);
        } else {
          newSet.add(index);
        }
        return newSet;
      });
    },
    [isUploading]
  );

  // Reset upload states
  const resetUploadStates = useCallback(() => {
    setIsUploading(false);
    setCurrentUploadingIndex(null);
    setUploadedFiles(new Set());
  }, []);

  // Reset states when modal opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      resetUploadStates();
    }
  }, [isOpen, resetUploadStates]);

  const handleUploadAll = useCallback(async () => {
    if (!jobId || selectedFiles.length === 0 || isUploading) return;

    setIsUploading(true);
    const pendingArtifacts: PendingArtifact[] = [];

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setCurrentUploadingIndex(i);

        if (file.visibility === "private") {
          // Private files: Upload directly using RTK Query
          try {
            const response = await uploadFileStream({
              jobId,
              file: file.file,
              fileName: file.name,
              description: file.description,
            }).unwrap();

            if (response.success && response.artifact_id) {
              // Create local preview URL for images
              console.log("UploadAssetsModal - file data:", {
                name: file.name,
                type: file.type,
                size: file.size,
              });

              // Create both blob URL and base64 for immediate display
              let localPreviewUrl: string | undefined;
              let localPreviewBase64: string | undefined;

              if (isPreviewableImage(file.type, file.name)) {
                // Create blob URL for immediate use
                localPreviewUrl = URL.createObjectURL(file.file);

                // Also create base64 for reliable display
                try {
                  const reader = new FileReader();
                  const base64Promise = new Promise<string>((resolve) => {
                    reader.onload = () => {
                      const result = reader.result as string;
                      resolve(result.split(',')[1]); // Remove data:image/...;base64, prefix
                    };
                    reader.readAsDataURL(file.file);
                  });
                  localPreviewBase64 = await base64Promise;
                } catch (error) {
                  console.error('Failed to create base64 preview:', error);
                }
              }

              console.log("UploadAssetsModal - creating pending artifact:", {
                file_name: file.name,
                mime_type: file.type,
                file_size: file.size,
                local_preview_url: localPreviewUrl,
                has_base64: !!localPreviewBase64,
                isPreviewableImage: isPreviewableImage(file.type, file.name),
                blobUrlValid: localPreviewUrl ? 'YES' : 'NO'
              });

              pendingArtifacts.push({
                artifact_id: response.artifact_id,
                entity_id: jobId,
                entity_type: "job",
                visibility: "private",
                file_name: file.name,
                description: file.description,
                file_path: response.path,
                readByAgent: file.readByAgent,
                // Additional metadata for better local preview
                mime_type: file.type,
                file_size: file.size,
                local_preview_url: localPreviewUrl,
                local_preview_base64: localPreviewBase64,
              });
            }
          } catch (error) {
            console.error(`Direct upload failed for ${file.name}:`, error);
            toast({
              title: "Upload Failed",
              description: `Failed to upload ${file.name}`,
              variant: "destructive",
            });
          }
        } else {
          // Public files: Use existing 3-step flow but don't finalize
          try {
            const uploadRequest = {
              file_name: file.name,
              mime_type: file.type,
            };

            const uploadResponse = await agentApi.uploadArtifact(
              jobId,
              uploadRequest
            );
            await agentApi.uploadFileToUrl(
              uploadResponse.upload_url,
              file.file
            );

            // Create both blob URL and base64 for immediate display
            let localPreviewUrl: string | undefined;
            let localPreviewBase64: string | undefined;

            if (isPreviewableImage(file.type, file.name)) {
              // Create blob URL for immediate use
              localPreviewUrl = URL.createObjectURL(file.file);

              // Also create base64 for reliable display
              try {
                const reader = new FileReader();
                const base64Promise = new Promise<string>((resolve) => {
                  reader.onload = () => {
                    const result = reader.result as string;
                    resolve(result.split(',')[1]); // Remove data:image/...;base64, prefix
                  };
                  reader.readAsDataURL(file.file);
                });
                localPreviewBase64 = await base64Promise;
              } catch (error) {
                console.error('Failed to create base64 preview:', error);
              }
            }

            console.log("UploadAssetsModal - creating public pending artifact:", {
              file_name: file.name,
              mime_type: file.type,
              file_size: file.size,
              local_preview_url: localPreviewUrl,
              has_base64: !!localPreviewBase64,
              isPreviewableImage: isPreviewableImage(file.type, file.name),
              blobUrlValid: localPreviewUrl ? 'YES' : 'NO'
            });

            pendingArtifacts.push({
              artifact_id: uploadResponse.artifact_id,
              entity_id: jobId,
              entity_type: "job",
              visibility: "public",
              file_name: file.name,
              description: file.description,
              file_path: uploadResponse.file_path,
              readByAgent: file.readByAgent,
              // Additional metadata for better local preview
              mime_type: file.type,
              file_size: file.size,
              local_preview_url: localPreviewUrl,
              local_preview_base64: localPreviewBase64,
            });
          } catch (error) {
            console.error(`Public upload failed for ${file.name}:`, error);
            toast({
              title: "Upload Failed",
              description: `Failed to upload ${file.name}`,
              variant: "destructive",
            });
          }
        }
      }

      // Add pending artifacts to Redux store
      if (pendingArtifacts.length > 0) {
        dispatch(
          addPendingArtifacts({
            tabId: activeTabId,
            artifacts: pendingArtifacts,
          })
        );

        toast({
          title: "Files Uploaded",
          description: `${pendingArtifacts.length} file(s) uploaded and ready to share with agent`,
        });
      }

      // Note: Blob URLs will be cleaned up when the component unmounts or when artifacts are removed

      setTimeout(() => {
        setSelectedFiles([]);
        onOpenChange(false);
      }, 1000);
    } catch (error) {
      console.error("Upload process failed:", error);
    } finally {
      setIsUploading(false);
      setCurrentUploadingIndex(null);
    }
  }, [
    selectedFiles,
    jobId,
    onOpenChange,
    isUploading,
    dispatch,
    activeTabId,
    toast,
  ]);



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[49] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]">
      <div
        className="max-w-[740px] relative w-[95vw] p-0 bg-[#18181A] border border-[#242424] rounded-[16px] overflow-hidden flex flex-col font-['Inter']"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div
          className={cn(
            "flex items-center justify-between p-4  md:p-8",
            selectedFiles.length > 0 ? "md:p-6 border-b border-[#242424] " : ""
          )}
        >
          <div className="flex flex-col gap-1 md:gap-[10px]">
            <h2 className="text-[18px] md:text-[22px] font-medium text-white md:leading-[32px]">
              Attach Files
            </h2>
            <p className="text-[13px] md:text-[15px] text-[#737780] md:leading-[24px]">
              Upload assets and media files that your agent can access and integrate into your code.
            </p>
          </div>
          <button
            type="button"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
            className={cn(
              "p-2 absolute top-6 right-6 bg-[#FFFFFF05] backdrop-blur-lg hover:bg-[#242424] rounded-lg transition-colors",
              isUploading && "opacity-50 cursor-not-allowed"
            )}
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-[#737780]" />
          </button>
        </div>

        {/* Main Content */}
        <div
          className={cn(
            "flex flex-col flex-1 p-4 md:p-8 pt-0 md:pt-0 overflow-hidden overflow-y-auto max-h-[486px]",
            selectedFiles.length > 0 ? "md:p-6 pt-0 md:pt-0" : ""
          )}
        >
          {/* Assets Header */}
          {selectedFiles.length > 0 && (
            <div className="md:pt-4 pt-3 flex items-center justify-between backdrop-blur-lg pb-2 z-[10] bg-[#18181A]">
              <div className="flex items-center gap-2">
                <h3 className="text-[12px] font-nothing uppercase text-[#737780] tracking-[0.8px]">
                  {selectedFiles.length} Files Uploaded
                </h3>
              </div>
              <div className="flex items-center gap-2">
                {/* Upload Assets Button */}
                <TooltipPrimitive.Provider>
                  <TooltipPrimitive.Root>
                    <TooltipPrimitive.Trigger asChild>
                      <button
                        type="button"
                        onClick={handleBrowseClick}
                        disabled={selectedFiles.length >= 5 || isUploading}
                        className={cn(
                          "flex items-center rounded-full gap-2 px-4 py-2 radial-bg text-[#80FFF9] hover:opacity-[0.80] font-medium text-[14px] font-['Inter'] transition-all duration-200 disabled:opacity-20",
                          isUploading && "cursor-not-allowed"
                        )}
                      >
                        <img
                          src={UploadNormal}
                          alt="Upload"
                          className="w-6 h-6"
                        />
                        Upload Files
                      </button>
                    </TooltipPrimitive.Trigger>
                    {(selectedFiles.length >= 5 || isUploading) && (
                      <TooltipPrimitive.Portal>
                        <TooltipPrimitive.Content
                          className="bg-[#272829]/95 backdrop-blur-md border-[#2E2F34] text-white shadow-lg z-[9999] px-3 py-2 rounded-md text-sm"
                          side="bottom"
                          sideOffset={8}
                        >
                          {isUploading
                            ? "Upload in progress..."
                            : "You can only upload 5 items at a time"}
                        </TooltipPrimitive.Content>
                      </TooltipPrimitive.Portal>
                    )}
                  </TooltipPrimitive.Root>
                </TooltipPrimitive.Provider>
              </div>
            </div>
          )}

          {/* Assets List */}
          <div className="flex-1">
            
              <div className="flex flex-col gap-3 md:gap-4 md:mt-4">
                {selectedFiles.map((file, index) => {
                  const FileIcon = getFileIcon(file.type, file.name);
                  const isCurrentlyUploading = currentUploadingIndex === index;
                  const isUploaded = uploadedFiles.has(index);

                  return (
                    <div
                      key={index}
                      className={cn(
                        "relative overflow-clip duration-300 rounded-[12px] h-full group",
                        isCurrentlyUploading
                          ? "border-[#80FFF9] opacity-100"
                          : isUploaded
                          ? "border-[#FFFFFF0F] opacity-100"
                          : "border-[#FFFFFF0F] opacity-100",
                        showCommentInput.has(index) ? "opacity-100 border border-[#FFFFFF1F]" : "border border-transparent"
                      )}
                    >
                      <div className="flex items-center gap-4 p-3 bg-[#1D1D1E]">
                        {/* File Icon/Preview */}
                        <div className="flex items-center justify-center flex-shrink-0 w-10 h-10 overflow-hidden rounded-lg">
                          {isPreviewableImage(file.type, file.name) ? (
                            <img
                              src={createObjectURL(file.file)}
                              alt={file.name}
                              className="object-cover w-full h-full rounded-lg"
                              onError={(e) => {
                                // Fallback to icon if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = "none";
                                const parent = target.parentElement;
                                if (parent) {
                                  const fallbackIcon =
                                    parent.querySelector(".fallback-icon");
                                  if (fallbackIcon) {
                                    (
                                      fallbackIcon as HTMLElement
                                    ).style.display = "block";
                                  }
                                }
                              }}
                            />
                          ) : isPreviewableSVG(file.type, file.name) ? (
                            <img
                              src={createObjectURL(file.file)}
                              alt={file.name}
                              className="w-6 h-6"
                              onError={(e) => {
                                // Fallback to icon if SVG fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = "none";
                                const parent = target.parentElement;
                                if (parent) {
                                  const fallbackIcon =
                                    parent.querySelector(".fallback-icon");
                                  if (fallbackIcon) {
                                    (
                                      fallbackIcon as HTMLElement
                                    ).style.display = "block";
                                  }
                                }
                              }}
                            />
                          ) : getCustomFileIcon(file.type, file.name) ? (
                            <img
                              src={getCustomFileIcon(file.type, file.name)!}
                              alt="File"
                              className={cn("w-10 h-10", getCustomFileIcon(file.type, file.name) === ZipIcon && "p-1")}
                            />
                          ) : (
                            <FileIcon className="w-5 h-5 text-[#FFFFFF60]" />
                          )}

                          {/* Fallback icon (hidden by default, shown when image fails to load) */}
                          {(isPreviewableImage(file.type, file.name) ||
                            isPreviewableSVG(file.type, file.name)) && (
                            <div
                              className="fallback-icon"
                              style={{ display: "none" }}
                            >
                              {getCustomFileIcon(file.type, file.name) ? (
                                <img
                                  src={getCustomFileIcon(file.type, file.name)!}
                                  alt="File"
                                  className="w-5 h-5"
                                />
                              ) : (
                                <FileIcon className="w-5 h-5 text-[#FFFFFF60]" />
                              )}
                            </div>
                          )}
                        </div>

                        {/* File Info */}
                        <div className="flex-1 min-w-0">
                          <h3 className="text-[#E6E6E6] font-medium text-[14px] font-['Inter'] truncate mb-1">
                            {truncateFileName(file.name)}
                          </h3>
                          <div className="flex items-center gap-2">
                            {getFileTypeLabel(file.type, file.name) && (
                              <>
                                <div className="text-[12px] text-white/40 flex items-center gap-[6px]">
                                  <span className="font-['Inter'] font-medium text-[12px]">{getFileTypeLabel(file.type, file.name)}</span>
                                </div>
                                <div className="w-1 h-1 rounded-full bg-[#FFFFFF]/40"></div>
                              </>
                            )}
                            <div className="text-[12px] text-white/50 flex items-center gap-2">
                              <span>{formatFileSize(file.size)}</span>
                            </div>
                          </div>
                        </div>

                        {/* Action Icons */}
                        <div className="flex items-center gap-2">
                          
                          {/* Agent reading capability indicator */}
                          {isVideoFile(file.type, file.name) || isArchiveFile(file.type, file.name) ? (
                            // For video and archive files: show "Agent cannot read" without toggle
                            <div className="flex items-center gap-2">
                              <TooltipPrimitive.Provider delayDuration={0}>
                                <TooltipPrimitive.Root>
                                  <TooltipPrimitive.Trigger asChild>
                                    <div className="flex items-center gap-2 bg-[#FFFFFF0A] text-[#FFFFFF66] px-2 py-1 rounded-full font-medium font-['Inter'] text-[12px]">
                                      
                                      Agent cannot read
                                      <img
                                        alt="Info"
                                        src={InfoSVG}
                                        className="w-4 h-4"
                                      />
                                    </div>
                                  </TooltipPrimitive.Trigger>
                                  <TooltipPrimitive.Portal>
                                    <TooltipPrimitive.Content
                                      className="bg-[#FFFFFF] backdrop-blur-md text-[#0F0F10] shadow-lg z-[9999] font-['Inter'] font-medium px-3 py-2 rounded-md text-[12px]"
                                      side="top"
                                      sideOffset={8}
                                    >
                                      The agent can't read this file type, but it can use the file in your application.
                                    </TooltipPrimitive.Content>
                                  </TooltipPrimitive.Portal>
                                </TooltipPrimitive.Root>
                              </TooltipPrimitive.Provider>
                            </div>
                          ) : supportsBase64Conversion(file) ? (
                            // For supported files: show toggle
                            <div className="flex items-center gap-2">
                              <TooltipPrimitive.Provider delayDuration={0}>
                                <TooltipPrimitive.Root>
                                  <TooltipPrimitive.Trigger asChild>
                                    <div
                                      className={cn(
                                        "flex items-center gap-2 text-[#F3CA5FCC] px-2 py-1 bg-[#F3CA5F14] rounded-full font-medium font-['Inter'] text-[12px]",
                                        file.readByAgent
                                          ? "opacity-100"
                                          : "bg-[#FFFFFF0A] text-[#FFFFFF66]"
                                      )}
                                    >
                                      <img
                                        alt="Gold Coin"
                                        src={GoldCoinSVG}
                                        className={cn(
                                          "w-4 h-4",
                                          file.readByAgent
                                            ? "opacity-100"
                                            : "hidden"
                                        )}
                                      />{" "}
                                      {file.readByAgent
                                        ? "Agent will read"
                                        : "Agent will not read"}
                                      <img
                                        alt="Info"
                                        src={InfoSVG}
                                        className={cn(
                                          "w-4 h-4",
                                          file.readByAgent ? "hidden" : ""
                                        )}
                                      />
                                    </div>
                                  </TooltipPrimitive.Trigger>
                                  <TooltipPrimitive.Portal>
                                    <TooltipPrimitive.Content
                                      className="bg-[#FFFFFF] backdrop-blur-md text-[#0F0F10] shadow-lg z-[9999] font-['Inter'] font-medium px-3 py-2 rounded-md text-[12px]"
                                      side="top"
                                      sideOffset={8}
                                    >
                                      {file.readByAgent
                                        ? <span className="font-['Inter'] font-medium">This file will be added to the agent's context and <br/> will use credits based on its size.</span>
                                        : "This file will not be added to the agent's context."}
                                    </TooltipPrimitive.Content>
                                  </TooltipPrimitive.Portal>
                                </TooltipPrimitive.Root>
                              </TooltipPrimitive.Provider>
                              <CustomSwitch
                                checked={file.readByAgent}
                                onCheckedChange={(checked) =>
                                  handleSendToAgentToggle(index, checked)
                                }
                                disabled={
                                  isUploading ||
                                  isCurrentlyUploading ||
                                  !supportsBase64Conversion(file)
                                }
                              />
                            </div>
                          ) : null}
                          <button
                            type="button"
                            onClick={(e) => handleCommentToggle(index, e)}
                            disabled={
                              isUploading || isCurrentlyUploading || isUploaded
                            }
                            className={cn(
                              "p-2 hover:bg-[#FFFFFF0F] rounded-lg transition-colors group",
                              showCommentInput.has(index)
                                ? "opacity-100 bg-[#FFFFFF0F]"
                                : "opacity-60 hover:opacity-100",
                              (isUploading ||
                                isCurrentlyUploading ||
                                isUploaded) &&
                                "opacity-30 cursor-not-allowed"
                            )}
                            title={
                              file.description ? "Edit comment" : "Add comment"
                            }
                          >
                            <img
                              src={
                                file.description ? CommentedIcon : CommentIcon
                              }
                              alt={file.description ? "Commented" : "Comment"}
                              className="w-6 h-6 group-hover:hidden"
                            />
                            <img
                              src={
                                !file.description ? CommentIcon : CommentedIcon
                              }
                              alt="Comment Active"
                              className="hidden w-6 h-6 group-hover:block"
                            />
                          </button>
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(index)}
                            disabled={isUploading || isCurrentlyUploading}
                            className={cn(
                              "group/delete p-2 rounded-lg transition-colors hover:bg-[#ED5B5B0F]",
                              (isUploading || isCurrentlyUploading) &&
                                "opacity-30 cursor-not-allowed"
                            )}
                            title="Delete"
                          >
                            <img
                              src={DeleteIcon}
                              alt="Delete"
                              className="w-6 h-6 group-hover/delete:hidden"
                            />
                            <img
                              src={DeleteActiveIcon}
                              alt="Delete Active"
                              className="hidden w-6 h-6 group-hover/delete:block"
                            />
                          </button>
                        </div>
                      </div>

                      {/* Description Input - Only show when comment button is clicked */}
                      <AnimatePresence>
                        {showCommentInput.has(index) && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{
                              duration: 0.3,
                              ease: "easeInOut",
                            }}
                            className="bg-[#18181A] overflow-hidden"
                          >
                            <input
                              type="text"
                              placeholder="Add comments for agent"
                              value={file.description || ""}
                              onChange={(e) =>
                                handleDescriptionChange(index, e.target.value)
                              }
                              disabled={isUploading}
                              className={cn(
                                "w-full bg-transparent pl-4 py-4 font-medium text-[14px] text-[#FFFFFF60] placeholder-[#4A4A4D] font-['Inter'] focus:outline-none transition-colors",
                                isUploading && "opacity-50 cursor-not-allowed"
                              )}
                              autoFocus
                            />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  );
                })}
              </div>
          
          </div>
        </div>

        {/* Footer */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-end gap-4 p-6 border-t border-[#242424]/60">
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              disabled={isUploading}
              className={cn(
                "px-6 py-3 bg-[#272729] hover:bg-[#444] rounded-full text-[#DCDCE5] font-medium text-[16px] transition-all duration-200",
                isUploading && "opacity-50 cursor-not-allowed"
              )}
            >
              Cancel
            </button>

            <button
              type="button"
              onClick={handleUploadAll}
              disabled={!jobId || isUploading}
              className={cn(
                "md:px-6 p-3 md:py-3 bg-white hover:bg-white/90 rounded-full text-[#0E0E0F] font-semibold text-[14px]  md:text-[16px] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",
                isUploading && "cursor-not-allowed"
              )}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Attaching...
                </>
              ) : (
                <>
                  <img src={AttachIcon} alt="Share" className="w-6 h-6 rotate-45 invert" />
                  Attach {selectedFiles.length} Files
                </>
              )}
            </button>
          </div>
        )}

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileInputChange}
          disabled={isUploading}
          className="hidden"
          aria-label="Upload files"
        />
      </div>
    </div>
  );
}
