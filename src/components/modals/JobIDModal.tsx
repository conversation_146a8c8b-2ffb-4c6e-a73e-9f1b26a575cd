import React from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { useLazyGetJobQuery } from '@/store/api/apiSlice';



interface JobIDModalProps {
    isJobIdDialogOpen: boolean,
    setIsJobIdDialogOpen: React.Dispatch<React.SetStateAction<boolean>>,
    jobIdInput: string,
    setJobIdInput: React.Dispatch<React.SetStateAction<string>>,
    handleJobClick: (job: any) => void
}

function JobIDModal({
    isJobIdDialogOpen,
    setIsJobIdDialogOpen,
    jobIdInput,
    setJobIdInput,
    handleJobClick,
}: JobIDModalProps) {
    const [getJobDetails] = useLazyGetJobQuery();

    const handleJobIdSubmit = async () => {
        if (!jobIdInput.trim()) return;

        try {
            const response = await getJobDetails(jobIdInput.trim());
            if ('error' in response) {
                console.error("Error fetching job details");
                return;
            }
            const job = response.data;
            if (job) {
                handleJobClick(job);
                setIsJobIdDialogOpen(false);
                setJobIdInput("");
            }
        } catch (error) {
            console.error("Error fetching job details:", error);
            // You might want to show an error toast here
        }
    };
    return (
        <>
            <Dialog open={isJobIdDialogOpen} onOpenChange={setIsJobIdDialogOpen}>
                <DialogContent className="w-[700px]">
                    <DialogHeader>
                        <DialogTitle>Enter Job ID</DialogTitle>
                        <DialogDescription>
                            Enter a job ID to view its details and navigate to chat
                        </DialogDescription>
                    </DialogHeader>
                    <div className="px-3">
                        <Input
                            placeholder="Enter job ID"
                            className="my-2 h-14"
                            value={jobIdInput}
                            onChange={(e) => setJobIdInput(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                    handleJobIdSubmit();
                                }
                            }}
                        />
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsJobIdDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleJobIdSubmit}>
                            View Job
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}

export default JobIDModal