import { Dialog, DialogContent } from "@/components/ui/dialog";
import { X } from "lucide-react";

import CreditedSVG from "@/assets/referral/credited.svg";
import EmergentButton from "@/components/EmergentButton";
import { useCredits } from "@/contexts";
import { useEffect } from "react";
import { useReferralLocalStorage } from "@/hooks/useLocalStorage";

interface CreditCreditedModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  reward?: number;
}

export function CreditCreditedModal({
  isOpen,
  onOpenChange,
  reward = 5,
}: CreditCreditedModalProps) {


  const {refreshCredits} = useCredits();
  const { clearReferralData } = useReferralLocalStorage();

  useEffect(()=>{
    refreshCredits();
  },[isOpen])

  const handleClose = () => {
    // Clean up referral-related localStorage items including modal data
    clearReferralData();
    onOpenChange(false);
  };
  
  const buttonStyle = {
    background: `
      linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(0, 0, 0, 1)),
      rgba(243, 202, 95, 1)
    `,
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundBlendMode: "overlay, normal",
    WebkitBackgroundClip: "text",
    backgroundClip: "text",
    WebkitTextFillColor: "transparent",
    color: "transparent",
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95%]  md:max-w-[600px] bg-[#1C1C1F] text-white border-[#2E2F34] p-6 md:p-12">
        <div className="flex justify-end">
          <button
            type="button"
            title="Close"
            onClick={handleClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={24} />
          </button>
        </div>
        <div className="flex flex-col items-center justify-center gap-4">
          <img src={CreditedSVG} alt="Credited" className="w-20 h-20 max-md:w-12 max-md:h-12" />
          <div className="flex flex-col items-center justify-center gap-2 mb-[3rem] max-md:mb-[1rem]">
            <h3 className="text-[20px] md:text-[32px] text-center font-medium text-white">
              You have earned <br />{" "}
              <span className="" style={buttonStyle}>
                {reward} credits
              </span>
            </h3>
            <p className="text-[14px] md:text-[16px] text-[#737780]">
             Thanks to the user that referred you.
            </p>
          </div>
          <EmergentButton
            onClick={handleClose}
            className="w-full h-[45px]  md:h-full max-w-[200px] text-nowrap  md:max-w-[250px] mx-auto"
            type="button"
            variant="light"
          >
            Start Building
          </EmergentButton>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default CreditCreditedModal;
