import { X, Arrow<PERSON>eft } from "lucide-react";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

import { cn } from "@/lib/utils";

import { useAlert } from "@/contexts/AlertContext";

import LinkSvg from "@/assets/referral/link.svg"
import GrayGift from "@/assets/referral/gray-gift.svg"
import Share from "@/assets/referral/share.svg"
import CoinSVG from "@/assets/referral/coin.svg"
import ExternalSVG from "@/assets/referral/external.svg"

import ReferralBackground from "@/assets/referral/referral.png";
import { useReferral, getReferralShareUrl } from "@/hooks/useReferral";
import { trackReferralEvent } from "@/services/postHogService";
import { useAuth } from "@/contexts";

// Terms & Conditions content component
const TermsAndConditionsContent = ({ onBack }: { onBack: () => void }) => {
  const termsData = [
    {
      title: "New users only",
      description: "This referral program is exclusively for first-time users who haven't previously created an account."
    },
    {
      title: "Reward eligibility",
      description: "You earn credits when someone signs up using your referral code and purchases their first subscription."
    },
    {
      title: "Program flexibility",
      description: "We may modify, suspend, or discontinue this referral program at any time without prior notice."
    },
    {
      title: "Appropriate sharing",
      description: "Share your referral link responsibly in relevant contexts. Spam, misleading promotions, or inappropriate distribution may result in account penalties."
    },
    {
      title: "Quality control",
      description: "We monitor for fraudulent activity and may withhold rewards or suspend accounts that violate our community standards."
    },
    {
      title: "One reward per referral",
      description: "Each successfully referred user earns you credits once. Multiple referrals from the same user are not eligible for additional rewards."
    }
  ];

  return (
    <motion.div
      className="relative flex flex-col w-full gap-2 pb-4 md:gap-4 md:pb-8"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <motion.div
        className="sticky top-0 p-4 bg-[#18181A] md:px-6 md:pt-6 z-[10]"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-[#18181A] flex items-center gap-4 ">
          <button
            type="button"
            onClick={onBack}
            className="w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
            aria-label="Go back"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <h2 className="text-white/80 text-[18px] md:text-[24px] font-semibold">Terms & Conditions</h2>
        </div>
      </motion.div>

      {/* Terms content */}
      <motion.div
        className="px-4 space-y-3 md:space-y-6 md:px-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          staggerChildren: 0.1,
          delayChildren: 0.3
        }}
      >
        {termsData.map((term, index) => (
          <motion.div
            key={index}
            className="space-y-3"
            initial={{ opacity: 0, y: 20, x: -10 }}
            animate={{ opacity: 1, y: 0, x: 0 }}
            transition={{
              duration: 0.4,
              delay: index * 0.1,
              ease: "easeOut"
            }}
          >
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5"><div className=" w-2 h-2 bg-white rounded-[1.5px]" /></div>
              <div className="flex flex-col gap-1">
                <h3 className="text-white text-[16px] font-medium ">
                  {term.title}
                </h3>
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  {term.description}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
};

export const ReferralModal = ({
  isOpen,
  onOpenChange,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const { bannerHeight } = useAlert();
  const heightDiff = bannerHeight + 56;
  const [isCopied, setIsCopied] = useState(false);
  const [showTerms, setShowTerms] = useState(false);

  const { referralInfo, isLoading } = useReferral();
  const { user } = useAuth();

  // Reset to main view when modal opens
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setShowTerms(false);
    }
    onOpenChange(open);
  };

  // Generate referral link from user's referral code
  const referralLink = referralInfo?.referralCode
    ? getReferralShareUrl(referralInfo.referralCode)
    : "Not Available";

  const HOWITWORKS = [
    {
      element: (
        <div className="max-md:text-[13px] text-[16px] font-medium text-white">
          Share your <span className="text-[#80FFF9]">referral link</span>
        </div>
      ),
      icon: Share,
    },
    {
      element: (
        <div className="max-md:text-[13px] text-[16px] font-medium text-white">
          They get{" "}
          <span className=" text-[#80FFF9]">extra 5 credits</span> when they join.
        </div>
      ),
      icon: CoinSVG,
    },
    {
      element: (
        <div className="max-md:text-[13px] text-[16px] font-medium text-white">
          You earn<span className="text-[#80FFF9]"> 20 credits</span> on their first subscription.
        </div>
      ),
      icon: GrayGift,
    },
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          style={{
            height: `calc(100vh - ${heightDiff}px)`,
            top: `${heightDiff}px`,
          }}
          className={cn("fixed inset-0 w-full z-[49] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]")}
          onClick={() => handleOpenChange(false)}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
        >
          <motion.div
            className={cn(
              "p-0 max-md:max-w-[95vw] overflow-y-auto w-full max-w-[600px] mx-auto bg-[#18181A] border border-[#242424] max-h-[80dvh] rounded-[20px] font-['Inter']"
            )}
            onClick={(e) => e.stopPropagation()}
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
          >
            <AnimatePresence mode="wait">
              {showTerms ? (
                <TermsAndConditionsContent
                  key="terms"
                  onBack={() => setShowTerms(false)}
                />
              ) : (
                <motion.div
                  key="main"
                  className="relative flex flex-col w-full gap-6 pb-6 md:pb-8"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                >
          {/* Header with gift icons and close button */}
          <motion.div
            className="relative px-4 pt-4 md:px-6 md:pt-6"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.15 }}
          >
            {/* Background pattern/grid effect */}
            <div>
              <img
                src={ReferralBackground}
                alt="Referral Background"
                className="object-cover w-full h-full"
              />
            </div>

            {/* Close button */}
            <button
              type="button"
              onClick={() => handleOpenChange(false)}
              className="absolute top-5 md:top-8 md:right-8 right-5 max-md:w-8 max-md:h-8 w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
              aria-label="Close modal"
            >
              <X className="w-5 h-5 max-md:w-4 max-md:h-4" />
            </button>
          </motion.div>

          <motion.div
            className="px-4 md:px-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <motion.h3
              className="text-[#999999] text-[16px] font-medium mb-3 md:mb-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.25 }}
            >
              How it works:
            </motion.h3>

            <motion.div
              className="space-y-[10px]"
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.15,
                    delayChildren: 0.3
                  }
                }
              }}
            >
              {HOWITWORKS.map((step, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-4 bg-[#FFFFFF08] max-md:p-4 p-6 py-4 rounded-[8px]"
                  variants={{
                    hidden: {
                      opacity: 0,
                      x: -20,
                      y: 10
                    },
                    visible: {
                      opacity: 1,
                      x: 0,
                      y: 0,
                      transition: {
                        duration: 0.5,
                        ease: "easeOut"
                      }
                    }
                  }}
                >
                   <motion.img
                     src={step.icon}
                     alt="Step"
                     className="w-6 h-6 max-md:w-5 max-md:h-5"
                     initial={{ scale: 0, rotate: -180 }}
                     animate={{ scale: 1, rotate: 0 }}
                     transition={{
                       duration: 0.4,
                       delay: 0.45 + index * 0.15,
                       ease: "easeOut"
                     }}
                   />

                  <motion.div
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: 0.5 + index * 0.15
                    }}
                  >
                    {step.element}
                  </motion.div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
          <motion.div
            className="px-4 md:px-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.55 }}
          >
            <h3 className="text-[#999999] font-medium mb-3">
              Your invite link :
            </h3>

            <div className="flex items-center gap-3 max-md:pl-3 p-4 pr-2 bg-[#18181A] border border-[#333333] rounded-[10px] max-h-[56px]">
              <img
                src={LinkSvg}
                alt="Link"
                className="w-5 h-5 text-[#8A8F98]"
              />
              <span className="text-[#FFFFFFCC] font-medium truncate flex-1 font-['Inter'] tracking-[0.4px]">
                {isLoading ? "Loading..." : referralLink}
              </span>
              <button
                type="button"
                onClick={async () => {
                  // Track the copy link click
                  trackReferralEvent('copy_link_clicked', {
                    userId: user?.id,
                    userEmail: user?.email,
                    referralCode: referralInfo?.referralCode,
                    linkUrl: referralLink
                  });

                  try {
                    await navigator.clipboard.writeText(referralLink);
                    setIsCopied(true);
                    setTimeout(() => {
                      setIsCopied(false);
                    }, 1000);
                  } catch (error) {
                    console.error('Failed to copy referral link:', error);
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = referralLink;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    setIsCopied(true);
                    setTimeout(() => {
                      setIsCopied(false);
                    }, 1000);
                  }
                }}
                title="Copy Link"
                className="px-4 py-2 font-bold text-black transition-colors max-md:text-[13px]   bg-white rounded-lg hover:bg-gray-300 disabled:opacity-50"
                disabled={isLoading || !referralInfo?.referralCode}
              >
                {isCopied ? "Copied" : "Copy Link"}
              </button>
            </div>
          </motion.div>
          <motion.div
            className="flex px-4 md:px-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
          >
            {/* Terms link */}
            <button
              type="button"
              onClick={() => setShowTerms(true)}
              className="flex group items-center gap-2 max-md:text-[14px] text-[#FFFFFF]/80 transition-all duration-100 ease-in-out hover:text-white cursor-pointer"
            >
              <span>View Terms & Conditions</span>
              <img src={ExternalSVG} alt="External" className="w-5 h-5 transition-all duration-100 ease-in-out max-md:w-4 max-md:h-4 opacity-60 group-hover:opacity-100" />
            </button>
          </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
