import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { createPortal } from "react-dom";
import { useLocation } from "react-router-dom";
import {
  X,
  Loader2,
  ChevronDown,
  Plus,
  RefreshCw,
  Search,
  Check,
  ExternalLink,
  GitBranchIcon,
  Copy,
} from "lucide-react";
import { Dialog, DialogContent, DialogClose, DialogFooter } from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
// RTK Query imports
import {
  useGetGitHubUserDetailsQuery,
  useGetGitHubInstallationsQuery,
  useGetGitHubRepositoriesQuery,
  useGetGitHubBranchesQuery,
  useCreateGitHubRepositoryMutation,
  usePushToGitHubMutation,
  GitHubBranch,
} from "@/store/api/apiSlice";
import { useToast } from "@/hooks/use-toast";
import GithubIcon from "@/assets/github.svg";
import GithubIconFull from "@/assets/menu-icons/github.svg";
// ConnectedGitHubIcon removed as it's not used
import RepositoryIcon from "@/assets/repo.svg";
import BranchIcon from "@/assets/branch.svg";
import InfoSquareIcon from "@/assets/info-square.svg";
import WarningPurple from "@/assets/warning-purple.svg";
import GitubGreenArrow from "@/assets/github/arrow_green.svg"
import SaveCloudIcon from "@/assets/save-cloud.svg";
import ShieldLock from "@/assets/github/shield_lock.svg";
import { useGitHub } from "@/hooks/useGitHubAPI";
import { Badge } from "../ui/badge";
// PulseDot removed as it's not used

import ConnectWithGithub from "@/assets/github_connect.svg";
import { cn } from "@/lib/utils";
import {
  isValidGitName as validateGitName,
  replaceSpacesWithHyphens,
} from "@/lib/utils/gitValidation";
import { processAndSortGitHubInstallations } from "@/lib/utils/gitHubUtils";
import {
  handleGitHubPushError,
  generateProposedBranchName,
} from "../../lib/utils/gitHubErrorHandler";
import {
  validateRepositoryName,
  validateBranchName,
  validateProposedBranchName,
  validateFormSubmission,
  type ValidationResult,
} from "../../lib/utils/gitHubValidation";

// State management types
interface DropdownState {
  showBranchDropdown: boolean;
  showCustomBranchInput: boolean;
  showRepositoryDropdown: boolean;
  showInstallationDropdown: boolean;
}
import LineSVG from "@/assets/Line.svg";
import OrangeError from "@/assets/github/emergency_home.svg";
import RedError from "@/assets/github/emergency_home_red.svg";
import GitBranch from "@/assets/github/git_branch.svg";
import GithubArrow from "@/assets/github/github_arrow.svg";
import GithubGreenDark from "@/assets/github/GithubGreenDark.svg";
import GithubBack from "@/assets/github/github_back.svg";
import GitBranchDark from "@/assets/github/GitBranchDark.svg";
import PurpleError from "@/assets/github/emergency_home_purple.svg";
import CopyButton from "../CopyButton";
import { useAuth } from "@/contexts";

// Portal-based dropdown component for branch selection
interface PortalDropdownProps {
  isOpen: boolean;
  triggerRef: React.RefObject<HTMLDivElement>;
  children: React.ReactNode;
  className?: string;
  scrollableContainerRef?: React.RefObject<HTMLDivElement>;
}

function PortalDropdown({
  isOpen,
  triggerRef,
  children,
  className = "",
  scrollableContainerRef,
}: PortalDropdownProps) {
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });

  const updatePosition = useCallback(() => {
    if (isOpen && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();

      // Check if trigger element is visible in viewport
      if (rect.width === 0 || rect.height === 0) {
        return; // Don't position if trigger is not visible
      }

      // Ensure dropdown doesn't go off-screen
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      const dropdownMaxHeight = 300; // max-h-[300px]
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;
      const padding = 8; // Padding from viewport edges
      const minSpaceRequired = 120; // Minimum space needed to show below
      const preferredHeight = Math.min(dropdownMaxHeight, 200); // Preferred dropdown height

      let top = rect.bottom + 4;
      let showAbove = false;

      // Only show above if there's really insufficient space below AND much more space above
      if (spaceBelow < minSpaceRequired && spaceAbove > preferredHeight + 50) {
        top = rect.top - preferredHeight - 4;
        showAbove = true;
      }

      // Ensure dropdown width doesn't exceed viewport width
      const maxWidth = viewportWidth - padding * 2;
      const dropdownWidth = Math.min(rect.width, maxWidth);

      // Calculate final top position with bounds checking
      let finalTop = top;
      if (showAbove) {
        // When showing above, ensure it doesn't go above viewport
        finalTop = Math.max(padding, top);
      } else {
        // When showing below, ensure it doesn't go below viewport
        const availableHeight = Math.min(preferredHeight, spaceBelow - padding);
        finalTop = Math.min(top, viewportHeight - availableHeight - padding);
      }

      const newPosition = {
        top: finalTop,
        left: Math.max(
          padding,
          Math.min(rect.left, viewportWidth - dropdownWidth - padding)
        ),
        width: dropdownWidth,
      };

      // Temporary debug logging for branch dropdown positioning
      if (className === "branch-dropdown") {
        console.log("Branch dropdown positioning:", {
          triggerRect: rect,
          spaceBelow,
          spaceAbove,
          showAbove,
          calculatedTop: top,
          finalTop,
          newPosition,
        });
      }

      setPosition(newPosition);
    }
  }, [isOpen, triggerRef]);

  useEffect(() => {
    updatePosition();
  }, [updatePosition]);

  // Update position on scroll and resize
  useEffect(() => {
    if (!isOpen) return;

    const handleScroll = () => updatePosition();
    const handleResize = () => updatePosition();

    // Listen to both window scroll and modal container scroll
    window.addEventListener("scroll", handleScroll, true); // Use capture to catch all scroll events
    window.addEventListener("resize", handleResize);

    // Store reference to the scrollable container for cleanup
    const scrollableContainer = scrollableContainerRef?.current;
    if (scrollableContainer) {
      scrollableContainer.addEventListener("scroll", handleScroll);
    }

    return () => {
      window.removeEventListener("scroll", handleScroll, true);
      window.removeEventListener("resize", handleResize);

      // Use stored reference for cleanup to avoid stale closure issues
      if (scrollableContainer) {
        scrollableContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, [isOpen, updatePosition, scrollableContainerRef]);

  if (!isOpen) return null;

  return createPortal(
    <div
      className={`fixed z-[9999] bg-[#131314] border border-[#242424] rounded-md shadow-lg pointer-events-auto ${className}`}
      style={{
        top: position.top,
        left: position.left,
        width: position.width,
        maxHeight: "300px",
        overflowY: "auto",
        overflowX: "hidden",
      }}
      data-portal-dropdown={className}
      onMouseDown={(e) => {
        // Prevent clicks from bubbling up to the global click handler
        e.stopPropagation();
      }}
      onClick={(e) => {
        // Prevent clicks from bubbling up to the global click handler
        e.stopPropagation();
      }}
      onFocus={(e) => {
        // Prevent focus events from bubbling up
        e.stopPropagation();
      }}
      onWheel={(e) => {
        // Allow scrolling within the dropdown
        e.stopPropagation();
      }}
    >
      {children}
    </div>,
    document.body
  );
}

interface GitHubPushModalProps {
  podIsPaused?: boolean;
  isOpen: boolean;
  lastGithubUsed?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null;
  onOpenChange: (open: boolean) => void;
  jobId?: string;
  onSuccess?: (repoDetails?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  }) => void;
}

export function GitHubPushModal({
  podIsPaused,
  isOpen,
  onOpenChange,
  jobId,
  lastGithubUsed,
  onSuccess,
}: GitHubPushModalProps) {
  // Start with just dropdown state grouping for now
  const [dropdownState, setDropdownState] = useState<DropdownState>({
    showBranchDropdown: false,
    showCustomBranchInput: false,
    showRepositoryDropdown: false,
    showInstallationDropdown: false,
  });

  // Helper function for dropdown state updates
  const updateDropdownState = (updates: Partial<DropdownState>) => {
    setDropdownState((prev) => ({ ...prev, ...updates }));
  };
  const {user} = useAuth();

  // Keep individual states for now (will group in subsequent steps)
  const [branchName, setBranchName] = useState("main");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [needsForcePush, setNeedsForcePush] = useState(false);
  const [branches, setBranches] = useState<GitHubBranch[]>([]);
  const [customBranchName, setCustomBranchName] = useState("");
  const [repoNotFound, setRepoNotFound] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [selectedInstallation, setSelectedInstallation] = useState<string>("");
  const [selectedRepository, setSelectedRepository] = useState<string>("");
  const [isCreatingNewRepo, setIsCreatingNewRepo] = useState(false);
  const [newRepoName, setNewRepoName] = useState("");
  const [pushError, setPushError] = useState<string | null>(null);
  const [showConflictResolution, setShowConflictResolution] = useState(false);
  const [proposedBranchName, setProposedBranchName] = useState("");
  const [showForcePushConfirmation, setShowForcePushConfirmation] =
    useState(false);
  const [conflictDetected, setConflictDetected] = useState(false);
  const [conflictedRepoInfo, setConflictedRepoInfo] = useState<{
    repository: string;
    branch: string;
    owner: string;
  } | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successDetails, setSuccessDetails] = useState<{
    branch: string;
    repo: string;
    owner: string;
    isNewRepo?: boolean;
    isNewBranch?: boolean;
  } | null>(null);
  const [secretProtectionError, setSecretProtectionError] = useState<{
    fullError: string;
    learnMoreUrl?: string;
    isExpanded: boolean;
  } | null>(null);


  // Refs for dropdown positioning
  const branchInputRef = useRef<HTMLDivElement>(null);
  const installationInputRef = useRef<HTMLDivElement>(null);
  const repositoryInputRef = useRef<HTMLDivElement>(null);

  // Ref for scrollable container
  const scrollableContainerRef = useRef<HTMLDivElement>(null);

  // Define routes where we should NOT call user details API
  const location = useLocation();
  const authRoutes = ['/verify', '/activate', '/reset-password', '/login', '/register', '/oauth'];
  const isOnAuthRoute = authRoutes.some(route => location.pathname.startsWith(route));

  // RTK Query hooks
  const { data: userDetails, refetch: refetchUserDetails } =
    useGetGitHubUserDetailsQuery(undefined, {
      skip: !isOpen || isOnAuthRoute || !user,
    });

  const {
    data: installationsData,
    isLoading: isLoadingInstallations,
    refetch: refetchInstallations,
  } = useGetGitHubInstallationsQuery(undefined, {
    skip: !isOpen || !userDetails?.github?.authorized,
  });

  // Derive account login from current selection
  const currentAccountLogin = useMemo(() => {
    if (!selectedInstallation || !installationsData) return "";
    const installation = installationsData.find(
      (i: any) => i.installation_id === selectedInstallation
    );
    return installation?.account_login || "";
  }, [selectedInstallation, installationsData]);

  const {
    data: repositoriesData,
    isLoading: isLoadingRepositories,
    refetch: refetchRepositories,
  } = useGetGitHubRepositoriesQuery(currentAccountLogin, {
    skip: !selectedInstallation || !currentAccountLogin,
  });

  // Create a stable query argument that changes when we need to refetch
  const branchesQueryArg = useMemo(() => {
    if (!selectedRepository || isCreatingNewRepo || !currentAccountLogin) {
      return null;
    }
    return { accountLogin: currentAccountLogin, repoName: selectedRepository };
  }, [selectedRepository, isCreatingNewRepo, currentAccountLogin]);

  const {
    data: branchesData,
    isFetching: isLoadingBranches,
    refetch: refetchBranches,
  } = useGetGitHubBranchesQuery(
    branchesQueryArg || { accountLogin: "", repoName: "" },
    {
      skip: !branchesQueryArg,
    }
  );

  // Helper functions (defined after RTK Query hooks to avoid dependency issues)
  const getAccountLogin = () => {
    return currentAccountLogin;
  };

  const getRepoName = () => {
    if (isCreatingNewRepo) {
      return newRepoName;
    }
    const repo = repositoriesData?.find(
      (r: any) => r.name === selectedRepository
    );
    return repo?.name || "";
  };

  // RTK Query mutations
  const [createRepository, { isLoading: isCreatingRepo }] =
    useCreateGitHubRepositoryMutation();
  const [pushToGitHub, { isLoading: isPushing }] = usePushToGitHubMutation();

  // Local loading state for immediate feedback
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Track when we're in the process of creating a repository to prevent validation errors
  const [isCreatingRepoInProgress, setIsCreatingRepoInProgress] =
    useState(false);

  // Process installations data to identify primary account and sort
  const installations = useMemo(() => {
    return processAndSortGitHubInstallations(
      installationsData,
      userDetails?.github?.account_name
    );
  }, [installationsData, userDetails?.github?.account_name]);

  // Derived data
  const repositories = repositoriesData || [];
  const isConnected = userDetails?.github?.authorized || false;

  const { toast } = useToast();
  const { redirectToGitHubInstallation, isConnecting, setIsConnecting } =
    useGitHub();

  const reset = () => {
    // Reset dropdown state
    setDropdownState({
      showBranchDropdown: false,
      showCustomBranchInput: false,
      showRepositoryDropdown: false,
      showInstallationDropdown: false,
    });

    // Reset individual states
    setNeedsForcePush(false);
    setBranchName("main");
    setBranches([]);
    setCustomBranchName("");
    setRepoNotFound(false);
    setInitialLoading(true);
    setSelectedInstallation("");
    setSelectedRepository("");
    setIsCreatingNewRepo(false);
    setNewRepoName("");
    setSearchQuery("");
    setPushError(null);
    setShowConflictResolution(false);
    setProposedBranchName("");
    setShowForcePushConfirmation(false);
    setConflictDetected(false);
    setConflictedRepoInfo(null);
    setShowSuccessModal(false);
    setSuccessDetails(null);
    setSecretProtectionError(null);
    setIsSubmitting(false);
    setIsCreatingRepoInProgress(false);
  };

  // Reset form when modal closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // If we're closing the modal and we have success details, call onSuccess
      if (showSuccessModal && successDetails && onSuccess) {
        const repoDetails = {
          branch: successDetails.branch,
          repo: successDetails.repo,
          owner: successDetails.owner,
          provider: "github",
        };
        onSuccess(repoDetails);
      }
      reset();
    }
    onOpenChange(open);
  };

  // Memoized conflict detection logic
  const handleConflictDetection = useCallback(() => {
    if (pushError && pushError.includes("Conflict Detected")) {
      setNeedsForcePush(true);
      setShowConflictResolution(true);
      // Generate a proposed branch name based on current date
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, "");
      const timeStr =
        now.toTimeString().slice(0, 2) + now.toTimeString().slice(3, 5);
      setProposedBranchName(`conflict_${dateStr}_${timeStr}`);
    } else {
      setNeedsForcePush(false);
      setShowConflictResolution(false);
      setProposedBranchName("");
    }
  }, [pushError]);

  // Check if error indicates need for force push
  useEffect(() => {
    handleConflictDetection();
  }, [handleConflictDetection]);

  // Handle modal opening, initial setup, and user details loading
  useEffect(() => {
    if (!isOpen) return;

    setInitialLoading(true);
    setNeedsForcePush(false);
    setBranchName("main");

    // If we already have user details, clear loading quickly
    if (userDetails) {
      const quickTimeout = setTimeout(() => {
        setInitialLoading(false);
      }, 500);
      return () => clearTimeout(quickTimeout);
    }

    // Otherwise, set a timeout to prevent infinite loading (5 seconds max)
    const loadingTimeout = setTimeout(() => {
      setInitialLoading(false);
    }, 5000);

    return () => clearTimeout(loadingTimeout);
  }, [isOpen, userDetails]);

  // Memoized function to reset repository state
  const resetRepositoryState = useCallback(() => {
    setSelectedRepository("");
    setSearchQuery("");
    setBranches([]);
    setIsCreatingNewRepo(false);
    setNewRepoName("");
    setRepoNotFound(false);
    setBranchName("main");

    // Force the input field to clear by setting an empty value
    setTimeout(() => {
      const repoInput = document.querySelector(
        ".repository-input input"
      ) as HTMLInputElement;
      if (repoInput) {
        repoInput.value = "";
      }
    }, 0);
  }, []);

  // Monitor installation changes to reset repository-related state
  useEffect(() => {
    if (selectedInstallation) {
      resetRepositoryState();
    }
  }, [selectedInstallation, resetRepositoryState]);

  // Reset connecting state if we're connected but still showing connecting UI
  useEffect(() => {
    if (isConnected && isConnecting) {
      setIsConnecting(false);
      // Don't force reload the modal - just let the UI update naturally
    }
  }, [isConnected, isConnecting, setIsConnecting]);

  // Memoized branch selection logic
  const selectedBranchName = useMemo(() => {
    if (
      !branchesData ||
      !Array.isArray(branchesData) ||
      branchesData.length === 0
    ) {
      return "main"; // Default fallback
    }

    // If we have lastGithubUsed data, try to use that branch
    if (lastGithubUsed) {
      const branch = branchesData.find(
        (b: any) => b.name === lastGithubUsed.branch
      );
      if (branch) {
        return branch.name;
      }
    }

    // Auto-select default branch (usually 'main' or 'master') if available
    const defaultBranch = branchesData.find(
      (b: any) => b.name === "main" || b.name === "master"
    );
    if (defaultBranch) {
      return defaultBranch.name;
    }

    // Use first available branch
    return branchesData[0].name;
  }, [branchesData, lastGithubUsed]);

  // Update branches when branchesData changes
  useEffect(() => {
    console.log("branchesData changed:", {
      branchesData,
      isArray: Array.isArray(branchesData),
      length: branchesData?.length,
      selectedBranchName,
      isCreatingNewRepo,
      selectedRepository,
    });

    if (branchesData && Array.isArray(branchesData)) {
      console.log("Setting branches:", branchesData);
      setBranches(branchesData);
      setBranchName(selectedBranchName);
    } else {
      console.log("Clearing branches - no valid branchesData");
      setBranches([]);
    }
  }, [branchesData, selectedBranchName, isCreatingNewRepo, selectedRepository]);

  // Track previous isCreatingNewRepo state to detect mode switches
  const prevIsCreatingNewRepoRef = useRef(isCreatingNewRepo);

  // Handle refetching branches when switching modes
  useEffect(() => {
    const wasCreatingNewRepo = prevIsCreatingNewRepoRef.current;
    const isNowSelectingRepo = !isCreatingNewRepo;

    // If we switched from "Create New" to "Select Repo" and have the necessary data
    if (
      wasCreatingNewRepo &&
      isNowSelectingRepo &&
      selectedRepository &&
      currentAccountLogin &&
      !isLoadingBranches
    ) {
      console.log(
        "Mode switch detected: Create New -> Select Repo, refetching branches for:",
        {
          selectedRepository,
          currentAccountLogin,
        }
      );

      // Force refetch branches with a small delay to ensure RTK Query has updated
      const timeoutId = setTimeout(async () => {
        try {
          const result = await refetchBranches();
          console.log("Refetch result:", result);
        } catch (error) {
          console.warn("Failed to refetch branches on mode switch:", error);
        }
      }, 50);

      return () => clearTimeout(timeoutId);
    }

    // Update the ref for next comparison
    prevIsCreatingNewRepoRef.current = isCreatingNewRepo;
  }, [
    isCreatingNewRepo,
    selectedRepository,
    currentAccountLogin,
    isLoadingBranches,
    refetchBranches,
  ]);

  // Handle refresh repositories
  const handleRefreshRepositories = async () => {
    if (!selectedInstallation || isLoadingRepositories || isRefreshing) return;

    setIsRefreshing(true);
    await refetchRepositories();
    setIsRefreshing(false);
  };

  // Handle refresh branches
  const handleRefreshBranches = async () => {
    if (!selectedRepository || isLoadingBranches || isCreatingNewRepo) return;

    await refetchBranches();
  };

  // Filter repositories based on search query
  const filteredRepositories = repositories.filter((repo) => {
    if (!searchQuery) return true;
    return repo.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Handle branch input change
  const handleBranchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const oldValue = e.target.value;
    // Only replace spaces with hyphens, but validate other characters
    const value = replaceSpacesWithHyphens(oldValue);
    setBranchName(value);

    // If we're in the custom branch input mode, just update the branch name
    if (dropdownState.showCustomBranchInput) {
      // No need to do anything else, just update the branch name
      return;
    }
  };

  // Track previous branch name and repository to detect actual changes
  const prevBranchNameRef = useRef<string>(branchName);
  const prevSelectedRepositoryRef = useRef<string>(selectedRepository);

  // Reset conflict detection when branch name or repository actually changes
  useEffect(() => {
    const branchChanged = prevBranchNameRef.current !== branchName;
    const repoChanged =
      prevSelectedRepositoryRef.current !== selectedRepository;

    if (branchChanged || repoChanged) {
      if (conflictDetected) {
        // Hide conflict UI when switching away from conflicted repo/branch
        setConflictDetected(false);
        setShowForcePushConfirmation(false);
        setNeedsForcePush(false);
        setProposedBranchName("");
        setPushError(null);
      }

      // Check if we're switching back to a previously conflicted repo/branch combination
      if (conflictedRepoInfo) {
        const accountLogin = getAccountLogin();
        const repoName = getRepoName();

        const isConflictedRepo =
          conflictedRepoInfo.repository === repoName &&
          conflictedRepoInfo.branch === branchName &&
          conflictedRepoInfo.owner === accountLogin;

        if (isConflictedRepo && !conflictDetected) {
          // Restore conflict UI for this repo/branch combination
          setConflictDetected(true);
          setNeedsForcePush(true);
          setProposedBranchName(generateProposedBranchName());
          // Don't set pushError - we only want the main conflict UI to show
        }
      }
    }

    prevBranchNameRef.current = branchName;
    prevSelectedRepositoryRef.current = selectedRepository;
  }, [branchName, selectedRepository, conflictDetected, conflictedRepoInfo]);

  // Scroll to bottom when conflict is detected with smooth animation
  useEffect(() => {
    if (conflictDetected && scrollableContainerRef.current) {
      // Use setTimeout to ensure the conflict UI has rendered
      setTimeout(() => {
        if (scrollableContainerRef.current) {
          const container = scrollableContainerRef.current;
          const targetScrollTop =
            container.scrollHeight - container.clientHeight;

          // Custom smooth scroll with easing
          const startScrollTop = container.scrollTop;
          const distance = targetScrollTop - startScrollTop;
          const duration = 800; // Longer duration for smoother animation
          const startTime = performance.now();

          const easeInOutCubic = (t: number) => {
            return t < 0.5
              ? 4 * t * t * t
              : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
          };

          const animateScroll = (currentTime: number) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = easeInOutCubic(progress);

            container.scrollTop = startScrollTop + distance * easedProgress;

            if (progress < 1) {
              requestAnimationFrame(animateScroll);
            }
          };

          requestAnimationFrame(animateScroll);
        }
      }, 150); // Slightly longer delay to ensure UI is fully rendered
    }
  }, [conflictDetected]);

  // Validation helpers using the new validation utilities
  const getRepositoryValidation = (): ValidationResult => {
    // If we're in the process of creating a repository, don't show validation errors
    // This prevents the "Repository already exists" error from showing briefly after creation
    if (isCreatingRepoInProgress) {
      return { isValid: true };
    }

    return validateRepositoryName(newRepoName, {
      repositories,
      isCreatingNewRepo,
      selectedInstallation,
    });
  };

  const getBranchValidation = (): ValidationResult => {
    return validateBranchName(branchName, {
      branches,
      selectedRepository,
      isCreatingNewRepo,
    });
  };

  const getProposedBranchValidation = (): ValidationResult => {
    return validateProposedBranchName(proposedBranchName);
  };

  // Check if the selected account is a collaborator account
  const isCollaboratorAccount = () => {
    const selectedAccount = installations.find(
      (i) => i.installation_id === selectedInstallation
    );
    return (
      selectedAccount?.account_type === "User" && !selectedAccount?.isPrimary
    );
  };

  // Check if current repo/branch combination has a conflict
  const hasConflictForCurrentRepo = () => {
    if (!conflictedRepoInfo) return false;

    const accountLogin = getAccountLogin();
    const repoName = getRepoName();

    return (
      conflictedRepoInfo.repository === repoName &&
      conflictedRepoInfo.branch === branchName &&
      conflictedRepoInfo.owner === accountLogin
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Set loading state immediately for user feedback
    setIsSubmitting(true);

    try {
      if (!jobId || !selectedInstallation) {
        return;
      }

      if (podIsPaused) {
        toast({
          title: "Pod is paused",
          description: "Please unpause your pod before pushing to GitHub",
          variant: "destructive",
        });
        onOpenChange(false);
        return;
      }

      const accountLogin = getAccountLogin();
      const repoName = getRepoName();

      if (!accountLogin || !repoName) {
        return;
      }

      // Check if this repo/branch combination already has a conflict
      if (hasConflictForCurrentRepo()) {
        toast({
          title: "Conflict detected",
          description:
            "This repository and branch combination has a conflict. Please resolve it using the options below.",
          variant: "destructive",
        });
        return;
      }
      // If creating a new repository, create it first
      if (isCreatingNewRepo) {
        // Set flag to prevent validation errors during creation process
        setIsCreatingRepoInProgress(true);

        await createRepository({
          installation_id: selectedInstallation,
          org:
            installations.find(
              (i: any) => i.installation_id === selectedInstallation
            )?.account_type !== "User"
              ? accountLogin
              : undefined,
          name: repoName,
        }).unwrap();

        // Add a delay to allow GitHub API to process the repository creation
        await new Promise((resolve) => setTimeout(resolve, 1500));
      }

      // Push to GitHub
      await pushToGitHub({
        jobId,
        data: {
          account_login: accountLogin,
          repo_name: repoName,
          branch_name: branchName,
          is_new_repo: isCreatingNewRepo,
          force: needsForcePush,
        },
      }).unwrap();

      // Reset conflict states and show success modal
      setConflictDetected(false);
      setShowForcePushConfirmation(false);
      setNeedsForcePush(false);
      setPushError(null);
      setConflictedRepoInfo(null);

      setSuccessDetails({
        branch: branchName,
        repo: repoName,
        owner: accountLogin,
        isNewRepo: isCreatingNewRepo,
        isNewBranch: false,
      });

      // Show success modal immediately
      setShowSuccessModal(true);

      // Reset the repo creation flag since we're now showing success
      setIsCreatingRepoInProgress(false);

      // Don't call onSuccess immediately - let the user see the success modal first
      // onSuccess will be called when the modal is closed
    } catch (error: any) {
      const errorResult = handleGitHubPushError(error, {
        defaultErrorMessage: "Failed to push to GitHub",
        needsForcePush,
        allowConflictDetection: true,
      });

      switch (errorResult.type) {
        case "secret_protection":
          setSecretProtectionError({
            fullError: errorResult.fullErrorText,
            learnMoreUrl: errorResult.learnMoreUrl,
            isExpanded: false,
          });
          return;

        case "remote_rejection":
          if (errorResult.shouldShowConflictUI) {
            setConflictDetected(true);
            setNeedsForcePush(true);
            setProposedBranchName(generateProposedBranchName());
            // Store the conflicted repo info
            setConflictedRepoInfo({
              repository: getRepoName(),
              branch: branchName,
              owner: getAccountLogin(),
            });
            return;
          }
          break;

        case "generic":
        default:
          break;
      }

      setPushError(errorResult.errorMessage);
      toast({
        title: "Error",
        description: errorResult.errorMessage,
        variant: "destructive",
      });
    } finally {
      // Always reset the loading state
      setIsSubmitting(false);
      // Reset the repo creation flag
      setIsCreatingRepoInProgress(false);
    }
  };

  const handleCreateBranchAndPush = async () => {
    // Set loading state immediately for user feedback
    setIsSubmitting(true);

    try {
      if (!jobId || !selectedInstallation || !proposedBranchName) {
        return;
      }

      const accountLogin = getAccountLogin();
      const repoName = getRepoName();

      if (!accountLogin || !repoName) {
        return;
      }
      // Push to GitHub with the new branch name
      await pushToGitHub({
        jobId,
        data: {
          account_login: accountLogin,
          repo_name: repoName,
          branch_name: proposedBranchName,
          is_new_repo: isCreatingNewRepo,
          force: false,
        },
      }).unwrap();

      // Reset conflict states and show success modal
      setConflictDetected(false);
      setShowForcePushConfirmation(false);
      setNeedsForcePush(false);
      setPushError(null);
      setConflictedRepoInfo(null);

      setSuccessDetails({
        branch: proposedBranchName,
        repo: repoName,
        owner: accountLogin,
        isNewRepo: isCreatingNewRepo,
        isNewBranch: true,
      });

      // Show success modal immediately
      setShowSuccessModal(true);

      // Don't call onSuccess immediately - let the user see the success modal first
      // onSuccess will be called when the modal is closed
    } catch (error: any) {
      const errorResult = handleGitHubPushError(error, {
        defaultErrorMessage: "Failed to create branch and push to GitHub",
        needsForcePush: false,
        allowConflictDetection: false, // For new branch creation, don't show conflict UI
      });

      switch (errorResult.type) {
        case "secret_protection":
          setSecretProtectionError({
            fullError: errorResult.fullErrorText,
            learnMoreUrl: errorResult.learnMoreUrl,
            isExpanded: false,
          });
          return;

        case "remote_rejection":
        case "generic":
        default:
          // For new branch creation, just show the error without conflict UI
          setPushError(errorResult.errorMessage);
          toast({
            title: "Error",
            description: errorResult.errorMessage,
            variant: "destructive",
          });
          break;
      }
    } finally {
      // Always reset the loading state
      setIsSubmitting(false);
    }
  };

  const handleForcePush = async () => {
    // Set loading state immediately for user feedback
    setIsSubmitting(true);

    try {
      if (!jobId || !selectedInstallation) {
        return;
      }

      const accountLogin = getAccountLogin();
      const repoName = getRepoName();

      if (!accountLogin || !repoName) {
        return;
      }
      // Push to GitHub with force flag
      await pushToGitHub({
        jobId,
        data: {
          account_login: accountLogin,
          repo_name: repoName,
          branch_name: branchName,
          is_new_repo: isCreatingNewRepo,
          force: true,
        },
      }).unwrap();

      // Reset conflict states and show success modal
      setConflictDetected(false);
      setShowForcePushConfirmation(false);
      setNeedsForcePush(false);
      setPushError(null);
      setConflictedRepoInfo(null);

      setSuccessDetails({
        branch: branchName,
        repo: repoName,
        owner: accountLogin,
        isNewRepo: isCreatingNewRepo,
        isNewBranch: false,
      });

      // Show success modal immediately
      setShowSuccessModal(true);

      // Don't call onSuccess immediately - let the user see the success modal first
      // onSuccess will be called when the modal is closed
    } catch (error: any) {
      const errorResult = handleGitHubPushError(error, {
        defaultErrorMessage: "Failed to force push to GitHub",
        needsForcePush: true,
        allowConflictDetection: false, // Force push shouldn't show conflict UI
      });

      switch (errorResult.type) {
        case "secret_protection":
          setSecretProtectionError({
            fullError: errorResult.fullErrorText,
            learnMoreUrl: errorResult.learnMoreUrl,
            isExpanded: false,
          });
          return;

        case "remote_rejection":
        case "generic":
        default:
          setPushError(errorResult.errorMessage);
          toast({
            title: "Error",
            description: errorResult.errorMessage,
            variant: "destructive",
          });
          break;
      }
    } finally {
      // Always reset the loading state
      setIsSubmitting(false);
    }
  };

  const isLoading =
    initialLoading || isLoadingInstallations || isLoadingRepositories;

  // Combined loading state for better user feedback
  const isProcessing = isSubmitting || isPushing || isCreatingRepo;

  // Handle adding a new GitHub account
  const handleAddNewAccount = async () => {
    // Set connecting state to true
    setIsConnecting(true);

    // Define a function to directly check user details and update UI
    const checkUserDetailsAndUpdateUI = async () => {
      try {
        // Refetch user details from RTK Query
        const result = await refetchUserDetails();

        // Check if GitHub is authorized in the response
        if (
          result.data &&
          result.data.github &&
          result.data.github.authorized === true
        ) {
          // Refetch installations (connection state will be updated automatically by useGitHub hook)
          await refetchInstallations();

          // Show success toast
          toast({
            title: "Success",
            description: "Successfully connected to GitHub",
          });
          return true;
        } else {
          return false;
        }
      } catch (error) {
        console.error("Error checking user details:", error);
        return false;
      } finally {
        // Always reset connecting state
        setIsConnecting(false);
      }
    };

    // Set up a polling mechanism to check for connection
    const pollForConnection = () => {
      //console.log("Starting to poll for GitHub connection...");
      let attempts = 0;
      const maxAttempts = 10;
      const pollInterval = 3000; // 3 seconds

      const pollTimer = setInterval(async () => {
        attempts++;
        //console.log(`Polling attempt ${attempts}/${maxAttempts}`);

        const connected = await checkUserDetailsAndUpdateUI();

        if (connected || attempts >= maxAttempts) {
          clearInterval(pollTimer);
          if (!connected && attempts >= maxAttempts) {
            //console.log("Max polling attempts reached without success");
            setIsConnecting(false);
          }
        }
      }, pollInterval);

      // Clear interval after a maximum time to prevent infinite polling
      setTimeout(() => {
        clearInterval(pollTimer);
        setIsConnecting(false);
      }, pollInterval * maxAttempts + 1000);
    };

    try {
      // Web implementation using the centralized method from GitHubContext
      redirectToGitHubInstallation({
        isPopup: true,
        pollingEnabled: false, // We'll handle polling ourselves
        onSuccess: async () => {
          //console.log("GitHub connection callback triggered");
          await checkUserDetailsAndUpdateUI();
        },
      });

      // Start polling for connection status
      pollForConnection();
    } catch (err) {
      console.error("Error connecting to GitHub:", err);
      setIsConnecting(false);
      toast({
        title: "Error",
        description: "Failed to connect to GitHub",
        variant: "destructive",
      });
    }
  };

  // Handle clicking outside of dropdowns (for non-portal dropdowns only)
  const handleOutsideClick = (_e: React.MouseEvent<HTMLFormElement>) => {
    // All dropdowns are now portal-based, so this function is mainly for form interactions
    // Portal dropdown outside clicks are handled by the global event listener
  };

  // Global click handler for portal dropdowns
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;

      // Check if the click is outside of the installation dropdown
      if (
        dropdownState.showInstallationDropdown &&
        !target.closest(".installation-dropdown") &&
        !target.closest(".installation-input")
      ) {
        updateDropdownState({ showInstallationDropdown: false });
      }

      // Check if the click is outside of the repository dropdown
      if (
        dropdownState.showRepositoryDropdown &&
        !target.closest(".repository-dropdown") &&
        !target.closest(".repository-input") &&
        !target.closest(".repo-toggle-buttons")
      ) {
        updateDropdownState({ showRepositoryDropdown: false });
      }

      // Check if the click is outside of the branch dropdown
      if (
        dropdownState.showBranchDropdown &&
        !target.closest(".branch-dropdown") &&
        !target.closest(".branch-input") &&
        !target.closest("#branch-selector")
      ) {
        updateDropdownState({ showBranchDropdown: false });
      }

      // Check if the click is outside of the custom branch input
      // For custom branch input, we'll handle closing manually via Cancel button or Escape key
      // Don't auto-close on outside clicks to allow proper editing
      if (
        dropdownState.showCustomBranchInput &&
        !target.closest(
          "[data-portal-dropdown='branch-dropdown custom-branch-input']"
        ) &&
        !target.closest("#branch-selector")
      ) {
        // Only close if clicking on specific elements that should close it
        if (
          target.closest(".modal-backdrop") ||
          target.closest("[data-dialog-close]")
        ) {
          updateDropdownState({ showCustomBranchInput: false });
          setCustomBranchName("");
        }
        // Don't close for other clicks - let user interact with the input
        return;
      }
    };

    const hasOpenDropdown =
      dropdownState.showInstallationDropdown ||
      dropdownState.showRepositoryDropdown ||
      dropdownState.showBranchDropdown ||
      dropdownState.showCustomBranchInput;

    // Don't add global click handler when custom branch input is open
    // to allow proper editing without interference
    if (hasOpenDropdown && !dropdownState.showCustomBranchInput) {
      document.addEventListener("mousedown", handleGlobalClick);
      return () => document.removeEventListener("mousedown", handleGlobalClick);
    }
  }, [
    dropdownState.showInstallationDropdown,
    dropdownState.showRepositoryDropdown,
    dropdownState.showBranchDropdown,
    dropdownState.showCustomBranchInput,
  ]);

  // Close portal dropdowns when modal closes
  useEffect(() => {
    if (!isOpen) {
      updateDropdownState({
        showInstallationDropdown: false,
        showRepositoryDropdown: false,
        showBranchDropdown: false,
        showCustomBranchInput: false,
      });
    }
  }, [isOpen]);

  // Handle repository name input change (for search or creating new repo)
  const handleRepositoryInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    // Only replace spaces with hyphens, but allow all other valid GitHub repository characters
    const formattedValue = replaceSpacesWithHyphens(value);

    setSearchQuery(formattedValue);

    // Always clear selected repository when search value changes
    if (formattedValue !== searchQuery) {
      setSelectedRepository("");
      setBranches([]);
      // setFilteredBranches removed
      setBranchName("main");
    }

    // If in "Create New" mode, update the new repo name
    if (isCreatingNewRepo) {
      setNewRepoName(formattedValue);
      setRepoNotFound(false);
      return;
    }

    // If in "Select Repo" mode, check if the input matches an existing repo
    const matchingRepo = repositories.find(
      (repo) => repo.name.toLowerCase() === formattedValue.toLowerCase()
    );

    if (matchingRepo) {
      // If we found a matching repo, select it
      setSelectedRepository(matchingRepo.name);
      updateDropdownState({ showRepositoryDropdown: false });
      setRepoNotFound(false);
    } else if (formattedValue) {
      // If no match but we have input, keep the dropdown open to show filtered results
      setSelectedRepository("");
      updateDropdownState({ showRepositoryDropdown: true });

      // Check if there are any filtered results
      const hasFilteredResults = repositories.some((repo) =>
        repo.name.toLowerCase().includes(formattedValue.toLowerCase())
      );

      // Set repoNotFound to true only if there are no filtered results and we have input
      setRepoNotFound(!hasFilteredResults && formattedValue.length > 0);

      // Clear branches since we don't have a selected repository
      setBranches([]);
    } else {
      // Empty input, clear selection
      setSelectedRepository("");
      setRepoNotFound(false);

      // Clear branches since we don't have a selected repository
      setBranches([]);
    }
  };

  // Memoized installation selection logic
  const defaultInstallationId = useMemo(() => {
    if (
      installations.length === 0 ||
      isLoadingInstallations ||
      selectedInstallation
    ) {
      return null;
    }

    // If we have lastGithubUsed data, try to match with that first
    if (lastGithubUsed && lastGithubUsed.owner) {
      const matchingInstallation = installations.find(
        (installation: any) =>
          installation.account_login === lastGithubUsed.owner
      );
      if (matchingInstallation) {
        return matchingInstallation.installation_id;
      }
    }

    // If no matching installation from lastGithubUsed, select primary account if available
    const primaryAccount = installations.find(
      (installation: any) => installation.isPrimary
    );
    if (primaryAccount) {
      return primaryAccount.installation_id;
    }

    // Otherwise, select the first installation (they're already sorted properly)
    return installations[0]?.installation_id || null;
  }, [
    lastGithubUsed,
    installations,
    isLoadingInstallations,
    selectedInstallation,
  ]);

  // Handle installation selection
  useEffect(() => {
    if (defaultInstallationId) {
      setSelectedInstallation(defaultInstallationId);
    }
  }, [defaultInstallationId]);

  // Memoized repository and branch selection logic
  const defaultRepositoryData = useMemo(() => {
    if (
      !lastGithubUsed ||
      isLoadingRepositories ||
      repositories.length === 0 ||
      !selectedInstallation
    ) {
      return { repoName: "", branchName: "main" };
    }

    // Only prefill if the last used repo belongs to the currently selected installation
    const currentInstallation = installations.find(
      (installation: any) =>
        installation.installation_id === selectedInstallation
    );

    // Check if the current installation matches the last used owner
    if (
      currentInstallation &&
      currentInstallation.account_login === lastGithubUsed.owner
    ) {
      return {
        repoName: lastGithubUsed.repo,
        branchName: lastGithubUsed.branch || "main",
      };
    }

    // Reset to default if not using the last repo
    return { repoName: "", branchName: "main" };
  }, [
    lastGithubUsed,
    repositories,
    isLoadingRepositories,
    selectedInstallation,
    installations,
  ]);

  // Handle repository and branch selection
  useEffect(() => {
    if (defaultRepositoryData.repoName) {
      setSelectedRepository(defaultRepositoryData.repoName);
      setSearchQuery(defaultRepositoryData.repoName);
    }
    setBranchName(defaultRepositoryData.branchName);
  }, [defaultRepositoryData]);

  if (!isConnected) {
    return (
      <>
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
          <DialogContent
            className="md:max-w-[620px] md:min-h-[600px] transition-all duration-300 ease-in-out  bg-[#0F0F10] overflow-hidden"
            // @ts-ignore
            hideclosebutton
          >
            {/* Loading overlay when connecting */}
            {isConnecting && (
              <div className="absolute inset-0 z-50 flex flex-col items-center justify-center bg-[#0F0F10]/90 backdrop-blur-sm">
                <div className="flex flex-col items-center justify-center p-6 rounded-lg">
                  <Loader2 className="w-12 h-12 text-[#00E573] animate-spin mb-4" />
                  <p className="text-lg font-medium text-white">
                    Connecting to GitHub...
                  </p>
                </div>
              </div>
            )}

            <img
              src={LineSVG}
              alt="Line"
              className="absolute top-0 left-0 right-0 z-[-1] w-full pointer-events-none"
            />
            <DialogClose className="z-20 ml-auto bg-[#FFFFFF05] backdrop-blur-lg hover:bg-[#FFFFFF12]">
              <X className="absolute z-20 w-5 h-5 text-gray-400 hover:text-white top-12 right-12" />
            </DialogClose>
            <div className="flex items-center p-[40px] pt-[80px] gap-1 z-10">
              <img src={ConnectWithGithub} alt="GitHub" />
            </div>
            <div className="p-[40px] pt-[0px] flex flex-col items-start gap-4 h-[300px] z-10">
              <span className="text-white text-[24px] font-medium font-brockmann">
                Connect Github to Emergent
              </span>
              <span className="text-[18px] text-[#8A8F98]">
                Connect your GitHub account to effortlessly save, sync, and
                access your work in all repositories, anytime.
              </span>
            </div>
            <div className="flex items-center justify-end gap-4 border border-[#242424] p-[20px] bg-[#111112]">
              <Button
                onClick={() => onOpenChange(false)}
                className="flex items-center px-[20px] w-fit justify-center bg-[#272729] hover:bg-[#343434] text-white"
                disabled={isConnecting}
              >
                <div className="flex items-center justify-center w-full text-[16px]">
                  Cancel
                </div>
              </Button>
              <Button
                onClick={handleAddNewAccount}
                className="max-w-[200px] w-full bg-[#00E573] hover:bg-[##00E573]/90 text-[#0E0E0F] font-brockmann font-semibold hover:bg-[#00E573]/80"
                disabled={isConnecting}
              >
                <div className="flex items-center justify-center w-full text-[16px]">
                  {isConnecting ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Connecting...
                    </div>
                  ) : (
                    "Connect GitHub"
                  )}
                </div>
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent
          className={cn(
            "max-w-[700px] p-0 overflow-visible transition-all duration-300 ease-in-out top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
            showForcePushConfirmation || (showSuccessModal && "overflow-hidden")
          )}
          //@ts-ignore
          hideclosebutton
        >
          {/* Loading overlay when connecting */}
          {isConnecting && (
            <div className="absolute inset-0 z-50 flex flex-col items-center justify-center bg-[#0F0F10]/90 backdrop-blur-sm">
              <div className="flex flex-col items-center justify-center p-6 rounded-lg">
                <Loader2 className="w-12 h-12 text-[#00E573] animate-spin mb-4" />
                <p className="text-lg font-medium text-white">
                  Connecting to GitHub...
                </p>
              </div>
            </div>
          )}

          {!showForcePushConfirmation && !showSuccessModal && (
            <div className="flex items-center gap-2 px-4  md:px-8 max-h-[98px] py-6 border-b border-[#242424]">
              <div className="flex items-center justify-center bg-[#ffffff05] p-2 rounded-full">
                <img src={GithubIconFull} alt="GitHub" className="w-6 h-6" />
              </div>
              <h2 className="text-[22px] font-medium text-white">
                Save to Github
              </h2>
              <DialogClose className="ml-auto">
                <X className="w-5 h-5 text-gray-400 hover:text-white" />
              </DialogClose>
            </div>
          )}

          {/* Always render the form, but conditionally show loading overlay */}
          <div
            ref={scrollableContainerRef}
            className="relative max-h-[500px] overflow-y-auto"
          >
            {isLoading && (
              <div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-[#0F0F10] transition-opacity duration-300 ease-in-out">
                <Loader2 className="h-8 w-8 text-[#00FF85] animate-spin mb-4" />
                <p className="text-[#DDDDE6]/70 text-sm">
                  {initialLoading
                    ? "Connecting to GitHub..."
                    : isLoadingInstallations
                    ? "Loading GitHub accounts..."
                    : "Loading repositories..."}
                </p>
              </div>
            )}
            {/* Success UI */}
            {showSuccessModal && successDetails ? (
              <>
                <img
                  src={LineSVG}
                  className="absolute top-0 left-0 right-0 z-[-1] w-full pointer-events-none"
                  alt="Line"
                />
                <DialogClose className="absolute z-20 p-2 rounded-md group backdrop-blur-lg bg-white/5 top-8 right-8">
                  <X className="w-5 h-5 text-gray-400 group-hover:text-white" />
                </DialogClose>
                <div className="p-8 pt-[10rem] space-y-6">
                  <div className="flex flex-col items-start w-full gap-6">
                    <img
                      src={GithubGreenDark}
                      alt="GitHub"
                      className="w-16 h-16 mb-4"
                    />
                    {/* Success Message */}
                    <div className="space-y-2 text-start">
                      <h2 className="text-2xl font-medium text-white">
                        Successfully Saved to GitHub!
                      </h2>
                      <p className="text-[#737780] font-medium font-['Inter']">
                        Your changes have been saved to{" "}
                        <span className="text-[#fff] font-medium font-['Inter']">
                          {successDetails.owner}/{successDetails.repo},<br />
                        </span>
                        Continue working on your task or view the codebase on
                        GitHub.
                      </p>
                    </div>

                    {/* Repository Details */}
                    <div
                      onClick={() => {
                        const repoUrl = `https://github.com/${successDetails.owner}/${successDetails.repo}/tree/${successDetails.branch}`;
                        window.open(repoUrl, "_blank");
                      }}
                      className="bg-[#18181A] mb-4 w-full cursor-pointer flex justify-between items-center border border-[#242424] rounded-lg p-4 gap-2"
                    >
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          <img
                            src={GithubIcon}
                            alt="GitHub"
                            className="w-6 h-6"
                          />
                          <span className="font-medium font-['Inter'] text-white text-nowrap">
                            {successDetails.owner} \ {successDetails.repo}
                          </span>
                        </div>

                        <div className="flex items-center">
                          <img
                            src={GitBranch}
                            alt="Branch"
                            className="w-5 h-5 opacity-50 grayscale"
                          />
                          <span className="font-medium text-[#626266] truncate">
                            {successDetails.branch}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 cursor-pointer group hover:opacity-80">
                        <span className="flex group-hover:text-[#2EE572] items-center text-nowrap gap-2 text-[#626266] font-medium">
                          View on GitHub
                          <img
                            src={GitubGreenArrow}
                            alt="Arrow"
                            className={cn("w-6 h-6 grayscale opacity-35 group-hover:opacity-100 group-hover:grayscale-0", )}
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : showForcePushConfirmation ? (
              /* Force Push Confirmation UI */
              <div className="p-4 space-y-6 md:p-8">
                <div className="flex items-start w-full gap-2">
                  <div className="space-y-1">
                    <h2 className="text-2xl font-medium text-white">
                      Confirm force push to
                    </h2>
                    <p className="text-[#E38F45] font-medium text-[22px] leading-[28px] tracking-[-2%]">
                      "{branchName}"
                    </p>
                  </div>

                  <div
                    onClick={() => setShowForcePushConfirmation(false)}
                    className="z-20 rounded-sm p-1 group ml-auto bg-[#FFFFFF05] backdrop-blur-lg hover:bg-[#FFFFFF12]"
                  >
                    <X className="z-20 w-5 h-5 text-gray-400 group-hover:text-white top-12 right-12" />
                  </div>
                </div>

                <div className="space-y-4">
                  <p className="text-[#B7BECC] font-['Inter'] font-medium leading-[24px] text-[15px] md:max-w-[80%]">
                    Are you sure you want to force push? <br />
                    This will replace everything on{" "}
                    <span className="font-semibold text-[#FFA04D]">
                      '{branchName}'
                    </span>{" "}
                    with your current changes — and can't be undone.
                  </p>
                </div>
              </div>
            ) : (
              <form
                onSubmit={handleSubmit}
                className="p-4 space-y-4 md:space-y-6 md:p-8"
                onClick={handleOutsideClick}
              >
                {/* Connected Account Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <img
                      src={GithubIcon}
                      alt="GitHub"
                      className="w-5 h-5 opacity-70"
                    />
                    <span className="text-sm text-gray-400">
                      Connected Organizations
                    </span>
                  </div>
                  <div className="relative">
                    <div
                      ref={installationInputRef}
                      className={`flex items-center justify-between w-full h-14 bg-[#131314] border ${
                        dropdownState.showInstallationDropdown
                          ? "border-white/50"
                          : "border-[#242424]"
                      } rounded-md p-3 text-white installation-input`}
                      onClick={() => {
                        // Close other dropdowns when clicking on the installation input
                        updateDropdownState({
                          showRepositoryDropdown: false,
                          showBranchDropdown: false,
                          showInstallationDropdown:
                            !dropdownState.showInstallationDropdown,
                        });
                      }}
                    >
                      <div className="flex items-center w-full">
                        <div className="relative flex-1">
                          {selectedInstallation ? (
                            <div className="flex items-center">
                              {installations.find(
                                (i) =>
                                  i.installation_id === selectedInstallation
                              )?.account_login || "Select a GitHub account"}
                              {installations.find(
                                (i) =>
                                  i.installation_id === selectedInstallation
                              )?.account_type === "User" &&
                                !installations.find(
                                  (i) =>
                                    i.installation_id === selectedInstallation
                                )?.isPrimary && (
                                  <span className="text-xs text-[#DDDDE6] rounded-full bg-[#FFFFFF0D] ml-1 px-2 py-1 font-['Inter'] font-medium">
                                    Collaborator
                                  </span>
                                )}
                            </div>
                          ) : (
                            <span className="text-[#8F8F98]">
                              Select a GitHub account
                            </span>
                          )}
                        </div>
                      </div>
                      <ChevronDown
                        className={`h-4 w-4 text-[#898F99] transition-transform duration-200 ${
                          dropdownState.showInstallationDropdown
                            ? "rotate-180"
                            : ""
                        }`}
                      />
                    </div>

                    {/* Installation dropdown using Portal */}
                    <PortalDropdown
                      isOpen={
                        installations.length > 0 &&
                        dropdownState.showInstallationDropdown
                      }
                      triggerRef={installationInputRef}
                      className="installation-dropdown"
                      scrollableContainerRef={scrollableContainerRef}
                    >
                      <div className="flex flex-col gap-[6px] p-2">
                        {installations.map((installation) => (
                          <div
                            key={installation.installation_id}
                            className={cn(
                              "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                              selectedInstallation ===
                                installation.installation_id &&
                                "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                            )}
                            onClick={() => {
                              // If we're selecting the same installation, don't reset everything
                              if (
                                installation.installation_id ===
                                selectedInstallation
                              ) {
                                updateDropdownState({
                                  showInstallationDropdown: false,
                                });
                                return;
                              }

                              // Clear repository-related state when changing installation
                              setSelectedRepository("");
                              setSearchQuery(""); // Clear the search query/text field
                              setBranches([]);
                              setIsCreatingNewRepo(false);
                              setNewRepoName("");
                              setRepoNotFound(false);

                              // Close any open dropdowns
                              updateDropdownState({
                                showRepositoryDropdown: false,
                                showBranchDropdown: false,
                                showInstallationDropdown: false,
                              });

                              setSelectedInstallation(
                                installation.installation_id
                              );

                              // Force the input field to clear by setting an empty value
                              const repoInput = document.querySelector(
                                ".repository-input input"
                              ) as HTMLInputElement;
                              if (repoInput) {
                                repoInput.value = "";
                              }
                            }}
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-2">
                                <span>{installation.account_login}</span>
                                {installation.account_type === "User" &&
                                  !installation.isPrimary && (
                                    <span className="text-xs text-[#DDDDE6] rounded-full bg-[#FFFFFF0D] ml-1 px-2 py-1 font-['Inter'] font-medium">
                                      Collaborator
                                    </span>
                                  )}
                              </div>
                              {selectedInstallation ===
                                installation.installation_id && (
                                <Check className="h-4 w-4 text-[#1BB4CC]" />
                              )}
                            </div>
                          </div>
                        ))}
                        <div className="border-t border-[#ffffff12]"></div>
                        <div
                          className={`px-3 min-h-[40px] rounded-[8px] py-2 text-[#4ADE80] hover:bg-[#4ADE80]/10 font-medium ${
                            isConnecting
                              ? "opacity-50 cursor-not-allowed"
                              : "cursor-pointer"
                          }`}
                          onClick={() => {
                            if (!isConnecting) {
                              handleAddNewAccount();
                              updateDropdownState({
                                showInstallationDropdown: false,
                              });
                            }
                          }}
                        >
                          <div className="flex items-center gap-2">
                            {isConnecting ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Plus className="w-4 h-4" />
                            )}
                            {isConnecting
                              ? "Connecting..."
                              : "Add New Github Organizations"}
                          </div>
                        </div>
                      </div>
                    </PortalDropdown>
                  </div>
                  {installations.length === 0 &&
                    !initialLoading &&
                    !isLoadingInstallations && (
                      <p className="text-[#FF4545] text-sm">
                        No GitHub accounts connected. Please connect a GitHub
                        account.
                      </p>
                    )}
                </div>

                {/* Repository Section */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center justify-between w-full gap-2">
                      <div className="flex items-center gap-2">
                        <img
                          src={RepositoryIcon}
                          alt="Repository"
                          className="w-5 h-5"
                        />
                        <span className="text-[#898F99] text-sm font-medium font-[Inter]">
                          Selected Repo
                        </span>
                      </div>
                      {selectedInstallation && (
                        <button
                          type="button"
                          className="flex items-center justify-center w-6 h-6 ml-1 rounded-full hover:bg-blue-400/5 group"
                          onClick={handleRefreshRepositories}
                          disabled={isLoadingRepositories || isRefreshing}
                          title="Refresh repositories"
                        >
                          <RefreshCw
                            className={`h-3.5 w-3.5 text-[#898F99] group-hover:text-white ${
                              isRefreshing ? "animate-spin" : ""
                            }`}
                          />
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="relative">
                    <div
                      ref={repositoryInputRef}
                      className={`flex items-center justify-between w-full h-14 bg-[#131314] border ${
                        dropdownState.showRepositoryDropdown
                          ? "border-white/50"
                          : "border-[#242424]"
                      } rounded-md p-3 text-white repository-input`}
                    >
                      <div className="flex items-center w-full">
                        <Search className="h-4 w-4 text-[#8F8F98] mr-2" />
                        <div className="relative flex-1">
                          <Input
                            type="text"
                            value={searchQuery}
                            onChange={handleRepositoryInputChange}
                            onFocus={() => {
                              if (!isCreatingNewRepo) {
                                // Close other dropdowns and open repository dropdown
                                updateDropdownState({
                                  showBranchDropdown: false,
                                  showInstallationDropdown: false,
                                  showRepositoryDropdown: true,
                                });
                              }
                            }}
                            placeholder={
                              isCreatingNewRepo
                                ? "Enter new repository name"
                                : "Search for a repository"
                            }
                            className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                            disabled={
                              !selectedInstallation ||
                              (isCreatingNewRepo && isCollaboratorAccount())
                            }
                          />
                          {selectedRepository && !isCreatingNewRepo && (
                            <button
                              type="button"
                              className="absolute right-0 flex items-center justify-center w-6 mr-1 h-6 transform hover:bg-[#ffffff05] -translate-y-1/2 rounded-full top-1/2"
                              onClick={() => {
                                setSelectedRepository("");
                                setSearchQuery("");
                                setBranches([]);
                                // setFilteredBranches removed
                                setBranchName("main");
                                updateDropdownState({
                                  showRepositoryDropdown: true,
                                });
                              }}
                              title="Clear selected repository"
                            >
                              <X className="h-3.5 w-3.5 text-[#898F99]" />
                            </button>
                          )}
                        </div>
                      </div>
                      <div className="flex text-sm text-nowrap bg-[#ffffff15] p-[6px] rounded-full font-semibold cursor-pointer repo-toggle-buttons">
                        <span
                          className={`px-[10px] py-1 rounded-full transition-colors ${
                            !isCreatingNewRepo
                              ? "bg-white text-black"
                              : "text-white/50 hover:text-white/80 font-medium"
                          }`}
                          onClick={async () => {
                            console.log("Switching to Select Repo mode", {
                              selectedRepository,
                              currentAccountLogin,
                              searchQuery,
                              branchesLength: branches.length,
                            });

                            setIsCreatingNewRepo(false);

                            // Close other dropdowns and show repository dropdown
                            updateDropdownState({
                              showBranchDropdown: false,
                              showInstallationDropdown: false,
                              showRepositoryDropdown: !!(
                                selectedInstallation && repositories.length > 0
                              ),
                            });

                            // If we have a search query, try to find a matching repository
                            if (searchQuery) {
                              const matchingRepo = repositories.find(
                                (r) =>
                                  r.name.toLowerCase() ===
                                  searchQuery.toLowerCase()
                              );

                              if (matchingRepo) {
                                console.log(
                                  "Found matching repo:",
                                  matchingRepo.name
                                );
                                // If we found a matching repo, select it
                                setSelectedRepository(matchingRepo.name);
                                setSearchQuery(matchingRepo.name);
                                setRepoNotFound(false);

                                // Force refetch branches for the selected repository
                                if (currentAccountLogin) {
                                  console.log(
                                    "Force refetching branches for:",
                                    matchingRepo.name
                                  );
                                  try {
                                    const result = await refetchBranches();
                                    console.log("Refetch result:", result);
                                  } catch (error) {
                                    console.warn(
                                      "Failed to refetch branches:",
                                      error
                                    );
                                  }
                                }
                              } else {
                                // If no matching repo, show the error
                                setSelectedRepository("");
                                setRepoNotFound(true);
                                setBranches([]);
                                setBranchName("main");
                              }
                            } else if (selectedRepository) {
                              console.log(
                                "Keeping existing repo:",
                                selectedRepository
                              );
                              // If we already have a selected repository, keep it and refetch branches
                              const repo = repositories.find(
                                (r) => r.name === selectedRepository
                              );
                              if (repo) {
                                setSearchQuery(repo.name);
                                setRepoNotFound(false);

                                // Force refetch branches for the selected repository
                                if (currentAccountLogin) {
                                  console.log(
                                    "Force refetching branches for existing repo:",
                                    repo.name
                                  );
                                  try {
                                    const result = await refetchBranches();
                                    console.log("Refetch result:", result);
                                  } catch (error) {
                                    console.warn(
                                      "Failed to refetch branches:",
                                      error
                                    );
                                  }
                                }
                              } else {
                                // If repository no longer exists, clear it
                                setSelectedRepository("");
                                setBranches([]);
                                setBranchName("main");
                              }
                            } else {
                              // No repository selected, clear branch data
                              setBranches([]);
                              setBranchName("main");
                            }
                          }}
                        >
                          Select Repo
                        </span>
                        <span
                          className={`px-[10px] py-1 rounded-full transition-colors ${
                            isCreatingNewRepo
                              ? "bg-white text-black "
                              : isCollaboratorAccount()
                              ? "text-white/20 cursor-not-allowed"
                              : "text-white/50 hover:text-white/80 font-medium"
                          }`}
                          onClick={() => {
                            // Check if the selected account is a collaborator account
                            if (isCollaboratorAccount()) {
                              return;
                            }

                            console.log("Switching to Create New mode", {
                              selectedRepository,
                              searchQuery,
                              branchesLength: branches.length,
                            });

                            // Switch to create new mode
                            setIsCreatingNewRepo(true);

                            // Close any open dropdowns
                            updateDropdownState({
                              showRepositoryDropdown: false,
                              showBranchDropdown: false,
                              showInstallationDropdown: false,
                            });

                            // Clear selected repository and error state
                            setSelectedRepository("");
                            setRepoNotFound(false);

                            // If we have a search query, use it as the new repo name
                            if (searchQuery) {
                              setNewRepoName(searchQuery);
                            } else {
                              // If no search query but we have a selected repository, use that
                              const repo = repositories.find(
                                (r) => r.name === selectedRepository
                              );
                              if (repo) {
                                setNewRepoName(repo.name);
                                setSearchQuery(repo.name);
                              }
                            }

                            // Clear branches and reset branch name since we're creating a new repo
                            console.log(
                              "Clearing branches for Create New mode"
                            );
                            setBranches([]);
                            setBranchName("main");
                          }}
                        >
                          Create New
                        </span>
                      </div>
                    </div>

                    {/* Repository dropdown using Portal */}
                    <PortalDropdown
                      isOpen={
                        repositories.length > 0 &&
                        filteredRepositories.length > 0 &&
                        dropdownState.showRepositoryDropdown &&
                        !isCreatingNewRepo
                      }
                      triggerRef={repositoryInputRef}
                      className="repository-dropdown"
                      scrollableContainerRef={scrollableContainerRef}
                    >
                      <div className="px-2 py-1 flex flex-col gap-[6px]">
                        {filteredRepositories.map((repo) => (
                          <div
                            key={repo.id}
                            className={cn(
                              "px-3 min-h-[40px] rounded-[8px]   py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium  cursor-pointer",
                              repo.permissions.push ? "" : "opacity-20",
                              selectedRepository === repo.name &&
                                "bg-[#172426] text-[#1BB4CC]"
                            )}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();

                              if (repo.permissions.push) {
                                setSelectedRepository(repo.name);
                                setSearchQuery(repo.name);
                                setIsCreatingNewRepo(false);
                                setNewRepoName("");
                                updateDropdownState({
                                  showRepositoryDropdown: false,
                                });
                                setRepoNotFound(false);

                                // Branches will be fetched automatically by RTK Query
                              } else {
                                toast({
                                  title:
                                    "You don't have push access to this repository.",
                                  description:
                                    "Please select a repository you have push access to.",
                                  variant: "destructive",
                                });
                              }
                            }}
                          >
                            <div className="flex items-center justify-between">
                              <span>{repo.name}</span>

                              {lastGithubUsed?.repo === repo.name &&
                                lastGithubUsed?.owner === getAccountLogin() && (
                                  <Badge
                                    variant="secondary"
                                    className="bg-green-700"
                                  >
                                    Last used
                                  </Badge>
                                )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </PortalDropdown>

                    {/* Feedback messages */}
                    {selectedInstallation &&
                      isCreatingNewRepo &&
                      isCollaboratorAccount() && (
                        <p className="text-[#FF4545] text-sm mt-2 flex items-center gap-1">
                          <img
                            src={InfoSquareIcon}
                            alt="Info"
                            className="w-3 h-3"
                          />
                          <span>
                            You don't have permission to create repositories in
                            an organization where you're a collaborator.
                          </span>
                        </p>
                      )}
                    {selectedInstallation &&
                      isCreatingNewRepo &&
                      !newRepoName && (
                        <p className="text-[#DDDDE6]/50 text-sm mt-2">
                          Enter a name for your new repository
                        </p>
                      )}
                    {selectedInstallation &&
                      isCreatingNewRepo &&
                      newRepoName &&
                      !getRepositoryValidation().isValid && (
                        <p className="text-[#FF4545] text-sm mt-2">
                          {getRepositoryValidation().errorMessage}
                        </p>
                      )}
                    {selectedInstallation &&
                      isCreatingNewRepo &&
                      newRepoName &&
                      getRepositoryValidation().isValid && (
                        <span className="text-[#CCC] text-sm mt-2 flex items-center gap-1">
                          {getRepositoryValidation().successMessage && (
                            <>
                              <img
                                src={InfoSquareIcon}
                                alt="Info"
                                className="w-3 h-3"
                              />
                              <span className="text-[#00FF85]">
                                {getRepositoryValidation().successMessage}
                              </span>
                            </>
                          )}
                        </span>
                      )}
                    {selectedInstallation && repositories.length === 0 && (
                      <p className="text-[#DDDDE6]/50 text-sm mt-2">
                        No repositories found. Enter a name to create a new one.
                      </p>
                    )}
                    {selectedInstallation &&
                      !isCreatingNewRepo &&
                      repoNotFound &&
                      searchQuery && (
                        <p className="text-[#FF4545] text-sm mt-2 flex items-center gap-1">
                          <img
                            src={InfoSquareIcon}
                            alt="Info"
                            className="w-3 h-3"
                          />
                          <span>
                            Repository{" "}
                            <span className="font-semibold">
                              "{searchQuery}"
                            </span>{" "}
                            doesn't exist. Switch to "Create New" to create it.
                          </span>
                        </p>
                      )}
                  </div>
                </div>

                {/* Branch Selection */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <img src={BranchIcon} alt="Branch" className="w-5 h-5" />
                      <span className="text-[#898F99] text-sm font-medium font-[Inter]">
                        Branch
                      </span>
                    </div>
                    {selectedRepository && !isCreatingNewRepo && (
                      <div className="flex items-center gap-2">
                        <button
                          type="button"
                          className="flex items-center justify-center w-6 h-6 ml-1 rounded-full hover:bg-blue-400/5 group"
                          onClick={handleRefreshBranches}
                          disabled={isLoadingBranches}
                          title="Refresh branches"
                        >
                          <RefreshCw
                            className={`h-3.5 w-3.5 text-[#898F99] group-hover:text-white ${
                              isRefreshing ? "animate-spin" : ""
                            }`}
                          />
                        </button>
                      </div>
                    )}
                  </div>

                  {isLoadingBranches ? (
                    <div className="flex items-center justify-center h-14 bg-[#131314] border border-[#242424] rounded-md">
                      <Loader2 className="h-4 w-4 text-[#00FF85] animate-spin" />
                    </div>
                  ) : isCreatingNewRepo ? (
                    <div className="space-y-2">
                      <div className="relative">
                        <div className="flex items-center justify-between w-full h-14 bg-[#131314] border border-[#242424] rounded-md p-3 text-white">
                          <div className="flex items-center w-full branch-input">
                            <div className="relative flex-1">
                              <Input
                                type="text"
                                value={branchName}
                                onChange={handleBranchInputChange}
                                placeholder="Branch name (e.g., main)"
                                className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                                disabled={
                                  !selectedInstallation ||
                                  (!newRepoName && isCreatingNewRepo)
                                }
                              />
                              {branchName && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute right-0 w-6 h-6 transform -translate-y-1/2 rounded-full top-1/2"
                                  onClick={() => {
                                    setBranchName("main");
                                  }}
                                  title="Clear branch name"
                                  disabled={
                                    !selectedInstallation ||
                                    (!newRepoName && isCreatingNewRepo)
                                  }
                                >
                                  <X className="h-3.5 w-3.5 text-[#898F99]" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      {branchName && !getBranchValidation().isValid && (
                        <div className="text-[#FF4545] text-sm mt-2">
                          {getBranchValidation().errorMessage}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="relative">
                        <div className="relative">
                          <div
                            ref={branchInputRef}
                            className={cn(
                              `flex items-center justify-between w-full h-14 bg-[#131314] border ${
                                dropdownState.showBranchDropdown
                                  ? "border-white/50"
                                  : "border-[#242424]"
                              } rounded-md p-3 text-[#C4C4CC] branch-input`,
                              conflictDetected
                                ? "border border-[#E38F4533]"
                                : secretProtectionError
                                ? "border border-[#A199FF33]"
                                : ""
                            )}
                            onClick={() => {
                              if (
                                !selectedInstallation ||
                                !selectedRepository ||
                                branches.length === 0
                              ) {
                                return;
                              }
                              // Close other dropdowns and toggle branch dropdown
                              updateDropdownState({
                                showRepositoryDropdown: false,
                                showInstallationDropdown: false,
                                showBranchDropdown:
                                  !dropdownState.showBranchDropdown,
                              });
                            }}
                            id="branch-selector"
                          >
                            <div className="flex items-center w-full">
                              <div className="relative flex-1">
                                {branchName ? (
                                  <div className="flex items-center">
                                    {branchName}
                                  </div>
                                ) : (
                                  <span className="text-[#8F8F98]">
                                    Select a branch
                                  </span>
                                )}
                              </div>
                            </div>
                            {(conflictDetected || secretProtectionError) && (
                              <img
                                src={
                                  conflictDetected ? OrangeError : PurpleError
                                }
                                alt="Branch"
                                className="w-6 h-6 mr-4"
                              />
                            )}
                            <ChevronDown
                              className={`h-4 w-4 text-[#898F99] transition-transform duration-200 ${
                                dropdownState.showBranchDropdown
                                  ? "rotate-180"
                                  : ""
                              }`}
                            />
                          </div>

                          {/* Branch dropdown using Portal */}
                          <PortalDropdown
                            isOpen={
                              branches.length > 0 &&
                              dropdownState.showBranchDropdown
                            }
                            triggerRef={branchInputRef}
                            className="branch-dropdown"
                            scrollableContainerRef={scrollableContainerRef}
                          >
                            <div className="flex flex-col gap-[6px] p-2 max-h-[210px]">
                              {branches.map((branch) => (
                                <div
                                  key={branch.name}
                                  className={cn(
                                    "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                                    branchName === branch.name &&
                                      "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                                  )}
                                  onClick={() => {
                                    setBranchName(branch.name);
                                    // Close all dropdowns
                                    updateDropdownState({
                                      showBranchDropdown: false,
                                      showRepositoryDropdown: false,
                                      showInstallationDropdown: false,
                                    });
                                  }}
                                >
                                  <div className="flex items-center justify-between w-full">
                                    <span>{branch.name}</span>
                                    {branchName === branch.name && (
                                      <Check className="h-4 w-4 text-[#1BB4CC]" />
                                    )}
                                  </div>
                                </div>
                              ))}
                              <div className="border-t border-[#ffffff12] mt-2"></div>
                              <div
                                className="px-3 min-h-[40px] rounded-[8px] py-2 text-[#4ADE80] hover:bg-[#4ADE80]/10 font-medium cursor-pointer"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  // Initialize the custom branch name with the current branch name
                                  // or a default value if no branch is selected
                                  setCustomBranchName(branchName || "main");
                                  // Show the custom branch input field and close the branch dropdown
                                  updateDropdownState({
                                    showCustomBranchInput: true,
                                    showBranchDropdown: false,
                                  });
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  <Plus className="w-4 h-4" />
                                  Create New Branch
                                </div>
                              </div>
                            </div>
                          </PortalDropdown>
                        </div>

                        {/* Custom branch input using Portal - shown when "Create New Branch" is selected */}
                        <PortalDropdown
                          isOpen={dropdownState.showCustomBranchInput}
                          triggerRef={branchInputRef}
                          className="branch-dropdown custom-branch-input"
                          scrollableContainerRef={scrollableContainerRef}
                        >
                          <div
                            className="p-3 space-y-3"
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="text-sm text-[#898F99]">
                              Enter a new branch name:
                            </div>
                            <div className="flex items-center w-full branch-input">
                              <div className="relative flex-1">
                                <Input
                                  type="text"
                                  value={customBranchName}
                                  onChange={(e) => {
                                    const value = replaceSpacesWithHyphens(
                                      e.target.value
                                    );
                                    setCustomBranchName(value);
                                  }}
                                  onKeyDown={(e) => {
                                    // Handle Enter key to apply the branch name
                                    if (
                                      e.key === "Enter" &&
                                      validateGitName(customBranchName)
                                    ) {
                                      setBranchName(customBranchName);
                                      updateDropdownState({
                                        showCustomBranchInput: false,
                                      });
                                    }
                                    // Handle Escape key to cancel
                                    if (e.key === "Escape") {
                                      updateDropdownState({
                                        showCustomBranchInput: false,
                                      });
                                      setCustomBranchName("");
                                    }
                                  }}
                                  placeholder="Branch name (e.g., feature/my-new-branch)"
                                  className="w-full p-2 bg-[#1A1A1C] border border-[#242424] rounded-md focus-visible:ring-0 focus-visible:border-white/50 focus-visible:ring-offset-0 text-white"
                                  autoFocus
                                />
                              </div>
                            </div>
                            {customBranchName &&
                              !validateGitName(customBranchName) && (
                                <div className="text-[#FF4545] text-xs">
                                  Invalid branch name. Names must follow Git
                                  naming rules.
                                </div>
                              )}
                            <div className="flex justify-end gap-2 mt-3">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Just close the custom branch input without changing the branch name
                                  updateDropdownState({
                                    showCustomBranchInput: false,
                                  });
                                  // Reset the custom branch name
                                  setCustomBranchName("");
                                }}
                                className="border-[#242424] text-gray-300 hover:bg-[#1a1a1c] bg-transparent"
                              >
                                Cancel
                              </Button>
                              <Button
                                type="button"
                                size="sm"
                                onClick={() => {
                                  // Only close if the custom branch name is valid
                                  if (validateGitName(customBranchName)) {
                                    // Update the actual branch name with the custom branch name
                                    setBranchName(customBranchName);
                                    updateDropdownState({
                                      showCustomBranchInput: false,
                                    });
                                  }
                                }}
                                disabled={!validateGitName(customBranchName)}
                                className="bg-[#00FF85] hover:bg-[#00FF85]/90 text-black font-medium"
                              >
                                Use Branch
                              </Button>
                            </div>
                          </div>
                        </PortalDropdown>
                      </div>
                      {branchName && !getBranchValidation().isValid && (
                        <div className="text-[#FF4545] text-sm mt-2">
                          {getBranchValidation().errorMessage}
                        </div>
                      )}
                      {/* Show loading indicator when fetching branches */}
                      {selectedRepository &&
                        !isCreatingNewRepo &&
                        isLoadingBranches && (
                          <div className="text-[#898F99] text-sm font-medium font-[Inter] flex items-center gap-1">
                            <Loader2 className="w-3 h-3 animate-spin" />
                            <span>Checking branches...</span>
                          </div>
                        )}

                      {/* Show success message when not loading and validation passes */}
                      {branchName &&
                        getBranchValidation().isValid &&
                        getBranchValidation().successMessage &&
                        selectedRepository &&
                        !isLoadingBranches && (
                          <div className="text-[#898F99] text-sm font-medium font-[Inter] flex items-center gap-1">
                            <img
                              src={InfoSquareIcon}
                              alt="Info"
                              className="w-3 h-3"
                            />
                            <span className="text-[#00FF85]">
                              {getBranchValidation().successMessage}
                            </span>
                          </div>
                        )}
                    </div>
                  )}

                  {conflictDetected && !showForcePushConfirmation ? (
                    <div className="space-y-[10px]">
                      {/* Changes conflict detected */}
                      <div className="bg-[#1F1B18]  rounded-[10px] p-3 flex items-start gap-6">
                        <div className="flex flex-col gap-2">
                          <div className="flex items-center gap-[6px]">
                            <img
                              alt="Warning"
                              src={OrangeError}
                              className="w-5 h-5"
                            />
                            <span className="text-[#E38F45] text-[14px] font-medium font-['Inter']">
                              Changes conflict detected
                            </span>
                          </div>
                          <p className="text-[#99897A] text-[13px] font-['Inter'] leading-[20px] font-medium">
                            Your changes conflict with what's already on the{" "}
                            <span className="text-[#FFE4CC] font-semibold font-['Inter']">
                              '{branchName}'
                            </span>{" "}
                            branch. You can force push to replace it, but this
                            will permanently erase what's currently there.
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => setShowForcePushConfirmation(true)}
                          className="ml-auto bg-[#2E2720]  text-[#CC9666] w-fit text-nowrap font-medium px-[10px] py-[6px] rounded-[6px] tracking-[-0.2px]"
                        >
                          Force Push anyway
                        </button>
                      </div>

                      {/* Recommended Solution */}
                      <div className="bg-[#00E5731A]  rounded-[10px] p-3 flex flex-col gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <img
                              alt="Warning"
                              src={GitBranch}
                              className="w-5 h-5"
                            />
                            <span className="text-[#00E573] font-medium text-[14px] font-['Inter']">
                              Recommended Solution
                            </span>
                          </div>
                          <p className="text-[#FFFFFF60] text-[13px] font-medium font-['Inter']">
                            <span className="font-semibold font-['Inter'] text-white">
                              Save your changes to a new branch.
                            </span>{" "}
                            This keeps your work safe and allows for proper
                            conflict resolution on GitHub.
                          </p>
                        </div>

                        <div className="space-y-2">
                          <label className="text-[#FFFFFF] text-[13px] font-['Inter'] font-medium">
                            Proposed branch name :
                          </label>
                          <Input
                            type="text"
                            value={proposedBranchName}
                            onChange={(e) =>
                              setProposedBranchName(e.target.value)
                            }
                            className="w-full px-4 py-3 bg-[#80FFF90D] border border-[#80FFF94D] text-white focus:border-[#80FFF9] focus:ring-[#80FFF94D]"
                            placeholder="Enter branch name"
                          />
                        </div>
                      </div>
                    </div>
                  ) : null}

                  {/* Secret Protection Error Display */}
                  {secretProtectionError && (
                    <div className="space-y-4">
                      <div className="bg-[#A199FF0D]  rounded-[10px] p-4">
                        <div className="flex items-start gap-3">
                          <div className="flex-1 space-y-3">
                            <div className="flex justify-between gap-2">
                              <div className="flex flex-col gap-2">
                                <div className="flex items-start gap-[6px]">
                                  <img
                                    src={ShieldLock}
                                    alt="Warning"
                                    className="w-5 h-5"
                                  />
                                  <h4 className="text-[#A199FF] font-medium font-['Inter'] text-[16px] mb-1 leading-[20px]">
                                    Security Protection Activated
                                  </h4>
                                </div>
                                <p className="text-[#A199FF50] font-medium text-[13px] font-['Inter'] leading-[20px]">
                                  We detected a secret key in your code and
                                  blocked this upload to protect you.
                                </p>
                              </div>

                              {secretProtectionError?.learnMoreUrl && (
                                <button
                                  type="button"
                                  onClick={() =>
                                    window.open(
                                      secretProtectionError?.learnMoreUrl,
                                      "_blank"
                                    )
                                  }
                                  className=" text-[#FFFFFF60] flex gap-1 items-center px-3 hover:bg-[#FFFFFF]/10 transition-all duration-150 ease-out hover:text-[#ffffff80] bg-[#FFFFFF0F] w-fit text-nowrap font-medium text-[16px] h-8 rounded-[8px]"
                                >
                                  Learn how to fix this?
                                </button>
                              )}
                            </div>

                            {/* Expandable Error Details */}
                            <div className="space-y-2">
                              <button
                                type="button"
                                onClick={() =>
                                  setSecretProtectionError((prev) =>
                                    prev
                                      ? {
                                          ...prev,
                                          isExpanded: !prev.isExpanded,
                                        }
                                      : null
                                  )
                                }
                                className={cn(
                                  "flex items-center gap-2 font-['Inter'] text-[#ffffff40] hover:text-[#FFFFFF80] text-[13px] font-medium transition-colors",
                                  {
                                    "text-[#FFFFFF80]":
                                      secretProtectionError?.isExpanded,
                                  }
                                )}
                              >
                                View error details
                                <ChevronDown
                                  className={`w-4 h-4 transition-transform ${
                                    secretProtectionError?.isExpanded
                                      ? "rotate-180"
                                      : ""
                                  }`}
                                />
                              </button>

                              {secretProtectionError?.isExpanded && (
                                <div className="bg-[#FFFFFF08] rounded-[8px] relative backdrop-blur-lg p-3 md:max-w-[600px] mt-2 max-h-[300px]">
                                  <CopyButton
                                    showIcon={true}
                                    iconOnly={true}
                                    className="absolute border-none opacity-45 hover:opacity-100 bg-none right-3 top-3"
                                    value={secretProtectionError?.fullError}
                                    tooltipText="Copy"
                                    copiedTooltipText="Copied"
                                    buttonClassName="p-0"
                                    feedbackType="tooltip"
                                    iconProps={{ size: 16 }}
                                    onCopy={() => {
                                      navigator.clipboard.writeText(
                                        secretProtectionError?.fullError
                                      );
                                    }}
                                  />
                                  <pre className="text-[#FFFFFF50] text-[14px] whitespace-pre-wrap break-words w-full leading-[16px] max-h-[200px] font-[400] overflow-y-auto font-jetbrains">
                                    {secretProtectionError?.fullError}
                                  </pre>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {pushError && !secretProtectionError && (
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <img src={RedError} alt="Error" className="w-5 h-5" />
                        <h4 className="text-red-400 font-medium text-[14px] font-[Inter] leading-[20px]">
                          Error
                        </h4>
                      </div>
                      <div className="bg-[#FFFFFF08] rounded-[8px] relative backdrop-blur-lg p-3 md:max-w-[600px] mt-2 max-h-[300px]">
                        <CopyButton
                          showIcon={true}
                          iconOnly={true}
                          value={pushError}
                          className="absolute border-none opacity-45 hover:opacity-100 bg-none right-3 top-3"
                          tooltipText="Copy"
                          copiedTooltipText="Copied"
                          buttonClassName="p-0"
                          feedbackType="tooltip"
                          iconProps={{ size: 16 }}
                          onCopy={() => {
                            navigator.clipboard.writeText(pushError);
                          }}
                        />
                        <pre className="text-[#FFFFFF50] text-[14px] whitespace-pre-wrap break-words w-full leading-[16px] max-h-[200px] font-[400] overflow-y-auto font-jetbrains">
                          {pushError}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </form>
            )}
          </div>

          <DialogFooter className="flex justify-end p-4  md:p-6 border-t rounded-b-lg border-[#242424] ">
            {showSuccessModal ? (
              /* Success Footer */
              <Button
                type="button"
                onClick={() => {
                  setShowSuccessModal(false);
                  setSuccessDetails(null);
                  onOpenChange(false);
                  if (onSuccess) {
                    onSuccess(successDetails as any);
                  }
                }}
                className="bg-[#272729] hover:bg-[#272729]/90 px-5 font-semibold text-[#DCDCE5]"
              >
                Okay, Got It
              </Button>
            ) : conflictDetected && !showForcePushConfirmation ? (
              /* Conflict Detection Footer */
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => onOpenChange(false)}
                  className="mr-2 text-[#ACACB2] bg-[#222224] hover:bg-[#22222490] px-4 pl-3 py-3 rounded-[28px] "
                  disabled={isProcessing}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleCreateBranchAndPush}
                  disabled={
                    isProcessing || !getProposedBranchValidation().isValid
                  }
                  className="bg-[#00FF85] font-semibold flex items-center gap-2 rounded-[28px] pl-4 pr-3 py-3  hover:bg-[#00FF85]/90 text-black"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating Branch...
                    </>
                  ) : (
                    <div className="flex items-center gap-2 font-semibold">
                      Create Branch & Push
                      <img
                        src={GitBranchDark}
                        alt="Branch"
                        className="w-6 h-6"
                      />
                    </div>
                  )}
                </button>
              </div>
            ) : showForcePushConfirmation ? (
              /* Force Push Confirmation Footer */
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => setShowForcePushConfirmation(false)}
                  className=" text-[#ACACB2] px-4 py-3 pr-3 bg-[#222224] hover:bg-[#22222490] rounded-[28px]"
                  disabled={isProcessing}
                >
                  <div className="flex items-center gap-2 font-medium">
                    <img src={GithubBack} alt="Back" className="w-6 h-6" />
                    Go Back
                  </div>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    handleForcePush();
                  }}
                  disabled={isProcessing}
                  className="bg-[#FFA04D] hover:bg-[#FFA04D]/90 flex items-center gap-2 rounded-[28px] pl-4 pr-3  text-[#111112] font-medium"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Force Pushing...
                    </>
                  ) : (
                    <div className="flex items-center font-semibold tracking-[-0.2px] gap-2 text-[#111112]">
                      Force Push to "{branchName}"
                      <img
                        src={SaveCloudIcon}
                        alt="Save Cloud"
                        className="w-6 h-6"
                      />
                    </div>
                  )}
                </button>
              </div>
            ) : (
              /* Normal Footer */
              <div className="flex justify-end w-full gap-2">
                <button
                  type="button"
                  onClick={() => onOpenChange(false)}
                  className=" bg-[#272729] rounded-full font-medium px-5 py-3 text-gray-300 hover:bg-[#27272990]"
                  disabled={isProcessing}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={
                    isProcessing ||
                    !validateFormSubmission({
                      jobId,
                      selectedInstallation,
                      isCreatingNewRepo,
                      newRepoName,
                      selectedRepository,
                      branchName,
                      repositories,
                      isLoading,
                    }).isValid
                  }
                  className={cn(
                    "bg-[#00FF85] rounded-full max-md:max-w-[200px] flex items-center gap-1 px-5 py-3 hover:bg-[#00FF85]/90 text-black font-semibold",
                    isProcessing && "opacity-50 cursor-not-allowed",
                    "disabled:opacity-50"
                  )}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      {isCreatingRepo
                        ? "Creating Repository..."
                        : isCreatingNewRepo
                        ? "Creating & Pushing..."
                        : "Pushing..."}
                    </>
                  ) : (
                    <div className="flex items-center gap-2">
                      {isCreatingNewRepo
                        ? "Create & Push to Github"
                        : "Push to Github"}
                      <img
                        src={SaveCloudIcon}
                        alt="Save Cloud"
                        className="w-6 h-6"
                      />
                    </div>
                  )}
                </button>
              </div>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
