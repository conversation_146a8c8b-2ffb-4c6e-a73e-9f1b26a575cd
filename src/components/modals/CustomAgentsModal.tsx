
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { motion } from "framer-motion";
import { useAuth } from "@/contexts";
import { trackExperimentalFeature } from "@/services/postHogService";
import ArrowDark from "@/assets/feature/arrow_dark.svg";
import useScreenSize from "@/hooks/useScreenSize";
import { useTabState } from "@/components/TabBar";

// Import agent-related assets
import BotSVG from "@/assets/bot.svg";
import RightBanner from "@/assets/pro/secondBanner.png"


interface StepInterface {
  number: string;
  title: string;
  description: string;
}

const CUSTOM_AGENT_STEPS: StepInterface[] = [
  {
    number: "1",
    title: "Define Your Agent's Personality & Role",
    description: "Describe who your agent is and what they're great at.",
  },
  {
    number: "2",
    title: "Map Out the Workflow & Logic",
    description: "Break down how they should approach tasks step by step.",
  },
  {
    number: "3",
    title: "Add Guardrails for Better Decisions",
    description: "Establish do's and don'ts so your agent makes the right decisions.",
  },
];

interface CustomAgentsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CustomAgentsModal: React.FC<CustomAgentsModalProps> = ({
  isOpen,
  onOpenChange,
}) => {
  const { user } = useAuth();
  const { isMobile } = useScreenSize();
  const { setTabs, setActiveTab } = useTabState();

  const handleCreateAgent = () => {
    // Track the create agent action
    trackExperimentalFeature(true, {
      userId: user?.id,
      component: 'CustomAgentsModal',
      source: 'create_agent_button'
    });

    // Generate a unique tab ID for the create agent screen
    const newTabId = `create-agent-${Date.now()}-${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    // Create new tab for create agent
    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title: "Create New Agent",
        path: "/create-agent",
        state: {
          tabId: newTabId,
          fromModal: true,
        },
      },
    ]);

    // Set the new tab as active
    setActiveTab(newTabId);

    // Close the modal
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="md:w-[900px] max-h-[80vh] max-w-[90vw] md:h-[650px] p-0 bg-[#18181A] rounded-2xl overflow-hidden">
        {/* Close Button */}
        <DialogClose className="absolute z-10 flex items-center justify-center w-8 h-8 text-white transition-all duration-200 rounded-full md:w-10 md:h-10 backdrop-blur-lg top-4 right-4 md:right-6 md:top-6 hover:bg-white/10">
          <X className="w-6 h-6 md:w-6 md:h-6" />
        </DialogClose>

        {/* Content */}
        <div className="relative flex w-[inherit] h-full max-md:flex-col-reverse">
          {/* Left Side - Content */}
          <div className="flex flex-col justify-center flex-1 gap-4 md:px-[40px] md:py-12">
            <div className="flex flex-col gap-2">
              <div className="flex flex-col gap-8 max-md:px-5 max-md:pt-6">
                <img
                  src={BotSVG}
                  alt="Custom Agents"
                  className="w-12 h-12 max-md:hidden"
                />
                {/* Title */}
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-[20px] md:text-[28px] leading-[32px] font-semibold tracking-[-2%] text-white"
                >
                  Introducing Custom Agents
                </motion.h1>
              </div>

              {/* Description */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-[#737780] max-md:px-5 text-[14px] font-['Inter'] md:text-[15px] text-wrap whitespace-pre-wrap break-words leading-[20px] md:leading-[24px]"
              >
                Create and monetize expert-level AI agents tailored to custom use cases.
              </motion.p>
            </div>

            {/* Steps List */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex flex-col gap-[16px] max-md:px-5"
            >
              {CUSTOM_AGENT_STEPS.map((step, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 bg-[#80FFF908] p-4 rounded-[8px]"
                >
                  <div className="flex flex-col gap-1">
                    <h3 className="text-[16px] text-[#80FFF9] font-medium">
                      {step.title}
                    </h3>
                    <p className="text-[14px] font-[500] text-[#80FFF966] font-['Inter']">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </motion.div>

            {/* Get Started Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              className="sticky max-md:px-5 bottom-4 max-md:pb-6"
            >
              <button
                type="button"
                onClick={handleCreateAgent}
                className="flex items-center max-md:max-h-[48px] mt-4 md:mt-[36px] justify-center w-full gap-4 text-[16px] font-semibold text-black transition-all duration-200 transform bg-white shadow-lg h-14 rounded-xl hover:bg-gray-100 hover:shadow-xl hover:-translate-y-1"
              >
                Create Your First Agent
                <img src={ArrowDark} alt="Arrow" className="w-5 h-5" />
              </button>
            </motion.div>
          </div>

          {/* Right Side - Visual Preview */}
          <div className="relative max-md:max-h-[200px] flex-1 overflow-hidden bg-gradient-to-br from-[#DD99FF20] to-[#BB77DD20]">
            <img 
              src={RightBanner}
              alt="Right Banner"
              className="object-cover w-full h-full md:object-fill "
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
