import React, { useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import EyeIcon from '@/components/icons/EyeIcon';
import MobileIcon from '@/components/icons/MobileIcon';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

export interface PreviewPromptProps {
  /** Whether to show the preview prompt */
  isVisible: boolean;
  /** URL for the preview iframe */
  previewUrl?: string;
  /** Function to call when the preview button is clicked */
  onPreviewClick: () => void;
  /** Custom text to display instead of default "Your Preview is ready" */
  promptText?: string;
  /** Custom className for the button */
  className?: string;
  /** Custom aria-label for accessibility */
  ariaLabel?: string;
  /** Whether to show the mobile icon */
  showMobileIcon?: boolean;
  /** Custom animation configuration */
  animationConfig?: {
    initial?: object;
    animate?: object;
    exit?: object;
    transition?: object;
  };

  /** Function to check if the user has opened the preview before */
  hasOpenedBefore?: () => boolean;
}

const defaultAnimationConfig = {
  initial: { opacity: 0, scale: 0.5, y: 10 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 0.95, y: 5 },
  transition: {
    type: "spring",
    stiffness: 400,
    damping: 25,
    mass: 0.8,
    duration: 2
  }
};

/**
 * PreviewPrompt component displays an animated prompt for mobile users to view their preview.
 * It shows a small iframe preview with an overlay and a call-to-action button.
 *
 * @example
 * ```tsx
 * <PreviewPrompt
 *   isVisible={showPreviewPrompt}
 *   previewUrl="https://example.com"
 *   onPreviewClick={() => openPreviewPanel()}
 *   promptText="Your app is ready!"
 * />
 * ```
 */
export const PreviewPrompt: React.FC<PreviewPromptProps> = ({
  isVisible,
  previewUrl,
  onPreviewClick,
  promptText = "",
  className = "",
  ariaLabel = "Open preview",
  showMobileIcon = true,
  animationConfig = defaultAnimationConfig,
  hasOpenedBefore = () => false
}) => {
  if (!isVisible) {
    return null;
  }

  const buttonClasses = `
    z-[9999] p-2 w-fit h-[56px] rounded-[12px] 
    flex items-center justify-center space-x-[10px] font-brockmann text-base font-semibold 
    transition-colors 
    shadow-[0px_0px_40px_0px_rgba(255,255,255,0.20)] 
    shadow-lg
    ${className}
  `.trim();

  const previewContainerClasses = `
    rounded 
    shadow-[0px_50px_14px_0px_rgba(0,0,0,0.00),0px_32px_13px_0px_rgba(0,0,0,0.03),0px_18px_11px_0px_rgba(0,0,0,0.10),0px_8px_8px_0px_rgba(0,0,0,0.16),0px_2px_4px_0px_rgba(0,0,0,0.19)]
    flex 
    w-14 
    h-full
    justify-center 
    items-center
    relative
    overflow-hidden
    bg-gray-300
  `.trim();


  const getText = useCallback(() => {
    if (hasOpenedBefore()) return 'View Preview';

    return 'Your Preview is ready';
  }, [hasOpenedBefore]);

  return (
    <AnimatePresence>
      <motion.div
        initial={animationConfig.initial}
        animate={animationConfig.animate}
        exit={animationConfig.exit}
        transition={animationConfig.transition}
      >
        <div className="flex justify-center mb-2 md:hidden">
          <button
            type="button"
            onClick={onPreviewClick}
            className={cn([buttonClasses, hasOpenedBefore() ? 'bg-[#303033] text-white' : 'bg-white text-[#0E0E0F]'])}
            aria-label={ariaLabel}
          >
            <div className={previewContainerClasses}>
              {previewUrl && (
                <iframe
                  src={previewUrl}
                  title="Preview"
                  className="w-14 h-full rounded"
                  style={{ zoom: 1 }}
                />
              )}
              <div className="absolute inset-0 bg-black/50 rounded pointer-events-none flex items-center justify-center">
                <EyeIcon color="#fff" />
              </div>
            </div>

            <div className="flex items-center justify-center space-x-1">
              <p>{promptText || getText()}</p>
              {!hasOpenedBefore() ? <MobileIcon className="w-5 h-5" /> : <ChevronDown className="w-6 h-6 text-white/40" />}
            </div>
          </button>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PreviewPrompt;
