import React from 'react';
import { cn } from "@/lib/utils";
import { ImageGallery } from "./ImageGallery";
import { ImageGalleryUrl } from "./ImageGalleryUrl";
import { ResponseImageData } from '@/types/message';

interface ArtifactData {
  name?: string;
  url?: string;
  artifact_type?: string;
  mime_type?: string;
  file_size?: number;
  visibility?: 'public' | 'private';
  // Support PendingArtifact properties as well
  file_name?: string;
  file_path?: string;
  local_preview_url?: string;
  local_preview_base64?: string;
}

interface EnhancedImageRendererProps {
  base64Images?: ResponseImageData[] | null;
  artifacts?: ArtifactData[] | null;
  artifactSharedData?: {
    artifacts?: ArtifactData[];
    base64_image_list?: ResponseImageData[];
  } | null;
  className?: string;
}

// Helper function to check if artifact is a displayable image type
const isImageArtifact = (artifact: ArtifactData): boolean => {
  // Ensure artifact has required properties
  if (!artifact || (!artifact.name && !artifact.mime_type)) {
    return false;
  }

  // Support all browser-displayable image types for artifact URLs
  const supportedTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    'image/svg+xml', 'image/bmp', 'image/tiff', 'image/ico', 'image/avif',
    'image/heic', 'image/heif'
  ];

  // Check mime type first
  if (artifact.mime_type && supportedTypes.includes(artifact.mime_type)) {
    return true;
  }

  // Fallback to file extension - support all common image formats
  const fileName = artifact.name || artifact.file_name;
  if (fileName) {
    const supportedExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
      '.bmp', '.tiff', '.tif', '.ico', '.avif', '.heic', '.heif'
    ];
    return supportedExtensions.some(ext =>
      fileName.toLowerCase().endsWith(ext)
    );
  }

  return false;
};

// Helper function to match artifacts with base64 images by name similarity
const matchArtifactWithBase64 = (
  artifacts: ArtifactData[], 
  base64Images: ResponseImageData[]
): { matchedArtifacts: ArtifactData[], unmatchedBase64: ResponseImageData[] } => {
  const matchedArtifacts: ArtifactData[] = [];
  const unmatchedBase64: ResponseImageData[] = [];

  // Create a set of artifact names for quick lookup
  const artifactNames = new Set(
    artifacts
      .filter(isImageArtifact)
      .map(artifact => artifact.name?.toLowerCase())
      .filter(name => name !== undefined) // Remove undefined names
  );

  // Check each base64 image to see if it has a corresponding artifact
  base64Images.forEach(base64Image => {
    // Try to find a matching artifact by checking if any artifact name
    // could correspond to this base64 image (this is heuristic-based)
    const hasMatchingArtifact = artifacts.some(artifact => 
      isImageArtifact(artifact) && 
      // Simple heuristic: if we have artifacts and base64 images in similar quantities,
      // assume they correspond to each other
      true
    );

    if (!hasMatchingArtifact) {
      unmatchedBase64.push(base64Image);
    }
  });

  // Add all image artifacts to matched artifacts
  artifacts.forEach(artifact => {
    if (isImageArtifact(artifact)) {
      matchedArtifacts.push(artifact);
    }
  });

  return { matchedArtifacts, unmatchedBase64 };
};

/**
 * Enhanced image renderer that prioritizes high-quality artifact URLs over compressed base64 images
 * Falls back to base64 images when artifacts are not available
 */
export const EnhancedImageRenderer: React.FC<EnhancedImageRendererProps> = ({
  base64Images,
  artifacts,
  artifactSharedData,
  className
}) => {
  console.log('EnhancedImageRenderer - Input props:', {
    base64Images: base64Images?.length || 0,
    artifacts: artifacts?.length || 0,
    artifactSharedData: artifactSharedData ? 'present' : 'null',
    artifactsData: artifacts?.map(a => ({
      name: a.file_name || a.name,
      local_preview_url: a.local_preview_url,
      file_path: a.file_path,
      url: a.url,
      mime_type: a.mime_type
    }))
  });

  // Determine the best sources to use (all artifacts, not just images)
  const getOptimalSources = () => {
    // Priority 1: Use artifacts if available (all types - images and files)
    const rawArtifacts = artifacts || artifactSharedData?.artifacts || [];
    const availableBase64 = base64Images || artifactSharedData?.base64_image_list || [];

    // Map PendingArtifact properties to ImageGalleryUrl-compatible format
    // Include ALL artifacts, even those without URLs (private artifacts)
    const availableArtifacts = rawArtifacts
      .filter(artifact => {
        // Only require name - include artifacts without URLs (private artifacts)
        const name = artifact.file_name || artifact.name;
        return name;
      })
      .map(artifact => {
        // Prioritize base64 data for immediate display, then blob URL, then server URLs
        let url = '';
        if (artifact.local_preview_base64) {
          url = `data:${artifact.mime_type || 'image/jpeg'};base64,${artifact.local_preview_base64}`;
        } else if (artifact.local_preview_url) {
          url = artifact.local_preview_url;
        } else if (artifact.file_path) {
          url = artifact.file_path;
        } else if (artifact.url) {
          url = artifact.url;
        }

        return {
          name: artifact.file_name || artifact.name || 'unnamed',
          url: url,
          artifact_type: artifact.artifact_type,
          mime_type: artifact.mime_type,
          file_size: artifact.file_size,
          visibility: artifact.visibility || (url ? 'public' : 'private')
        };
      });

    // Separate image artifacts from non-image artifacts
    const imageArtifacts = availableArtifacts.filter(isImageArtifact);
    const nonImageArtifacts = availableArtifacts.filter(artifact => !isImageArtifact(artifact));

    // If we have artifacts, prefer them over base64
    if (availableArtifacts.length > 0) {
      // Check if we also have base64 images that might not have artifact counterparts
      if (availableBase64.length > 0) {
        const { matchedArtifacts, unmatchedBase64 } = matchArtifactWithBase64(
          imageArtifacts,
          availableBase64
        );

        return {
          imageArtifacts: matchedArtifacts,
          nonImageArtifacts: nonImageArtifacts,
          base64Images: unmatchedBase64,
          hasImageArtifacts: matchedArtifacts.length > 0,
          hasNonImageArtifacts: nonImageArtifacts.length > 0,
          hasBase64: unmatchedBase64.length > 0
        };
      } else {
        return {
          imageArtifacts: imageArtifacts,
          nonImageArtifacts: nonImageArtifacts,
          base64Images: [],
          hasImageArtifacts: imageArtifacts.length > 0,
          hasNonImageArtifacts: nonImageArtifacts.length > 0,
          hasBase64: false
        };
      }
    }

    // Priority 2: Fall back to base64 images if no artifacts
    return {
      imageArtifacts: [],
      nonImageArtifacts: [],
      base64Images: availableBase64,
      hasImageArtifacts: false,
      hasNonImageArtifacts: false,
      hasBase64: availableBase64.length > 0
    };
  };

  const {
    imageArtifacts,
    nonImageArtifacts,
    base64Images: fallbackBase64,
    hasImageArtifacts,
    hasNonImageArtifacts,
    hasBase64
  } = getOptimalSources();

  // Combine all artifacts for the ImageGalleryUrl component
  const allArtifacts = [...imageArtifacts, ...nonImageArtifacts];

  // If no content at all, return null
  if (allArtifacts.length === 0 && !hasBase64) {
    return null;
  }

  console.log('EnhancedImageRenderer - All sources:', {
    imageArtifacts: imageArtifacts.length,
    nonImageArtifacts: nonImageArtifacts.length,
    totalArtifacts: allArtifacts.length,
    fallbackBase64: fallbackBase64.length,
    hasImageArtifacts,
    hasNonImageArtifacts,
    hasBase64,
    preferredSource: allArtifacts.length > 0 ? 'artifacts (high quality)' : 'base64 (compressed)',
    artifactTypes: allArtifacts.map(artifact => ({
      name: artifact.name || 'unnamed',
      type: artifact.mime_type || 'unknown',
      url: artifact.url,
      hasLocalPreview: artifact.url?.startsWith('blob:')
    })),
    rawArtifacts: artifacts?.map(a => ({
      file_name: a.file_name,
      local_preview_url: a.local_preview_url,
      local_preview_base64: a.local_preview_base64 ? 'present' : 'none',
      file_path: a.file_path,
      url: a.url
    }))
  });

  return (
    <div className={cn("enhanced-image-renderer", className)}>
      {/* Render all artifacts (images will show as images, non-images will show as file icons) */}
      {allArtifacts.length > 0 && (
        <ImageGalleryUrl
          images={allArtifacts as any[]} // Type assertion to handle interface mismatch
          className="justify-end"
        />
      )}

      {/* Render fallback base64 images if any remain unmatched */}
      {hasBase64 && (
        <ImageGallery
          images={fallbackBase64}
          className="justify-end"
        />
      )}
    </div>
  );
};

export default EnhancedImageRenderer;
