import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts";
import GiftSVG from "@/assets/referral/gift.svg";
import GlowBulb from "@/assets/referral/glow-bulb.svg";
import GlowBulbMobile from "@/assets/referral/bulbGlow.svg"
import BlueCoinSVG from "@/assets/referral/blue-coin.svg"
import { trackReferralEvent } from "@/services/postHogService";

interface ReferralBannerProps {
  onOpenReferralModal: () => void;
}

export default function ReferralBanner({
  onOpenReferralModal,
}: ReferralBannerProps) {
  const { session, user } = useAuth();
  const navigate = useNavigate();

  const handleGetFreeCreditsClick = () => {
    // Track the button click
    trackReferralEvent('get_free_credits_clicked', {
      userId: user?.id,
      userEmail: user?.email,
      source: 'banner'
    });

    // Check if user is logged in
    if (!session || !user) {
      navigate("/login");
      return;
    }

    onOpenReferralModal();
  };

  return (
    <div className="flex items-center w-full px-[10px] justify-between py-1 pb-2">
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2">
          <img
            src={BlueCoinSVG}
            alt="Glow Bulb"
            className="w-5 h-5"
          />
          <span className="text-[#80FFF9] text-[15px] font-medium max-md:hidden">
            Earn 20 Credits
          </span>
          <span className="text-[#80FFF9] text-[13px] font-medium md:hidden">
            Invite Friends
          </span>
        </div>
        <span className="text-[#FFFFFF] text-[15px] font-medium max-md:hidden">
          Share with a friend and get 20 credits when they subscribe
        </span>
      </div>
      <button
        type="button"
        onClick={handleGetFreeCreditsClick}
        className="bg-[#80FFF91A] font-medium max-h-[32px] text-[#80FFF9] flex max-md:text-[13px] items-center gap-2 p-2 pr-3 rounded-[10px]"
      >
        <img src={GiftSVG} alt="Gift" className="w-4 h-4" />
        Get Free Credits
      </button>
    </div>
  );
}
