import { useState, useRef, useEffect, memo } from "react";
import { cn } from "@/lib/utils";
import { EnhancedImageRenderer } from "./EnhancedImageRenderer";
import { MessageActions } from "./MessageActions";
import { calculateLinePositions, highlightSearchText, renderLine } from "./chat/TextRenderUtils";
import { formatMessageTimestamp } from "@/lib/utils/dateFormatter";
import Logs from "@/assets/deployment/Logs.svg"
import LogWithError from "@/assets/deployment/LogWithError.svg"
import MdiCheck from "@/assets/deployment/mdi_check-all.svg"
import BlueCheck from "@/assets/upload/blue_check.svg"
import { CUSTOM_TEXT } from "@/constants/constants";
interface MessageItemProps {
  message: any;
  handleRollback?: () => void;
  isCloudFlow?: boolean;
  searchActive?: boolean;
  searchHighlights?: Array<{ startIndex: number, endIndex: number }>;
  isMatchingMessage?: boolean;
  hideImportantActions?: boolean;
  isSubagent?: boolean;
  podIsPaused?: boolean;
}

const MessageItemComponent = ({
  message,
  handleRollback,
  isCloudFlow,
  searchActive = false,
  searchHighlights = [],
  isSubagent = false,
  isMatchingMessage = false,
  hideImportantActions = false,
  podIsPaused = false,
}: MessageItemProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isMatchingMessage && searchActive && !isExpanded) {
      setIsExpanded(true);
    }
  }, [isMatchingMessage, searchActive, isExpanded]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
    } catch (err) {
      console.error("Failed to copy text:", err);
    }
  };

  // Extract logs and clean content for display
  const extractLogsFromContent = (content: string) => {
    const parts = content.split(CUSTOM_TEXT.logsIdentifier);
     const assetShared = content.includes("USER SHARED ASSETS WITH AGENT");


    if (parts.length === 3) {
      // Format: [message_before_logs, logs_content, message_after_logs]
      return {
        cleanContent: '',
        logs: parts[1].split('\n').filter(log => log.trim() !== ''),
        hasLogs: true,
        hasError: parts[1].toLowerCase().includes('error') ||
                  parts[1].toLowerCase().includes('failed') ||
                  parts[1].toLowerCase().includes('exception') ||
                  parts[1].toLowerCase().includes('fatal') ||
                  parts[1].toLowerCase().includes('critical')
      };
    }
    return {
      cleanContent: content,
      logs: [],
      hasLogs: false,
      hasError: false,
      assetShared
    };
  };

  const { cleanContent, logs, hasLogs, hasError, assetShared } = extractLogsFromContent(message.content);

  // Debug message data
  if (message.artifacts || message.base64_image_list?.length > 0) {
    console.log('MessageItem - Message with images/artifacts:', {
      messageId: message.id,
      role: message.role,
      base64_image_list: message.base64_image_list?.length || 0,
      artifacts: message.artifacts?.length || 0,
      artifactData: message.artifacts?.map((a: any) => ({
        name: a.name || a.file_name,
        url: a.url,
        local_preview_url: a.local_preview_url,
        mime_type: a.mime_type
      }))
    });
  }

  // Use clean content for display (without logs)
  const displayContent = cleanContent;
  const linePositions = calculateLinePositions(displayContent);
  const contentLines = displayContent.split("\n");

  return (
    <div
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn("relative group flex justify-center", "mb-0")}
      ref={ref}
      id={message.id}
    >
      <div
        className={cn(
          "my-4 md:my-8 group flex flex-col space-y-[10px] w-full  md:max-w-4xl md:pl-12 justify-end items-end "
        )}
      >
        <EnhancedImageRenderer
          base64Images={message.base64_image_list}
          artifacts={message.artifacts}
          artifactSharedData={message.artifact_shared_data}
          className="justify-end"
        />
        <div
          className={cn(
            "px-4 py-1 rounded-xl rounded-br-none overflow-hidden space-y-[10px] flex items-end",
            message.role === "user" ? "bg-[#273638] max-4-xl w-4xl" : "", hasLogs || assetShared ? "hidden" : ""
          )}
        >
          <div className="flex items-start gap-3">
            <div className="flex-1 min-w-0">
              <div className="prose prose-invert max-w-none">
                {message.role === "user" ? (
                  contentLines.map((line, i) =>
                    renderLine(
                      line,
                      i,
                      true,
                      searchActive,
                      searchHighlights,
                      linePositions[i]
                    )
                  )
                ) : isExpanded || (isMatchingMessage && searchActive) ? (
                  contentLines.map((line, i) =>
                    renderLine(
                      line,
                      i,
                      false,
                      searchActive,
                      searchHighlights,
                      linePositions[i]
                    )
                  )
                ) : (
                  <p className="text-[#C4C4CC] font-['Inter'] text-[15px] leading-6 font-normal">
                    {searchActive &&
                    searchHighlights &&
                    searchHighlights.length > 0
                      ? // Highlight search matches in the first line
                        highlightSearchText(
                          contentLines[0],
                          searchHighlights.filter(
                            (h) => h.startIndex < contentLines[0].length
                          )
                        )
                      : contentLines[0]}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Log indicators - show when logs are hidden from user */}
        {hasLogs && (
          <div className="bg-[#1E1E1F] rounded-[10px] p-3 flex max-md:w-full md:min-w-[65%]  md:gap-3 items-center justify-between">
            <div className="flex items-center gap-2">
              <img src={hasError ? LogWithError : Logs} alt="Logs" className="w-6 h-6" />
              <span className="max-md:text-[14px]">Build {hasError ? "Error" : ""} Logs shared with agent</span>
            </div>

            <div className="flex items-center gap-3 max-md:hidden">
              <span className="text-[#FFFFFF80] max-md:text-[10px]">{logs.length} log entries</span>
              <div className="w-1 h-1 rounded-full bg-[#FFFFFF33]"></div>
              <img src={MdiCheck} alt="Check" className="w-4 h-4 md:w-6 md:h-6" />
            </div>
          </div>
        )}

           {assetShared && (
          <div className="bg-[#80FFF90D] rounded-[10px] p-3 flex rounded-br-[0px]  md:gap-3 items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="max-md:text-[14px] font-medium text-[#80FFF9E5]">Assets Shared with Agent</span>
            </div>

            <img src={BlueCheck} alt="Check" className="w-4 h-4 md:w-6 md:h-6" />
            
          </div>
        )}


        <div
          className={cn(
            "transition-opacity duration-200 h-5 text-right",
            isHovered ? "opacity-100" : "opacity-100"
          )}
        >
          <div
            className={cn(
              "inline-flex items-center gap-2 text-[#5C5F66]",
              !isHovered
                ? "opacity-100"
                : "opacity-0 pointer-events-none absolute"
            )}
          >
            <span className="font-['Berkeley Mono Trial'] text-[13px]  md:text-[15px] leading-[20px]">
              {formatMessageTimestamp(message.timestamp)}
            </span>
          </div>
          <div className="relative z-50">
            <MessageActions
              isHovered={isHovered}
              message={message as any}
              handleRollback={handleRollback}
              isCloudFlow={isCloudFlow || false}
              onCopy={handleCopy}
              podIsPaused={podIsPaused}
              className={cn(
                "inline-flex items-center gap-1",
                isHovered
                  ? "opacity-100 pointer-events-auto"
                  : "opacity-0 absolute pointer-events-none"
              )}
              hideImportantActions={hideImportantActions}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const MessageItem = memo(MessageItemComponent, (prevProps, nextProps) => {
  // Custom comparison function to prevent re-renders when content hasn't changed
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.timestamp === nextProps.message.timestamp &&
    prevProps.searchActive === nextProps.searchActive &&
    prevProps.isMatchingMessage === nextProps.isMatchingMessage &&
    JSON.stringify(prevProps.searchHighlights) === JSON.stringify(nextProps.searchHighlights)
  );
});