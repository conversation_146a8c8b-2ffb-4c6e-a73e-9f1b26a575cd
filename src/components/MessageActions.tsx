import { cn } from "@/lib/utils";
import HistoryIcon from "@/assets/history.svg";
import { AgentMessage } from "@/types/message";
import CopyButton from "./CopyButton";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { Bug } from "lucide-react";
import { useState } from "react";
import { DebugInfoModal } from "./modals/DebugInfoModal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import useScreenSize from "@/hooks/useScreenSize";

interface MessageActionsProps {
  isHovered: boolean;
  message: AgentMessage;
  handleRollback?: () => void;
  onCopy?: () => void;
  isCloudFlow: boolean;
  className?: string;
  podIsPaused?: boolean;
  hideImportantActions: boolean;
  forkStatus?: "running" | "success" | "failed" | null;
  agentStatus?: any;
}

// Removed defaultCopyHandler as the functionality is now inline in the CopyButton component

export const MessageActions = ({
  isHovered,
  handleRollback,
  message,
  isCloudFlow,
  className,
  hideImportantActions,
  forkStatus,
  onCopy,
  agentStatus,
  podIsPaused = false,
}: MessageActionsProps) => {
  const isEmergentUser = useIsEmergentUser();
  const [isDebugModalOpen, setIsDebugModalOpen] = useState(false);
  const { isMobile } = useScreenSize();

  const handleDebug = () => {
    setIsDebugModalOpen(true);
  };

  const getRollbackTitle = () => {
    if (podIsPaused) {
      return "Please wake up the agent before rolling back";
    }
    if (agentStatus === "subagent_running") {
      return "Please wait for the subagent to finish before rolling back";
    }
    if (agentStatus === "subagent_stopping") {
      return "Can't rollback at the moment - Subagent is stopping";
    }
    if (agentStatus === "subagent_waiting") {
      return "Please finish the subagent before rolling back";
    } 
    if(agentStatus === "running") {
      return "Can't rollback at the moment - Agent is running";
    }
    return "";
  };

  return (
    <>
      <div
        className={cn(
          "inline-flex items-center gap-2",
          isHovered
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 absolute pointer-events-none",
          className
        )}
      >
        {isCloudFlow && message.enable_rollback && !hideImportantActions && forkStatus !== "running" && (
          <TooltipProvider>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <button
                  type="button"
                  className={`p-[6px] ${isMobile ? '' : ' pr-[10px]'} bg-[#222224] hover:bg-[#3c3c40] text-[#7B7B80] disabled:opacity-50 hover:text-[#ccc] flex items-center space-x-1 rounded-lg transition-colors`}
                  onClick={()=>{
                    if (handleRollback && agentStatus === "waiting") handleRollback();
                  }}
                  disabled={podIsPaused || agentStatus !== "waiting" }
                >
                  <div className="flex items-center justify-center w-5 h-5">
                    <img
                      src={HistoryIcon}
                      className="w-4 h-4 text-[#5C5F66]"
                      alt="Rollback"
                    />
                  </div>
                  {!isMobile && <span className="text-[15px] leading-[20px] font-['Inter'] font-medium">
                    Rollback
                  </span>}
                </button>
              </TooltipTrigger>
              <TooltipContent className={cn("bg-[#272829]/95 backdrop-blur-md border-[#2E2F34] text-white shadow-lg", !getRollbackTitle() ? "hidden" : "")}>
                {getRollbackTitle()}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {isEmergentUser && message.containerId && !isMobile && (
          <button
            type="button"
            className="p-[6px] pr-[10px] bg-[#222224] hover:bg-[#3c3c40] text-[#7B7B80] hover:text-[#ccc] flex items-center space-x-1 rounded-lg transition-colors"
            onClick={handleDebug}
          >
            <div className="flex items-center justify-center w-5 h-5">
              <Bug className="w-4 h-4 text-[#5C5F66]" />
            </div>
            <span className="text-[15px] leading-[20px] font-['Inter'] font-medium">
              Debug
            </span>
          </button>
        )}

        <CopyButton
          value={() => {
            // Get the content to copy based on message type
            if (message.action) {
              return [
                message.content,
                message.action && `\nAction: ${message.action}`,
                message.observation && `\nObservation: ${message.observation}`,
                message.thought && `\nThought: ${message.thought}`,
              ]
                .filter(Boolean)
                .join("");
            }
            return message.content;
          }}
          feedbackType="icon-text"
          defaultText={isMobile ? "" : "Copy"}
          copiedText="Copied"
          showIcon={true}
          onCopy={(_) => {
            if (onCopy) onCopy();
          }}
          copiedClassName=""
          buttonClassName="p-[6px] bg-white/5 hover:bg-white/15 text-[15px] font-inter font-medium  text-white/50 hover:text-white flex items-center space-x-1 rounded-lg transition-colors"
          iconProps={{ size: 16, className: "text-white/60" }}
          className="max-h-full bg-none"
        />
      </div>

      {/* Debug Info Modal */}
      <DebugInfoModal
        isOpen={isDebugModalOpen}
        onOpenChange={setIsDebugModalOpen}
        containerId={message.containerId}
        message={message}
      />
    </>
  );
};
