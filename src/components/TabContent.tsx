import React from 'react';
import Home from '@/features/home/<USER>';
import { ChatScreen } from '@/features/chat/ChatScreen';
import { CreateAgentScreen } from '@/features/create-agent/CreateAgentScreen';
import { CreateSubagentScreen } from '@/features/create-agent/CreateSubagentScreen';

const getChatIdFromPath = (path: string) => {
  const pathParts = path.split('/');
  return pathParts.length >= 3 ? pathParts[2] : null;
};

interface TabContentProps {
  path: string;
  tabId: string;
}

export const TabContent: React.FC<TabContentProps> = React.memo(({ path, tabId }) => {
  const Content = React.useMemo(() => {
    const pathParts = path.split('/');
    const pathBase = `/${pathParts[1]}`;

    switch (pathBase) {
      case '/':
        return Home;
      case '/chat':
        const chatId = getChatIdFromPath(path) || tabId;
        return () => <ChatScreen tabId={chatId} />;
      case '/create-agent':
        return () => <CreateAgentScreen tabId={tabId} />;
      case '/create-subagent':
        return () => <CreateSubagentScreen tabId={tabId} />;
      default:
        return Home;
    }
  }, [path, tabId]);

  return <Content />;
})