import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts";
import BotSVG from "@/assets/bot.svg";
import AgentGlow from "@/assets/pro/agentGlow.svg";
import AgentGlowMobile from "@/assets/pro/agentGlowMobile.svg";
import { trackExperimentalFeature } from "@/services/postHogService";
import PlusIcon from "./icons/PlusIcon";


interface CustomAgentsBannerProps {
  onOpenCustomAgentsModal: () => void;
}

export default function CustomAgentsBanner({
  onOpenCustomAgentsModal,
}: CustomAgentsBannerProps) {
  const { session, user } = useAuth();
  const navigate = useNavigate();

  const handleCreateAgentClick = () => {
    // Track the button click
    trackExperimentalFeature(true, {
      userId: user?.id,
      userEmail: user?.email,
      source: 'custom_agents_banner',
      component: 'CustomAgentsBanner'
    });

    // Check if user is logged in
    if (!session || !user) {
      navigate("/login");
      return;
    }

    onOpenCustomAgentsModal();
  };

  return (
    <div className="flex items-center w-full px-[10px] justify-between py-1 pb-2">
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2">
          <img
            src={BotSVG}
            alt="Custom Agent"
            className="w-6 h-6"
          />
          <span className="text-[#F8FF99] text-[15px] font-medium max-md:hidden">
            Introducing Custom Agents
          </span>
          <span className="text-[#F8FF99] text-[13px] font-medium md:hidden">
            Custom Agents
          </span>
        </div>
        <span className="text-[#FFFFFF] text-[15px] font-medium max-md:hidden">
          Build your own AI dream team
        </span>
      </div>
      <button
        type="button"
        onClick={handleCreateAgentClick}
        className="bg-[#F8FF991A] font-medium max-h-[32px] text-[#F8FF99] flex max-md:text-[13px] items-center gap-2 p-2 pr-3 rounded-[10px]"
      >
        <PlusIcon size={20} color="#F8FF99" />
        Create Your Agent
      </button>
    </div>
  );
}
