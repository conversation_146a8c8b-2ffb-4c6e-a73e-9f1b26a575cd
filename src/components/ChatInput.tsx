import { useState, useRef, useEffect, useCallback, useMemo, memo } from "react";
import { Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { agentApi } from "@/services/agentApi";
import { useToast } from "@/hooks/use-toast";
import AttachIcon from "@/assets/attach.svg";
import UploadIcon from "@/assets/folder-add.svg";
import PauseIcon from "@/assets/pause.svg";
import SendIcon from "@/assets/send.svg";
import { GitHubPushModal } from "./modals/GitHubPushModal"; // Import the GitHubPushModal component

import { useImageAttachments } from "@/hooks/useImageAttachments";
import { useFileAttachments } from "@/hooks/useFileAttachments";
import { ImageData } from "@/types/message";
import AttachmentCross from "@/assets/attachment-cross.svg";
import { useTabState } from "./TabBar";
import WhiteGithubIcon from "@/assets/white-github.svg";
import { useGitHub } from "@/hooks/useGitHubAPI";
import { useAppDispatch, useAppSelector } from "@/hooks/reduxHooks";
import { selectPendingArtifacts, clearPendingArtifacts, selectActiveTab, selectChatInputText, setChatInputText, clearChatInputText } from "@/store/tabSlice";

import { useSubmitHITLMutation } from "@/store/api/apiSlice";
import { TokenDisplay } from "./TokenDisplay";
import { cn } from "@/lib/utils";
import { useAuth, useCredits } from "@/contexts";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import {
  getSubagentName,
  getSubagentGradient,
  getDynamicStatusColor,
  getSubagentStyle,
} from "@/utils/commonUtils";
import { calculateAgentStatus, type AgentStatus } from "@/utils/agentStatusUtils";
// @ts-ignore
import animatedSpinner from "@/assets/animated-spinner.gif";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipArrow,
} from "@/components/ui/tooltip";

//Assets

import GithubDot from "@/assets/github/git-dot.svg"  
import ForkSVG from "@/assets/fork/fork.svg";
import { URL_LINKS } from "@/constants/constants";
// import { mobileDevice } from "@/lib/utils/deviceDetection";
import { hasSeenForkIntro, markForkIntroAsSeen } from "@/lib/utils/modalStateManager";
import CloseIcon from "@/assets/upload/close.svg"
import useScreenSize from "@/hooks/useScreenSize";
import { isAutoBudgetUpdateEnabled, useIsUploadAssetEnabled } from "@/services/postHogService";
import { calculateNextBudget } from "@/lib/utils/budget";
import { useBudget } from "@/hooks/useBudget";
import ChatPreview from "./upload/ChatPreview";
import { useImageCompression } from "@/hooks/useImageCompression";
import { PendingArtifact } from "@/types/artifact";


// Memoized status components
const StatusIndicator = memo(
  ({ status, colors }: { status: string; colors: any }) => {
    // Use the color directly from the colors object instead of parsing from class
    const dotColor = colors[status].color || "#5FD3F3";

    return (
      <div
        className={cn(
          `w-4 h-4 rounded-lg flex justify-center items-center`,
          status != "stopped" && status != "exit_cost" && "animate-status-pulse"
        )}
        style={
          {
            backgroundColor: `${dotColor}20`, // 20% opacity for background
            "--status-pulse-color": `${dotColor}70`, // 70 is hex for ~44% opacity
            "--status-pulse-transparent": `${dotColor}00`, // 00 is hex for 0% opacity
          } as React.CSSProperties
        }
      >
        <div
          className="w-2 h-2 rounded"
          style={{ backgroundColor: dotColor }}
        />
      </div>
    );
  }
);

const StatusText = memo(
  ({
    status,
    colors,
    statusText,
  }: {
    status: string;
    colors: any;
    statusText: any;
  }) => {
    return (
      <span
        className="text-[15px] md:text-[16px] font-medium md:text-nowrap"
        style={{ color: colors[status].color }}
      >
        {statusText[status]}
      </span>
    );
  }
);

interface ChatInputProps {
  onSubmit: (message: string, images: any, artifacts?: PendingArtifact[]) => void;
  placeholder?: string;
  isDisabled?: boolean;
  agentState?: {
    agent_running: boolean;
    job_running: boolean;
  } | null;
  containerId?: string | null;
  jobDetails?: {
    job_id: string;
    traj_path?: string;
    container_id?: string;
    createdBy?: string;
    asset_upload_enabled?: boolean;
  };
  lastGithubUsed?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null;
  acc_cost?: number;
  max_budget?: number;
  onAddToken: () => void;
  onDockerCommit?: () => void;
  isCloudFlow: boolean;
  clientRefId?: string;
  modelName?: string;
  agentName?: string;
  isSubagentActive?: boolean;
  hideStatus?: boolean;
  hideTokens?: boolean;
  disableWhenSubagentActive?: boolean;
  onPause?: ({ origin }: { origin?: "MainInput" | "SubagentButton" }) => void;
  onOpenSubagentPanel?: () => void;
  showFinishSubagentText?: boolean;
  pauseWasClicked?: boolean;
  showImages?: boolean;
  inputValue?: string;
  onImagesChange?: (images: ImageData[]) => void;
  onGitHubPush?: (repoDetails: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  }) => void;
  isEmergentUser?: boolean;
  handleDeploy?: () => void;
  subagentName?: string;
  fromSubagentPanel?: boolean;
  isPauseLoading?: boolean;
  buildMode: "brainstorming_requested" | "brainstorming_done" | "build" | null;
  forked_status?: "running" | "success" | "failed" | null;
  modalOpen?: {
    fork: boolean;
  };
  setModalOpen?: (open: { fork: boolean }) => void;
  onOpenUploadAssetsModal?: (files?: File[]) => void;
}

export const ChatInput = memo(
  ({
    onSubmit,
    placeholder = "Message Neo",
    isDisabled,
    agentState,
    containerId,
    jobDetails,
    acc_cost,
    max_budget,
    onAddToken,
    isCloudFlow,
    clientRefId,
    inputValue,
    modelName,
    agentName,
    isSubagentActive,
    hideStatus = false,
    hideTokens = false,
    disableWhenSubagentActive = false,
    onPause,
    onOpenSubagentPanel,
    showFinishSubagentText,
    pauseWasClicked,
    onImagesChange,
    lastGithubUsed,
    showImages = true,
    subagentName,
    onGitHubPush,
    modalOpen,
    setModalOpen,
    fromSubagentPanel = false,
    isPauseLoading: externalIsPauseLoading,
    buildMode,
    forked_status,
    onOpenUploadAssetsModal,
  }: ChatInputProps) => {
    const { toast } = useToast();

    // Redux hooks for pending artifacts and chat input persistence (must be before useState)
    const dispatch = useAppDispatch();
    const activeTabId = useAppSelector(selectActiveTab);
    const pendingArtifacts = useAppSelector(state => selectPendingArtifacts(state, activeTabId));
    // Only use persisted chat input text for main chat input, not subagent panel
    const persistedChatInputText = useAppSelector(state =>
      fromSubagentPanel ? "" : selectChatInputText(state, activeTabId)
    );
    const [submitHITL] = useSubmitHITLMutation();

    const [localValue, setLocalValue] = useState(persistedChatInputText);
    const [isGitHubPushModalOpen, setIsGitHubPushModalOpen] = useState(false);
    const [sendAsHimRequest, setSendAsHimRequest] = useState(false);
    const [showForkTooltip, setShowForkTooltip] = useState(false);
    const [hasSeenForkIntroLocal, setHasSeenForkIntroLocal] = useState(hasSeenForkIntro());
    const [showAttachDropdown, setShowAttachDropdown] = useState(false);

    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [agentStatus, setAgentStatus] = useState<AgentStatus>("stopped");

    // Use external pause loading state if provided, otherwise use local state
    const isPauseLoading = externalIsPauseLoading ?? false;
    const { isConnected: isGitHubConnected } = useGitHub();

    const { user } = useAuth();

    const { credits } = useCredits();
    const isEmergentUser = useIsEmergentUser();
    const { getPendingMessage, getActiveTab, removePendingMessage } =
      useTabState();

    // Track drag state for drag and drop functionality
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    const [isUploadAssetEnabled, setIsUploadAssetEnabled] = useState(false);

    const enabled = useIsUploadAssetEnabled();
    
    const { updateBudget } = useBudget({
      jobId: jobDetails?.job_id,
    });

    const { isMobile: mobileDevice } = useScreenSize();

    // Initialize image compression hook
    const { compressImage, getMimeType } = useImageCompression({
      maxWidth: 2000,
      maxHeight: 2000,
      quality: 30, // 10% quality for maximum compression
      format: 'jpeg' // Convert all images to JPEG
    });

    useEffect(() => {
        setIsUploadAssetEnabled( enabled ); 
    }, [jobDetails, useIsUploadAssetEnabled]);

    // Check if user has seen the fork introduction tooltip
    useEffect(() => {
      const hasSeenIntro = hasSeenForkIntro();
      setHasSeenForkIntroLocal(hasSeenIntro);

      const isForkButtonDisabled =
        (agentStatus !== "waiting" && agentStatus !== "subagent_waiting") ||
        buildMode !== "build" ||
        isSubagentActive;

      if (!hasSeenIntro && !isForkButtonDisabled && !mobileDevice) {
        const timer = setTimeout(() => {
          setShowForkTooltip(true);
        }, 1000); // 1 second delay

        return () => clearTimeout(timer);
      }
    }, [ agentStatus, buildMode, isSubagentActive]);

    // Handle dismissing the fork tooltip
    const handleDismissForkTooltip = useCallback(() => {
      setShowForkTooltip(false);
      setHasSeenForkIntroLocal(true);
      markForkIntroAsSeen();
    }, []);

    // Initialize image attachments
    const imageAttachments = useImageAttachments({
      maxImages: 5,
      maxSizeInMB: 5,
      maxPixelDimensions: 8000,
      maxPixelDimensionsMultiple: 2000,
      onImagesChange,
    });

    // Initialize file attachments for asset uploads
    const fileAttachments = useFileAttachments({
      maxFiles: 10,
      maxSizeInMB: 50,
      jobId: jobDetails?.job_id,
      onUploadComplete: (artifactId, fileName) => {
        console.log(`Upload completed: ${fileName} (${artifactId})`);
      },
      onUploadError: (fileName, error) => {
        console.error(`Upload failed for ${fileName}:`, error);
      },
    });

    // Unified file handler that routes to appropriate flow based on isUploadAssetEnabled
    const handleFileUpload = useCallback((files: FileList | File[]) => {
      if (!files || files.length === 0) return;

      const filesArray = Array.from(files);

      // If from subagent panel, always use the basic/legacy flow
      if (fromSubagentPanel || !isUploadAssetEnabled) {
        // Old flow: Separate image files from other files
        const imageFiles = filesArray.filter(
          (file) =>
            file.type.startsWith("image/") &&
            ["image/jpeg", "image/png", "image/gif", "image/webp"].includes(file.type)
        );

        const otherFiles = filesArray.filter(
          (file) => !file.type.startsWith("image/")
        );

        // Handle image files with old image attachment system
        if (imageFiles.length > 0) {
          imageAttachments.handleImageSelect(imageFiles);
        }

        // Handle other files with old file attachment system
        if (otherFiles.length > 0 && jobDetails?.job_id) {
          fileAttachments.handleFileSelect(otherFiles);
        }
      } else {
        // New flow: All files go through upload assets modal with pre-populated files
        onOpenUploadAssetsModal?.(filesArray);
      }
    }, [fromSubagentPanel, isUploadAssetEnabled, onOpenUploadAssetsModal, imageAttachments, fileAttachments, jobDetails?.job_id]);

    // Handle file input change for mobile compatibility
    const handleFileInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
          if (fromSubagentPanel || !isUploadAssetEnabled) {
            // Use the old flow for subagent panel or when upload asset is disabled
            handleFileUpload(files);
          } else {
            // When upload asset is enabled, always open the modal with selected files
            const filesArray = Array.from(files);
            onOpenUploadAssetsModal?.(filesArray);
          }
        }
        // Reset the input value to allow selecting the same file again
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      },
      [fromSubagentPanel, handleFileUpload, isUploadAssetEnabled, onOpenUploadAssetsModal]
    );

    // Handle attachment button click to toggle dropdown
    const handleAttachmentClick = useCallback(() => {
      if (isInputDisabled) return;

      if(fromSubagentPanel || !isUploadAssetEnabled) {
        handleImageAttachmentClick();
      } else {
        setShowAttachDropdown(!showAttachDropdown);
        return;
      }

    }, [fromSubagentPanel, showAttachDropdown, isUploadAssetEnabled]);

    // Handle image attachment click
    const handleImageAttachmentClick = useCallback(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      } else {
        // Fallback to the original method if ref is not available
        imageAttachments.openFilePicker();
      }
      setShowAttachDropdown(false);
    }, [imageAttachments]);

    // Handle upload asset button click
    const handleUploadAssetClick = useCallback(() => {
      // Open file selection dialog first, then open modal with selected files
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
      setShowAttachDropdown(false);
    }, []);



    useEffect(() => {
      if (inputValue) {
        setLocalValue(inputValue);
      }
    }, [inputValue]);

    // Sync localValue with persisted chat input text when tab changes (only for main chat input)
    useEffect(() => {
      if (!fromSubagentPanel) {
        setLocalValue(persistedChatInputText);
      }
    }, [persistedChatInputText, activeTabId, fromSubagentPanel]);

    // Persist chat input text to Redux when localValue changes (only for main chat input)
    useEffect(() => {
      if (fromSubagentPanel) return; // Don't persist subagent panel input to Redux

      const timeoutId = setTimeout(() => {
        dispatch(setChatInputText({
          tabId: activeTabId,
          text: localValue
        }));
      }, 300); // Debounce to avoid too many updates

      return () => clearTimeout(timeoutId);
    }, [localValue, activeTabId, dispatch, fromSubagentPanel]);

    // Handle click outside to close dropdown
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setShowAttachDropdown(false);
        }
      };

      if (showAttachDropdown) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showAttachDropdown]);

    const handlePauseAgent = useCallback(async () => {
      if (!jobDetails?.job_id || !agentState?.agent_running) {
        return;
      }

      // Call the parent's pause handler - status will be updated via useEffect
      if (onPause) {
        onPause({ origin: fromSubagentPanel ? "SubagentButton" : "MainInput" });
      }
    }, [jobDetails?.job_id, agentState?.agent_running, onPause]);

    const handleResumeAgent = useCallback(async () => {
      if (!jobDetails?.job_id || !localValue.trim()) return;

      // Check if admin user wants to send as HIM request or if it's automatically a HIM request
      const shouldSendAsHim = isEmergentUser && sendAsHimRequest && user?.id !== jobDetails.createdBy;

      if (shouldSendAsHim) {
        // console.log("HIM Message : xoxo", jobDetails.job_id, localValue.trim(), user?.id, jobDetails.createdBy);
        await agentApi.handleHimMessage(jobDetails.job_id, localValue.trim());
        setLocalValue("");

        // Clear pending artifacts for HIM messages as well (only for main chat input)
        if (!fromSubagentPanel && pendingArtifacts.length > 0) {
          dispatch(clearPendingArtifacts({ tabId: activeTabId }));
        }

        return;
      }

      // Get regular image attachments
      const regularImages =
        imageAttachments.images.length > 0
          ? imageAttachments.images.map((img) => ({
              mime_type: img.mime_type,
              img_base64: img.img_base64,
            }))
          : [];

      // Get image artifacts from pending artifacts and convert to base64 (skip for subagent panel)
      let imageArtifacts: ({mime_type: string, img_base64: string} | null)[] = [];

      if (!fromSubagentPanel) {
        try {
          imageArtifacts = await Promise.all(
            pendingArtifacts
              .filter(artifact => {
                // Only process artifacts that are marked to be sent to agent
                if (!artifact.readByAgent) return false;

                // Allow JPEG, PNG, GIF, and WebP images for base64 conversion
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                const isAllowedType = artifact.mime_type && allowedTypes.includes(artifact.mime_type);
                const isAllowedExtension = /\.(jpg|jpeg|png|gif|webp)$/i.test(artifact.file_name);
                return isAllowedType || isAllowedExtension;
              })
            .map(async (artifact) => {
              try {
                // Use local_preview_url if available, otherwise file_path
                const imageUrl = artifact.local_preview_url || artifact.file_path;
                if (!imageUrl) return null;

                // Fetch the original image
                const response = await fetch(imageUrl);
                if (!response.ok) return null;

                const originalBlob = await response.blob();

                // Compress the image before converting to base64
                const compressionResult = await compressImage(originalBlob);
                if (!compressionResult) {
                  console.error('Failed to compress image:', artifact.file_name);
                  return null;
                }

                console.log(`Compressed ${artifact.file_name}:`, {
                  originalSize: `${(compressionResult.originalSize / 1024 / 1024).toFixed(2)}MB`,
                  compressedSize: `${(compressionResult.compressedSize / 1024 / 1024).toFixed(2)}MB`,
                  compressionRatio: `${compressionResult.compressionRatio.toFixed(1)}%`
                });

                return {
                  mime_type: getMimeType('jpeg'), // Always JPEG after compression
                  img_base64: compressionResult.base64,
                };
              } catch (error) {
                console.error('Failed to process artifact image:', artifact.file_name, error);
                return null;
              }
            })
          );
        } catch (error) {
          console.error('Failed to process image artifacts:', error);
          imageArtifacts = [];
        }
      }

      // Filter out null values and combine all images
      const validImageArtifacts = imageArtifacts.filter(img => img !== null) as {mime_type: string, img_base64: string}[];
      const allImages = [...regularImages, ...validImageArtifacts];

      console.log('ChatInput - Image processing summary:', {
        regularImages: regularImages.length,
        imageArtifacts: validImageArtifacts.length,
        totalImages: allImages.length,
        pendingArtifacts: pendingArtifacts.length,
        artifactsToSendToAgent: pendingArtifacts.filter(a => a.readByAgent).length,
        compression: 'All images compressed to 2000x2000px, 10% quality, JPEG format',
        supportedTypes: 'Only artifacts with readByAgent=true are converted to base64'
      });

      const content = localValue.trim();
      const imagesContent = allImages;

      // For pending message display, we want to show local cached images immediately
      // So we pass ALL pending artifacts (not just those marked for agent) to preserve local_preview_url
      const artifactsForDisplay = fromSubagentPanel ? [] : pendingArtifacts;

      console.log('ChatInput - Sending message with artifacts:', {
        content: content.substring(0, 50) + '...',
        allImages: allImages.length,
        artifactsForDisplay: artifactsForDisplay.length,
        artifactData: artifactsForDisplay.map(a => ({
          file_name: a.file_name,
          local_preview_url: a.local_preview_url,
          local_preview_base64: a.local_preview_base64 ? 'present' : 'none',
          file_path: a.file_path,
          mime_type: a.mime_type,
          readByAgent: a.readByAgent
        }))
      });

      // Immediately send the message to the chat (including all pending artifacts for high-quality display)
      onSubmit(content, allImages, artifactsForDisplay);
      setLocalValue(""); // Clear input after sending

      // Clear persisted chat input text (only for main chat input)
      if (!fromSubagentPanel) {
        dispatch(clearChatInputText({ tabId: activeTabId }));
      }

      // Clear images immediately when user hits enter
      if (imageAttachments.images.length > 0) {
        imageAttachments.clearImages();
      }

      // Clear pending artifacts immediately when user submits (only for main chat input)
      if (!fromSubagentPanel && pendingArtifacts.length > 0) {
        dispatch(clearPendingArtifacts({ tabId: activeTabId }));
      }

      try {
        console.log("Budget Info : xoxo", acc_cost, credits);
        if (acc_cost && max_budget && acc_cost <= credits && isAutoBudgetUpdateEnabled()) {
          const nextBudget = calculateNextBudget(max_budget, acc_cost, credits);
          console.log("Next Budget : xoxo", nextBudget, acc_cost, credits);
          await updateBudget(nextBudget, false);
        }
      } catch (error) {
        console.error("Failed to increase budget:", error);
      }

      try {
        // For cloud flow, use submitHITL with resume true
        const payload = {
          processor_type: "env_only",
          is_cloud: true,
          task: content,
          prompt_name: "",
          prompt_version: "latest",
          work_space_dir: "",
          model_name: modelName || "claude-3-5-sonnet-20241022",
          env_image: "",
          base64_image_list: imagesContent,
          human_timestamp: new Date().toISOString(),
        };

        // Prepare finalize_artifact_upload if there are pending artifacts (skip for subagent panel)
        let finalizeArtifactUpload = undefined;
        if (!fromSubagentPanel && pendingArtifacts.length > 0) {
          finalizeArtifactUpload = {
            artifacts: pendingArtifacts.map(artifact => ({
              artifact_id: artifact.artifact_id,
              entity_id: artifact.entity_id,
              entity_type: artifact.entity_type,
              visibility: artifact.visibility,
              file_name: artifact.file_name,
              description: artifact.description,
              file_path: artifact.file_path
            }))
          };
        }

        await submitHITL({
          payload,
          client_ref_id: jobDetails?.job_id!,
          resume: true,
          finalize_artifact_upload: finalizeArtifactUpload
        }).unwrap();
        setAgentStatus("running");
      } catch (error) {
        console.error("Failed to resume agent:", error);
        removePendingMessage(getActiveTab().id, content);
        setLocalValue(content);
        toast({
          title: "Error",
          description: "Failed to resume agent",
          variant: "destructive",
        });
      }
    }, [
      jobDetails?.job_id,
      containerId,
      localValue,
      isCloudFlow,
      toast,
      modelName,
      imageAttachments,
      isEmergentUser,
      sendAsHimRequest,
      user?.id,
    ]);

    // This function is no longer needed as we're handling GitHub push directly in the button click handler

    // Update agent status based on external state
    useEffect(() => {
      const newStatus = calculateAgentStatus({
        agentState,
        isSubagentActive,
        currentAgentStatus: agentStatus,
        isPauseLoading,
        forked_status,
      });

      if (newStatus !== agentStatus) {
        setAgentStatus(newStatus);
      }
    }, [agentState, isSubagentActive, agentStatus, isPauseLoading, forked_status]);

    // Memoize UI elements with dynamic colors
    const colors = useMemo(() => {
      const getDynamicColor = (status: string) => {
        const dynamicColor = getDynamicStatusColor(
          status,
          subagentName,
          isSubagentActive
        );
        return {
          color: dynamicColor,
        };
      };

      const colorValues = {
        running: getDynamicColor("running"),
        waiting: getDynamicColor("waiting"),
        exit_cost: getDynamicColor("exit_cost"),
        stopped: getDynamicColor("stopped"),
        stopping: getDynamicColor("stopping"),
        subagent_running: getDynamicColor("subagent_running"),
        subagent_waiting: getDynamicColor("subagent_waiting"),
        subagent_stopping: getDynamicColor("subagent_stopping"),
        forking: getDynamicColor("forking"),
      };

      return colorValues;
    }, [subagentName, isSubagentActive]);

    const statusText = useMemo(() => {
      const getAgentDisplayName = () => agentName || "Agent";
      const getSubagentDisplayName = () =>
        subagentName ? getSubagentName(subagentName) : "Subagent";

      return {
        exit_cost: `${getAgentDisplayName()} is offline`,
        running: `${getAgentDisplayName()} is running...`,
        waiting: `${getAgentDisplayName()} is waiting...`,
        stopped: `${getAgentDisplayName()} is offline`,
        stopping: `${getAgentDisplayName()} is stopping gracefully. Usually takes a min`,
        subagent_running: `${getSubagentDisplayName()} is running...`,
        subagent_waiting: `${getSubagentDisplayName()} is waiting...`,
        subagent_stopping: `${getSubagentDisplayName()} is gracefully stopping...`,
        forking: "Forking is in progress...",
      };
    }, [agentName, subagentName]);

    // Get dynamic background gradient based on agent status and subagent
    const getBackgroundGradient = (direction: string = "bottom", baseOpacity: string = '0%', endOpacity: number = 0.2) => {
      const defaultGradients = {
        running:
          `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.02) 0%, rgba(103, 203, 101, 0.2) 100%)`,
        waiting:
          `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.02) 0%, rgba(95, 211, 243, 0.2) 100%)`,
        stopped:
          `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.02) 0%, rgba(123, 123, 128, 0.2) 100%)`,
        stopping:
          `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.02) 0%, rgba(244, 155, 87, 0.2) 100%)`,
        exit_cost:
          `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.02) 0%, rgba(123, 123, 128, 0.2) 100%)`,
      };

      if (fromSubagentPanel) {
        return (
          defaultGradients[agentStatus as keyof typeof defaultGradients] ||
          defaultGradients.stopped
        );
      }

      // If subagent is active and we have a subagent name, use subagent gradient
      if (isSubagentActive && subagentName) {
        const statusForGradient = agentStatus.startsWith("subagent_")
          ? (agentStatus.replace("subagent_", "") as
              | "running"
              | "waiting"
              | "stopping")
          : "waiting";
        return getSubagentGradient(subagentName, statusForGradient, direction);
      }

      // For subagent statuses without specific subagent, use default purple gradient
      if (agentStatus.startsWith("subagent_")) {
        return `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.2) 0%, rgba(221, 153, 255, 1) 100%)`;
      }

      const value =
        defaultGradients[agentStatus as keyof typeof defaultGradients] ||
        defaultGradients.stopped;

      if (
        agentStatus == "waiting" &&
        acc_cost !== undefined &&
        max_budget !== undefined &&
        acc_cost > max_budget
      ) {
        return `linear-gradient(to ${direction}, rgba(255, 255, 255, 0.2) 0.02, rgba(255, 255, 255, 0.1) 100%)`;
      }

      return value;
    };

    const buttonIcon = useMemo(() => {
      // Show loader when pause is in progress
      if (isPauseLoading) {
        return <Loader2 className="w-6 h-6 text-gray-500 animate-spin" />;
      }

      switch (agentStatus) {
        case "running":
        case "subagent_running":
        case "stopping":
        case "subagent_stopping":
          return <img src={PauseIcon} className="w-6 h-6" alt="pause" />;
        default:
          return <img src={SendIcon} className="w-6 h-6" alt="send" />;
      }
    }, [agentStatus, isPauseLoading]);

    // Check if input should be disabled
    const isInputDisabled = useMemo(() => {
      if (isEmergentUser && localStorage.getItem("mobile") !== "true") return false;

      if (forked_status == "running") {
        return true;
      }

      if (buildMode == "brainstorming_done" && agentStatus === "running") {
        return true;
      }

      // Disable when subagent is active and disableWhenSubagentActive is true
      if (
        (isSubagentActive || agentStatus === "subagent_waiting") &&
        !fromSubagentPanel
      ) {
        return true;
      }

      if (getPendingMessage(getActiveTab().id)?.content) {
        return true;
      }

      // Check if budget limit is reached
      if (
        acc_cost !== undefined &&
        max_budget !== undefined &&
        acc_cost > max_budget
      ) {
        return true;
      }

      if (credits < 0) {
        return true;
      }

      // Otherwise, use the standard disabled logic
      return isDisabled;
    }, [
      isDisabled,
      disableWhenSubagentActive,
      isSubagentActive,
      getActiveTab,
      getPendingMessage,
      acc_cost,
      max_budget,
      isEmergentUser,
      buildMode,
      agentStatus,
    ]);

    const adjustHeight = useCallback(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = "auto";
        textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
      }
    }, []);

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setLocalValue(e.target.value);
        // Call adjustHeight after value change
        requestAnimationFrame(adjustHeight);
      },
      [adjustHeight]
    );

    // Initial height adjustment when component mounts
    useEffect(() => {
      adjustHeight();
    }, [adjustHeight]);

    // Adjust height whenever localValue changes
    useEffect(() => {
      adjustHeight();
    }, [localValue, adjustHeight]);

    const handleSubmitForm = useCallback(
      (e: React.FormEvent) => {
        e.preventDefault();

        if (isInputDisabled) return;

        // Check if admin user wants to send as HIM request or if it's automatically a HIM request
        const shouldSendAsHim = isEmergentUser && sendAsHimRequest && user?.id !== jobDetails?.createdBy;
        
        if (shouldSendAsHim) {
          handleResumeAgent();
          return;
        }

        if (
          agentStatus === "running" ||
          agentStatus === "subagent_running" ||
          agentStatus === "stopping" ||
          agentStatus === "subagent_stopping"
        ) {
          handlePauseAgent();
          //console.log("paused");
        } else if (
          (agentStatus === "waiting" && !fromSubagentPanel) ||
          (agentStatus === "subagent_waiting" && fromSubagentPanel)
        ) {
          if (!localValue.trim()) return;
          handleResumeAgent();
          setLocalValue("");
        }
      },
      [
        localValue,
        isDisabled,
        imageAttachments,
        agentStatus,
        handlePauseAgent,
        handleResumeAgent,
        jobDetails,
        isInputDisabled,
        isEmergentUser,
        sendAsHimRequest,
        user?.id,
      ]
    );

    const disabledSubmit = useMemo(() => {
      let buttonState = false;
      if (
        isPauseLoading ||
        agentStatus === "subagent_waiting" ||
        (agentStatus === "running" && buildMode === "brainstorming_done") ||
        pauseWasClicked
      ) {
        buttonState = true;
      }

      if (agentStatus === "subagent_waiting" && fromSubagentPanel) {
        buttonState = false;
      }
      if(forked_status == "running"){
        buttonState = true;
      }

      return buttonState;
    }, [
      isPauseLoading,
      agentStatus,
      showFinishSubagentText,
      pauseWasClicked,
      buildMode,
      fromSubagentPanel,
      forked_status,
    ]);

    const getPlaceHolderText = useCallback(() => {
      if (
        acc_cost !== undefined &&
        max_budget !== undefined &&
        acc_cost > max_budget
      ) {
        return "Your budget limit has been reached. Please add more credits to resume your agent's task...";
      }
      if (forked_status == "running") {
        return "Please wait for forking to finish";
      }
      return placeholder;
    }, [placeholder, acc_cost, max_budget, forked_status]);

    const { ForkDisabled, ForkDisabledReason } = useMemo(() => {
      // Check conditions in order of priority
      if (agentStatus !== "waiting" && agentStatus !== "subagent_waiting") {
        return {
          ForkDisabled: true,
          ForkDisabledReason: "Please wait for the agent to finish before forking"
        };
      }

      if (buildMode !== "build") {
        return {
          ForkDisabled: true,
          ForkDisabledReason: "Please wait for agent to write code before forking"
        };
      }

      if (isSubagentActive) {
        return {
          ForkDisabled: true,
          ForkDisabledReason: "Cannot fork while subagent is active"
        };
      }

      if (credits <= 0) {
        return {
          ForkDisabled: true,
          ForkDisabledReason: "Insufficient credits to fork"
        };
      }

      return {
        ForkDisabled: false,
        ForkDisabledReason: null
      };
    }, [
      agentStatus,
      buildMode,
      isSubagentActive,
      credits
    ]);

    return (
      <AnimatePresence>
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className={cn(
            "flex flex-col md:p-1 p-0 max-w-full sm:max-w-4xl md:pt-1 pt-0 w-full md:rounded-[14px] rounded-b-l-none rounded-b-r-none md:backdrop-blur-[40px] xs:backdrop-blur-[0px] shadow-sm bg-[#1A1A1C] rounded-t-l-[14px] rounded-t-r-[14px]"
          )}
          style={{
            background: !mobileDevice
              ? getBackgroundGradient()
              : "bg-[#1A1A1C]",
          }}
        >
          <div className="flex items-center justify-between w-full px-2 md:px-3 md:py-2">
            <div
              className={cn(
                "flex items-center gap-2 ",
                (!fromSubagentPanel && pendingArtifacts.length > 0) ? "w-full" : "max-w-[50%]"
              )}
            >
              {/* Show selected images if any, otherwise show agent status */}
              {imageAttachments.images.length > 0 && (
                <div className="flex items-center gap-2 pt-1">
                  <div className="flex flex-wrap gap-2">
                    {imageAttachments.isProcessing ? (
                      <div className="flex items-center justify-center py-1">
                        <Loader2 className="h-4 w-4 text-[#999] animate-spin mr-2" />
                        <span className="text-[#999] text-xs">
                          Processing...
                        </span>
                      </div>
                    ) : (
                      imageAttachments.images.map((image, index) => (
                        <div
                          key={index}
                          className="relative w-16 h-16 md:w-20 md:h-20 rounded-md border border-[#333] group"
                        >
                          <img
                            src={`data:${image.mime_type};base64,${image.img_base64}`}
                            alt={`Selected image ${index + 1}`}
                            className="w-full h-full object-cover z-[1] rounded-md"
                          />
                          <button
                            onClick={() => imageAttachments.removeImage(index)}
                            className="absolute top-[-6px] right-[-6px] bg-transparent z-[2] rounded-full p-1 transition-opacity"
                            aria-label="Remove image"
                            type="button"
                          >
                            <div className="bg-white rounded-full p-[2px]">
                              <img
                                src={AttachmentCross}
                                alt="Remove image"
                                className="w-2 h-2"
                              />
                            </div>
                          </button>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}

              {/* Show selected files for upload if any */}
              {fileAttachments.hasFiles && (
                <div className="flex items-center gap-2 pt-1">
                  <div className="flex flex-wrap gap-2">
                    {fileAttachments.isProcessing ? (
                      <div className="flex items-center justify-center py-1">
                        <Loader2 className="h-4 w-4 text-[#999] animate-spin mr-2" />
                        <span className="text-[#999] text-xs">
                          Uploading...
                        </span>
                      </div>
                    ) : (
                      fileAttachments.files.map((file, index) => (
                        <div
                          key={index}
                          className="relative w-16 h-16 overflow-hidden rounded-lg md:w-20 md:h-20 group backdrop-blur-md"
                          style={{
                            background:
                              "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",
                            backdropFilter: "blur(10px)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                          }}
                        >
                          {/* File preview with dark theme */}
                          <div className="flex flex-col items-center justify-center w-full h-full p-2">
                            {/* File icon */}
                            <div className="w-8 h-8 mb-1 flex items-center justify-center rounded bg-[#FFFFFF20]">
                              <img
                                src={UploadIcon}
                                alt="File"
                                className="w-5 h-5 invert opacity-70"
                              />
                            </div>
                            {/* File name */}
                            <div className="text-[8px] text-white/90 text-center leading-tight truncate max-w-full">
                              {file.name.length > 8
                                ? `${file.name.substring(0, 8)}...`
                                : file.name}
                            </div>
                            {/* File size */}
                            <div className="text-[6px] text-white/50 text-center mt-1">
                              {(file.size / 1024 / 1024).toFixed(1)}MB
                            </div>
                          </div>

                          <button
                            onClick={() => fileAttachments.removeFile(index)}
                            className="absolute top-[-6px] right-[-6px] bg-transparent z-[2] rounded-full p-1 transition-opacity"
                            aria-label="Remove file"
                            type="button"
                          >
                            <div className="bg-white rounded-full p-[2px]">
                              <img
                                src={AttachmentCross}
                                alt="Remove file"
                                className="w-2 h-2"
                              />
                            </div>
                          </button>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}

              {/* Show pending artifacts (only for main chat input, not subagent panel) */}
              {!fromSubagentPanel && pendingArtifacts.length > 0 && (
                <div className="w-full pt-1">
                  <div className="flex max-w-full gap-2 pb-1 overflow-x-auto overflow-y-hidden flex-nowrap scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent hover:scrollbar-thumb-white/30">
                    {pendingArtifacts.map((artifact) => (
                      <div key={artifact.artifact_id} className="flex-shrink-0">
                        <ChatPreview artifact={artifact} />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {imageAttachments.images.length === 0 &&
                !fileAttachments.hasFiles &&
                (fromSubagentPanel || pendingArtifacts.length === 0) &&
                !hideStatus &&
                !mobileDevice && (
                  <div className="flex items-center gap-2 pt-1 md:gap-4">
                    <div className="flex items-center gap-2 min-w-[58vw] md:min-w-fit md:gap-4">
                      <StatusIndicator
                        status={
                          acc_cost !== undefined &&
                          max_budget !== undefined &&
                          acc_cost < max_budget
                            ? agentStatus
                            : "exit_cost"
                        }
                        colors={colors}
                      />
                      <StatusText
                        status={
                          acc_cost !== undefined &&
                          max_budget !== undefined &&
                          acc_cost < max_budget
                            ? agentStatus
                            : "exit_cost"
                        }
                        colors={colors}
                        statusText={statusText}
                      />
                    </div>

                    {agentStatus === "subagent_waiting" &&
                      onOpenSubagentPanel && (
                        <button
                          onClick={onOpenSubagentPanel}
                          className={`ml-2 px-2 py-1 text-xs font-semibold rounded-md transition-colors`}
                          style={{
                            backgroundColor: `${colors.subagent_waiting.color}`,
                            color: getSubagentStyle(subagentName || "").color,
                          }}
                          type="button"
                        >
                          Reply
                        </button>
                      )}
                  </div>
                )}
            </div>
            {!hideTokens &&
              imageAttachments.images.length === 0 &&
              !mobileDevice &&
              !isAutoBudgetUpdateEnabled() && (
                <TokenDisplay
                  acc_cost={acc_cost}
                  max_budget={max_budget}
                  onAddToken={onAddToken}
                />
              )}
          </div>
          <form
            onSubmit={handleSubmitForm}
            className={cn(
              "flex flex-1 flex-col md:bg-background bg-[#1A1A1C] rounded-none rounded-tl-[14px] rounded-tr-[14px] border border-1 relative md:rounded-xl",
              isDraggingOver
                ? "border-[#5FD3F3]/50 border-2 border-dashed"
                : "border-[#252526]"
            )}
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDragEnter={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDraggingOver(true);
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Only set to false if we're leaving the form element (not entering a child)
              if (e.currentTarget.contains(e.relatedTarget as Node)) {
                return;
              }
              setIsDraggingOver(false);
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDraggingOver(false);

              // Process dropped files using unified handler
              const files = e.dataTransfer.files;
              if (files.length > 0) {
                handleFileUpload(files);
              }
            }}
          >
            {isDraggingOver && (
              <div className="absolute inset-0 z-10 flex items-center justify-center bg-background/80 rounded-xl">
                <div className="text-[#5FD3F3] text-center">
                  <p className="font-medium">Drop files here</p>
                </div>
              </div>
            )}
            <textarea
              ref={textareaRef}
              value={localValue}
              onChange={handleChange}
              onPaste={(e) => {
                const items = e.clipboardData?.items;
                if (!items) return;

                // Get all file items (not just images when isUploadAssetEnabled is true)
                const fileItems = Array.from(items).filter((item) => {
                  if (fromSubagentPanel || !isUploadAssetEnabled) {
                    // Only accept images for subagent panel or old flow
                    return item.type.startsWith("image/");
                  } else {
                    // Accept all file types when upload assets is enabled
                    return item.kind === "file";
                  }
                });

                if (fileItems.length === 0) return;

                e.preventDefault();

                const files = fileItems
                  .map((item) => item.getAsFile())
                  .filter(Boolean) as File[];
                if (files.length > 0) {
                  handleFileUpload(files);
                }
              }}
              className={cn(
                `w-full resize-none md:bg-background bg-[#1A1A1C] outline-none text-[16px]  px-4 py-3 min-h-[64px] overflow-y-auto placeholder:text-white/50 ${
                  isInputDisabled ? "cursor-not-allowed opacity-50" : ""
                }`,
                fromSubagentPanel ? "max-h-[120px]" : "max-h-[200px]",
                mobileDevice
                  ? "rounded-none rounded-tl-[14px] rounded-tr-[14px]"
                  : "rounded-xl"
              )}
              style={{ lineHeight: "1.5" }}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmitForm(e);
                }
              }}
              placeholder={getPlaceHolderText()}
              rows={2}
              disabled={isInputDisabled}
            />
            <div
              className={cn(
                "flex items-center justify-between px-3 py-3 md:bg-background bg-[#1A1A1C]",
                mobileDevice ? "rounded-none" : "rounded-xl"
              )}
            >
              <div
                className="relative flex items-center gap-2"
                ref={dropdownRef}
              >
                <button
                  type="button"
                  onClick={() => {
                    (fromSubagentPanel || !isUploadAssetEnabled)
                      ? handleAttachmentClick()
                      : handleUploadAssetClick();
                  }}
                  className={cn(
                    "p-2 transition-colors duration-200 rounded-[30px] bg-[#FFFFFF14] hover:bg-gray-100/10 group/paperclip",
                    {
                      "opacity-50 cursor-not-allowed": isInputDisabled,
                    }
                  )}
                  disabled={isInputDisabled}
                >
                  <img
                    src={showAttachDropdown ? CloseIcon : AttachIcon}
                    alt="Attach"
                    className={cn(
                      "w-6 h-6 transition-transform duration-200 transform ",
                      !showAttachDropdown && "group-hover/paperclip:rotate-45"
                    )}
                  />
                </button>

                {/* Hidden file input for mobile compatibility */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={
                    (fromSubagentPanel || !isUploadAssetEnabled)
                      ? "image/jpeg,image/png,image/gif,image/webp"
                      : "*/*"
                  }
                  multiple
                  onChange={handleFileInputChange}
                  style={{ display: "none" }}
                  aria-hidden="true"
                />

                {/* GitHub Push Button - Only visible when subagent is not active */}
                <button
                  disabled={
                    (agentStatus !== "waiting" &&
                      agentStatus !== "subagent_waiting") ||
                    buildMode != "build" ||
                    isSubagentActive
                  }
                  type="button"
                  onClick={() => {
                    // Directly open the GitHub push modal
                    setIsGitHubPushModalOpen(true);
                  }}
                  className={cn(
                    "px-3 pr-4 h-[40px] py-2 transition-colors duration-200 rounded-full flex items-center gap-2 bg-[#FFFFFF14] hover:bg-gray-100/10",
                    (agentStatus !== "waiting" &&
                      agentStatus !== "subagent_waiting") ||
                      buildMode != "build" ||
                      isSubagentActive
                      ? "opacity-50 cursor-not-allowed"
                      : "",
                    fromSubagentPanel ? "hidden" : ""
                  )}
                >
                  <img
                    src={isGitHubConnected ? GithubDot : WhiteGithubIcon}
                    alt="GitHub"
                    className={cn(
                      "w-4 h-4",
                      isGitHubConnected ? "scale-[1.5] mt-[2px]" : ""
                    )}
                  />
                  <span className="max-md:hidden block text-sm font-medium text-[#E6E6E6]">
                    Save to GitHub
                  </span>
                  <span className="md:hidden visible text-sm font-medium text-[#E6E6E6]">
                    GitHub
                  </span>
                </button>

                {/* Admin HIM Button */}
                {isEmergentUser &&
                  jobDetails?.createdBy &&
                  user?.id !== jobDetails.createdBy && (
                    <button
                      type="button"
                      disabled={buildMode != "build"}
                      onClick={() => setSendAsHimRequest(!sendAsHimRequest)}
                      className={cn(
                        "px-3 h-[40px] py-2 transition-colors duration-200 rounded-full flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",
                        fromSubagentPanel ? "hidden" : "",
                        sendAsHimRequest
                          ? "bg-blue-500/20 border border-blue-500/50 hover:bg-blue-500/30"
                          : "bg-[#FFFFFF14] hover:bg-gray-100/10"
                      )}
                    >
                      <span
                        className={cn(
                          "text-[14px] font-medium",
                          sendAsHimRequest ? "text-blue-400" : "text-[#E6E6E6]"
                        )}
                      >
                        HIM
                      </span>
                    </button>
                  )}
                <TooltipProvider>
                  <Tooltip
                    open={showForkTooltip || undefined}
                    delayDuration={hasSeenForkIntroLocal ? 0 : 9999999}
                  >
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "relative transition-all duration-200",
                          showForkTooltip
                            ? "before:absolute before:inset-[-4px] before:border before:border-white/20 before:rounded-full before:shadow-lg before:pointer-events-none"
                            : ""
                        )}
                      >
                        <button
                          type="button"
                          disabled={ForkDisabled}
                          onClick={() => {
                            if (forked_status === "running") return;
                            // Dismiss tooltip when button is clicked
                            if (showForkTooltip) {
                              handleDismissForkTooltip();
                            }
                            modalOpen &&
                              setModalOpen &&
                              setModalOpen({
                                ...modalOpen,
                                fork: !modalOpen.fork,
                              });
                          }}
                          className={cn(
                            `px-3 h-[40px] py-2 transition-colors duration-200 rounded-full flex items-center gap-[6px] bg-[#FFFFFF14] hover:bg-gray-100/10 disabled:opacity-50 disabled:cursor-not-allowed`,
                            fromSubagentPanel ? "hidden" : "",
                            forked_status === "running"
                              ? "bg-[#80FFF90D] hover:bg-[#80FFF90D] disabled:opacity-100 cursor-not-allowed"
                              : ""
                          )}
                        >
                          <img
                            src={
                              forked_status == "running"
                                ? animatedSpinner
                                : ForkSVG
                            }
                            alt="Fork"
                            className="w-5 h-5"
                          />
                          <span
                            className={cn(
                              "text-[14px] font-medium text-[#E6E6E6]",
                              forked_status == "running" ? "text-[#80FFF9]" : ""
                            )}
                          >
                            {forked_status === "running"
                              ? "Forking..."
                              : "Fork"}
                          </span>
                        </button>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      side="top"
                      align="center"
                      className={cn(
                        hasSeenForkIntroLocal
                          ? "bg-[#DDDDE6] font-['Inter'] text-black border-0"
                          : "relative p-0 bg-none border-none pb-[30px] -bottom-[8px] bg-transparent"
                      )}
                    >
                      {hasSeenForkIntroLocal ? (
                        <>
                          <span className="text-sm font-['Inter'] font-medium">
                            {ForkDisabled && ForkDisabledReason
                              ? ForkDisabledReason
                              : "Fork into a new chat"}
                          </span>
                          <TooltipArrow className="fill-[#DDDDE6]" />
                        </>
                      ) : (
                        // Introduction tooltip
                        <motion.div
                          initial={{ opacity: 0, y: 6 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 6 }}
                          transition={{ duration: 0.3, ease: "easeOut" }}
                          className="max-w-[260px] p-3 bg-[#FFFFFF] border-[#2E2F34] text-white shadow-2xl rounded-[12px]"
                          style={{ backdropFilter: "blur(20px)" }}
                        >
                          <div className="flex flex-col gap-4">
                            <div className="flex flex-col gap-1">
                              <div className="flex items-center justify-between">
                                <span className="text-[16px] font-['Inter'] font-semibold text-[#0F0F10]">
                                  Introducing Forks
                                </span>
                              </div>
                              <p className="text-[12px] font-medium font-['Inter'] text-[#0F0F1080] leading-[16px]">
                                Continue in a new chat with key context
                                preserved. Perfect for branching tasks.
                              </p>
                            </div>
                            <div className="flex items-center justify-end w-full gap-2">
                              <button
                                onClick={() => {
                                  window.open(
                                    URL_LINKS.forking.learnMore,
                                    "_blank"
                                  );
                                  handleDismissForkTooltip();
                                }}
                                className="text-[12px] px-2 py-[4px] font-['Inter'] font-medium rounded-[5px] bg-[#0B0B0B24] hover:bg-[#0B0B0B34] text-[#00000080] transition-colors"
                                type="button"
                              >
                                Learn More
                              </button>
                              <button
                                onClick={handleDismissForkTooltip}
                                className="px-2 py-[4px] bg-[#0B0B0B] hover:bg-[#333333]  font-['Inter'] rounded-[5px] text-[#FFFFFF] text-[12px] font-medium transition-colors"
                                type="button"
                              >
                                Okay, got it
                              </button>
                            </div>
                          </div>
                          <div className="absolute z-[999] flex flex-col items-center justify-center transform -translate-x-1/2 -bottom-[25px] left-1/2">
                            <div className="bg-white min-h-5 min-w-[1px] mb-[2px]"></div>
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                        </motion.div>
                      )}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <button
                onClick={handleSubmitForm}
                className={`flex ${
                  agentStatus === "subagent_running" && showFinishSubagentText
                    ? "w-auto px-3"
                    : "w-[48px] px-[12px]"
                } py-[6px] justify-center items-center gap-[10px] rounded-[38px] bg-[#DDDDE6] disabled:opacity-50`}
                type="submit"
                disabled={disabledSubmit}
              >
                {agentStatus === "subagent_running" &&
                  showFinishSubagentText && (
                    <span className="text-sm font-medium text-black">
                      Finish Sub-Agent
                    </span>
                  )}
                {buttonIcon}
              </button>
            </div>
          </form>
          <div
            className="flex flex-row justify-between items-center w-full md:hidden p-[10px]"
            style={{ background: getBackgroundGradient("right", "90%") }}
          >
            {!hideStatus && (
              <div className="flex items-center gap-2 md:gap-4">
                <div className="flex items-center gap-2 min-w-[58vw] md:min-w-fit md:gap-4">
                  <StatusIndicator
                    status={
                      acc_cost !== undefined &&
                      max_budget !== undefined &&
                      acc_cost < max_budget
                        ? agentStatus
                        : "exit_cost"
                    }
                    colors={colors}
                  />
                  <StatusText
                    status={
                      acc_cost !== undefined &&
                      max_budget !== undefined &&
                      acc_cost < max_budget
                        ? agentStatus
                        : "exit_cost"
                    }
                    colors={colors}
                    statusText={statusText}
                  />
                </div>

                {agentStatus === "subagent_waiting" && onOpenSubagentPanel && (
                  <button
                    onClick={onOpenSubagentPanel}
                    className={`ml-2 px-2 py-1 text-xs font-semibold rounded-md transition-colors`}
                    style={{
                      backgroundColor: `${colors.subagent_waiting.color}`,
                      color: getSubagentStyle(subagentName || "").color,
                    }}
                    type="button"
                  >
                    Reply
                  </button>
                )}
              </div>
            )}
            {!hideTokens && !isAutoBudgetUpdateEnabled() && (
              <TokenDisplay
                acc_cost={acc_cost}
                max_budget={max_budget}
                onAddToken={onAddToken}
              />
            )}
          </div>

          {/* GitHub Push Modal */}
          {isGitHubPushModalOpen && (
            <GitHubPushModal
              isOpen={isGitHubPushModalOpen}
              onOpenChange={(open) => setIsGitHubPushModalOpen(open)}
              jobId={jobDetails?.job_id || ""}
              onSuccess={(repoDetails) => {
                // Pass the repository details to the parent component
                if (repoDetails && onGitHubPush) {
                  onGitHubPush(repoDetails);
                }
                setIsGitHubPushModalOpen(false);
              }}
              lastGithubUsed={lastGithubUsed}
            />
          )}
        </motion.div>
      </AnimatePresence>
    );
  }
);
