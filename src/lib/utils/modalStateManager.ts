/**
 * Utility functions for managing modal states and tracking data in localStorage
 * These states should persist across sessions and not be cleared during logout
 *
 * IMPORTANT: Always use clearLocalStorageSafely() or clearAllStorageSafely()
 * instead of localStorage.clear() to preserve modal states and UTM tracking data across the app.
 */

const MODAL_STORAGE_KEY = 'emergent_modal_states';

interface ModalStates {
  hasSeenFeatureModal?: boolean;
  hasSeenWelcomeModal?: boolean;
  hasSeenForkIntro?: boolean;
  hasSeenAssetModal?: boolean;
  // Add other modal states here as needed
}

interface TrackingData {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
  user_referrer?: string;
  first_login_completed?: string;
  pending_referral_code?: string;
  referral_applied?: string;
  referral_modal_data?: string;
  // Add other tracking data keys here as needed
}

/**
 * Get all modal states from localStorage
 */
export function getModalStates(): ModalStates {
  try {
    const stored = localStorage.getItem(MODAL_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('Error reading modal states from localStorage:', error);
    return {};
  }
}

/**
 * Set a specific modal state
 */
export function setModalState(modalKey: keyof ModalStates, value: boolean): void {
  try {
    const currentStates = getModalStates();
    const updatedStates = {
      ...currentStates,
      [modalKey]: value
    };
    localStorage.setItem(MODAL_STORAGE_KEY, JSON.stringify(updatedStates));
  } catch (error) {
    console.error('Error saving modal state to localStorage:', error);
  }
}

/**
 * Get a specific modal state
 */
export function getModalState(modalKey: keyof ModalStates, defaultValue: boolean = false): boolean {
  const states = getModalStates();
  return states[modalKey] ?? defaultValue;
}

/**
 * Check if user has seen the feature modal
 */
export function hasSeenFeatureModal(): boolean {
  return getModalState('hasSeenFeatureModal', false);
}

export function hasSeenAssetModal(): boolean {
  return getModalState('hasSeenAssetModal', false);
}

/**
 * Mark feature modal as seen
 */
export function markFeatureModalAsSeen(): void {
  setModalState('hasSeenFeatureModal', true);
}

export function markAssetModalAsSeen(): void {
  setModalState('hasSeenAssetModal', true);
}
/**
 * Check if user has seen the welcome modal
 */
export function hasSeenWelcomeModal(): boolean {
  return getModalState('hasSeenWelcomeModal', false);
}

/**
 * Mark welcome modal as seen
 */
export function markWelcomeModalAsSeen(): void {
  setModalState('hasSeenWelcomeModal', true);
}

/**
 * Check if user has seen the fork intro tooltip
 */
export function hasSeenForkIntro(): boolean {
  return getModalState('hasSeenForkIntro', false);
}

/**
 * Mark fork intro tooltip as seen
 */
export function markForkIntroAsSeen(): void {
  setModalState('hasSeenForkIntro', true);
}

/**
 * Reset all modal states (useful for testing or admin purposes)
 */
export function resetModalStates(): void {
  try {
    localStorage.removeItem(MODAL_STORAGE_KEY);
  } catch (error) {
    console.error('Error resetting modal states:', error);
  }
}

/**
 * Reset feature modal state specifically (for testing)
 */
export function resetFeatureModalState(): void {
  setModalState('hasSeenFeatureModal', false);
}

/**
 * Reset fork intro state specifically (for testing)
 */
export function resetForkIntroState(): void {
  setModalState('hasSeenForkIntro', false);
}

/**
 * Migrate old localStorage keys to the new modal state system
 * This should be called once during app initialization
 */
export function migrateOldModalStates(): void {
  try {
    // Migrate old hasSeenForkIntro key
    const oldForkIntro = localStorage.getItem('hasSeenForkIntro');
    if (oldForkIntro === 'true' && !hasSeenForkIntro()) {
      markForkIntroAsSeen();
      localStorage.removeItem('hasSeenForkIntro'); // Clean up old key
    }

  } catch (error) {
    console.error('Error during modal state migration:', error);
  }
}

/**
 * Safely clear localStorage while preserving modal states and tracking data
 * Use this instead of localStorage.clear() to maintain modal preferences and UTM parameters
 */
export function clearLocalStorageSafely(): void {
  try {
    // Preserve modal states and tracking data before clearing
    const modalStates = preserveModalStates();
    const trackingData = preserveTrackingData();

    // Clear localStorage
    localStorage.clear();

    // Restore modal states and tracking data after clearing
    restoreModalStates(modalStates);
    restoreTrackingData(trackingData);
  } catch (error) {
    console.error('Error during safe localStorage clearing:', error);
    // Fallback to regular clear if something goes wrong
    localStorage.clear();
  }
}

/**
 * Safely clear both localStorage and sessionStorage while preserving modal states and tracking data
 * Use this for complete storage cleanup (like logout scenarios)
 */
export function clearAllStorageSafely(): void {
  try {
    // Preserve modal states and tracking data before clearing
    const modalStates = preserveModalStates();
    const trackingData = preserveTrackingData();

    // Clear both storages
    localStorage.clear();
    sessionStorage.clear();

    // Restore modal states and tracking data after clearing
    restoreModalStates(modalStates);
    restoreTrackingData(trackingData);
  } catch (error) {
    console.error('Error during safe storage clearing:', error);
    // Fallback to regular clear if something goes wrong
    localStorage.clear();
    sessionStorage.clear();
  }
}

// Expose reset functions globally for easy testing in console
if (typeof window !== 'undefined') {
  (window as any).resetFeatureModal = resetFeatureModalState;
  (window as any).resetForkIntro = resetForkIntroState;
  (window as any).resetAllModals = resetModalStates;
  (window as any).clearStorageSafely = clearLocalStorageSafely;
  (window as any).clearAllStorageSafely = clearAllStorageSafely;
  (window as any).preserveTrackingData = preserveTrackingData;
  (window as any).restoreTrackingData = restoreTrackingData;
}

/**
 * Preserve modal states during localStorage clearing
 * Call this before localStorage.clear() to backup modal states
 */
export function preserveModalStates(): ModalStates | null {
  try {
    return getModalStates();
  } catch (error) {
    console.error('Error preserving modal states:', error);
    return null;
  }
}

/**
 * Preserve tracking data during localStorage clearing
 * Call this before localStorage.clear() to backup UTM parameters and other tracking data
 */
export function preserveTrackingData(): TrackingData | null {
  try {
    const trackingKeys = [
      'utm_source',
      'utm_medium',
      'utm_campaign',
      'utm_content',
      'utm_term',
      'user_referrer',
      'first_login_completed',
      'pending_referral_code',
      'referral_applied',
      'referral_modal_data'
    ];

    const trackingData: TrackingData = {};

    trackingKeys.forEach(key => {
      const value = localStorage.getItem(key);
      if (value !== null) {
        (trackingData as any)[key] = value;
      }
    });

    return Object.keys(trackingData).length > 0 ? trackingData : null;
  } catch (error) {
    console.error('Error preserving tracking data:', error);
    return null;
  }
}

/**
 * Restore modal states after localStorage clearing
 * Call this after localStorage.clear() to restore modal states
 */
export function restoreModalStates(states: ModalStates | null): void {
  if (!states) return;

  try {
    localStorage.setItem(MODAL_STORAGE_KEY, JSON.stringify(states));
  } catch (error) {
    console.error('Error restoring modal states:', error);
  }
}

/**
 * Restore tracking data after localStorage clearing
 * Call this after localStorage.clear() to restore UTM parameters and other tracking data
 */
export function restoreTrackingData(trackingData: TrackingData | null): void {
  if (!trackingData) return;

  try {
    Object.entries(trackingData).forEach(([key, value]) => {
      if (value !== undefined) {
        localStorage.setItem(key, value);
      }
    });
  } catch (error) {
    console.error('Error restoring tracking data:', error);
  }
}
