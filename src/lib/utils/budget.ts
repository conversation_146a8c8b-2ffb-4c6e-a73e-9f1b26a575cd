export function calculateNextBudget(
    current_max_budget: number, 
    accumulated_cost: number,
    total_available_credits: number
  ): number {

    if (accumulated_cost > total_available_credits) {
      return current_max_budget + Math.min(25, total_available_credits);
    }


    const increased_budget: number = current_max_budget + 25;
    const available_credits: number = total_available_credits - accumulated_cost;
    
    return Math.min(increased_budget, available_credits);
  }