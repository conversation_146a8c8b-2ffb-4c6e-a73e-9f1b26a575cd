/**
 * Utility function to get formatted file type labels for display in UI
 * Returns uppercase labels like PDF, CSV, VIDEO, TXT, etc.
 */
export const getFileTypeLabel = (mimeType: string, fileName?: string): string => {
  // First check by file extension for more specific labels
  if (fileName) {
    const lowerFileName = fileName.toLowerCase();
    if (lowerFileName.endsWith(".pdf")) return "PDF";
    if (lowerFileName.endsWith(".csv")) return "CSV";
    if (lowerFileName.endsWith(".xls") || lowerFileName.endsWith(".xlsx")) return "Excel";
    if (lowerFileName.endsWith(".doc") || lowerFileName.endsWith(".docx")) return "DOC";
    if (lowerFileName.endsWith(".ppt") || lowerFileName.endsWith(".pptx")) return "PPT";
    if (lowerFileName.endsWith(".txt")) return "TXT";
    if (lowerFileName.endsWith(".md")) return "Markdown";
    if (lowerFileName.endsWith(".json")) return "JSON";
    if (lowerFileName.endsWith(".xml")) return "XML";
    if (lowerFileName.endsWith(".html") || lowerFileName.endsWith(".htm")) return "HTML";
    if (lowerFileName.endsWith(".css")) return "CSS";
    if (lowerFileName.endsWith(".js") || lowerFileName.endsWith(".jsx")) return "JS";
    if (lowerFileName.endsWith(".ts") || lowerFileName.endsWith(".tsx")) return "TS";
    if (lowerFileName.endsWith(".py")) return "Python";
    if (lowerFileName.endsWith(".java")) return "Java";
    if (lowerFileName.endsWith(".cpp") || lowerFileName.endsWith(".cc")) return "C++";
    if (lowerFileName.endsWith(".c")) return "C";
    if (lowerFileName.endsWith(".dmg")) return "DMG";
    if (lowerFileName.endsWith(".zip") || lowerFileName.endsWith(".rar") || lowerFileName.endsWith(".7z")) return "Archive";
    if (lowerFileName.endsWith(".mp4") || lowerFileName.endsWith(".avi") || lowerFileName.endsWith(".mov")) return "Video";
    if (lowerFileName.endsWith(".mp3") || lowerFileName.endsWith(".wav") || lowerFileName.endsWith(".flac")) return "Audio";
    if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg") || lowerFileName.endsWith(".png") || lowerFileName.endsWith(".gif")) return "Image";
    if (lowerFileName.endsWith(".svg")) return "SVG";
  }

  // Fallback to MIME type checking
  if (mimeType.includes("pdf")) return "PDF";
  if (mimeType.includes("csv")) return "CSV";
  if (mimeType.includes("excel") || mimeType.includes("spreadsheet")) return "Excel";
  if (mimeType.includes("word") || mimeType.includes("document")) return "DOC";
  if (mimeType.includes("powerpoint") || mimeType.includes("presentation")) return "PPT";
  if (mimeType.includes("text")) return "TXT";
  if (mimeType.includes("image")) return "Image";
  if (mimeType.includes("video")) return "Video";
  if (mimeType.includes("audio")) return "Audio";
  if (mimeType.includes("zip") || mimeType.includes("archive") || mimeType.includes("compressed")) return "Archive";
  if (mimeType.includes("json")) return "JSON";
  if (mimeType.includes("xml")) return "XML";
  if (mimeType.includes("html")) return "HTML";
  if (mimeType.includes("css")) return "CSS";
  if (mimeType.includes("javascript")) return "JS";
  if (mimeType.includes("python")) return "Python";

  // Final fallback: return empty string if no specific file type is recognized
  return "";
};
