/**
 * Dynamic seat calculation utility for Pro Modal
 * Calculates remaining seats based on time until launch date
 */

export const TOTAL_SEATS = 80;
export const LAUNCH_DATE = new Date('2025-07-30T00:00:00Z');

/**
 * Calculates the number of remaining seats based on current date
 * Uses a non-linear decay function to simulate realistic booking patterns
 *
 * @returns {number} Number of remaining seats (0-80)
 */
export const calculateRemainingSeats = (): number => {
  const now = new Date();
  const timeUntilLaunch = LAUNCH_DATE.getTime() - now.getTime();

  // If launch date has passed, return 0 seats
  if (timeUntilLaunch <= 0) {
    return 0;
  }

  // Countdown starts today (July 21st) with 80 seats and runs for 9 days until launch
  const countdownStartDate = new Date('2025-07-21T00:00:00Z');
  const totalCountdownDuration = LAUNCH_DATE.getTime() - countdownStartDate.getTime();
  const timeElapsed = now.getTime() - countdownStartDate.getTime();

  // If we're before the countdown start, show all seats
  if (timeElapsed <= 0) {
    return TOTAL_SEATS;
  }

  // Calculate progress (0 to 1)
  const progress = Math.min(timeElapsed / totalCountdownDuration, 1);

  // Use aggressive exponential decay - seats decrease much faster
  // Over the 9-day countdown period from July 21st to July 30th
  const seatDecayFactor = Math.pow(progress, 0.8); // Much more aggressive decay
  const seatsReserved = Math.floor(TOTAL_SEATS * seatDecayFactor);
  const remainingSeats = Math.max(TOTAL_SEATS - seatsReserved, 0);

  return remainingSeats;
};

/**
 * Gets the urgency level based on remaining seats
 * 
 * @param remainingSeats - Number of remaining seats
 * @returns {'high' | 'medium' | 'low'} Urgency level
 */
export const getSeatUrgency = (remainingSeats: number): 'high' | 'medium' | 'low' => {
  if (remainingSeats <= 10) return 'high';
  if (remainingSeats <= 25) return 'medium';
  return 'low';
};

/**
 * Gets CSS classes for seat display based on urgency
 * 
 * @param remainingSeats - Number of remaining seats
 * @returns {string} CSS classes for styling
 */
export const getSeatDisplayClasses = (remainingSeats: number): string => {
  const urgency = getSeatUrgency(remainingSeats);
  
  switch (urgency) {
    case 'high':
      return 'text-red-400 animate-pulse';
    case 'medium':
      return 'text-yellow-400';
    default:
      return 'text-white/80';
  }
};

/**
 * Gets days remaining until launch
 * 
 * @returns {number} Days remaining (can be negative if past launch)
 */
export const getDaysUntilLaunch = (): number => {
  const now = new Date();
  const timeUntilLaunch = LAUNCH_DATE.getTime() - now.getTime();
  return Math.ceil(timeUntilLaunch / (24 * 60 * 60 * 1000));
};

/**
 * Formats the launch date for display
 * 
 * @returns {string} Formatted launch date
 */
export const getFormattedLaunchDate = (): string => {
  return LAUNCH_DATE.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
};
