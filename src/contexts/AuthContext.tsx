import { create<PERSON>ontext, useContext, useEffect, useRef, useState } from "react";
import { User, Session } from "@supabase/supabase-js";
import { supabase } from "../lib/supabase";
import { useToast } from "../hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { identifyPostHogUser, captureError, trackAuthEvent, trackUserOnboarding, trackUserSource } from "../services/postHogService";
import { clearLocalStorageSafely } from "../lib/utils/modalStateManager";

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<{ success: boolean; error?: any }>;
  signInWithEmail: (email: string, password: string) => Promise<{ success: boolean; error?: any }>;
  signUpWithEmail: (name: string, email: string, password: string) => Promise<{ success: boolean; needsVerification?: boolean; error?: any }>;
  signInWithOAuth: (provider: "google" | "github" | "apple") => Promise<{ success: boolean; error?: any }>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: any }>;
  updatePassword: (password: string) => Promise<{ success: boolean; error?: any }>;
  resendVerificationEmail: (email: string) => Promise<{ success: boolean; error?: any }>;
  verifyInviteCode: (inviteCode: string, userId?: string) => Promise<{ success: boolean; banned?: boolean; error?: any }>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  loading: true,
  refreshSession: async () => ({ success: false }),
  signOut: async () => { },
  signInWithEmail: async () => ({ success: false }),
  signUpWithEmail: async () => ({ success: false }),
  signInWithOAuth: async () => ({ success: false }),
  resetPassword: async () => ({ success: false }),
  updatePassword: async () => ({ success: false }),
  resendVerificationEmail: async () => ({ success: false }),
  verifyInviteCode: async () => ({ success: false }),
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const currentSessionIdRef = useRef<string | null>(null);
  const authUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Hooks
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    // let mounted = true;

    const initialize = async () => {
      try {
        console.log('Starting auth initialization...');
        // Get initial session
        const {
          data: { session: initialSession },
        } = await supabase.auth.getSession();

        console.log('Initial session retrieved:', initialSession);
        if (initialSession) {
          // currentSessionIdRef.current = initialSession.access_token;
          console.log('Setting initial session and user');
          setSession(initialSession);
          setUser(initialSession.user);
          identifyPostHogUser(initialSession.user);
        } else {
          console.log('No initial session found');
          // Clear Sentry user
          identifyPostHogUser(null);
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        captureError(error instanceof Error ? error : String(error), { context: 'auth_initialization' });
        toast({
          title: "Error",
          description: "Failed to initialize authentication.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, newSession) => {

      switch (event) {
        case "SIGNED_OUT":
          console.log('Signout event:', event);
          setSession(null);
          setUser(null);
          identifyPostHogUser(null);
          break;
        default:
          console.log('Auth event:', event);
          if (newSession) {
            console.log('Setting session and user');
            setSession(newSession);
            setUser(newSession.user);
            identifyPostHogUser(newSession.user);

            // Update user metadata with UTM data for new users (especially OAuth users)
            if (event === 'SIGNED_IN' && newSession.user) {
              updateUserMetadataWithUTM(newSession.user);
            }
          }
          break;
      }
    });

    initialize();

    return () => {
      subscription.unsubscribe();
    };
  }, [toast]);

  // Helper function to update user metadata with UTM data
  const updateUserMetadataWithUTM = async (user: User) => {
    try {
      // Check if user already has UTM data to avoid overwriting
      if (user.user_metadata?.utm_source) {
        return; // UTM data already exists
      }

      // Get UTM data from localStorage
      const utmSource = localStorage.getItem('utm_source') || '';
      const utmCampaign = localStorage.getItem('utm_campaign') || '';
      const utmMedium = localStorage.getItem('utm_medium') || '';
      const utmContent = localStorage.getItem('utm_content') || '';
      const utmTerm = localStorage.getItem('utm_term') || '';
      const userReferrer = localStorage.getItem('user_referrer') || '';

      // Only update if we have UTM data
      if (utmSource || utmCampaign || utmMedium || utmContent || utmTerm || userReferrer) {
        const { error } = await supabase.auth.updateUser({
          data: {
            ...user.user_metadata,
            utm_source: utmSource,
            utm_campaign: utmCampaign,
            utm_medium: utmMedium,
            utm_content: utmContent,
            utm_term: utmTerm,
            user_referrer: userReferrer,
          }
        });

        if (error) {
          console.error('Error updating user metadata with UTM data:', error);
        } else {
          console.log('Successfully updated user metadata with UTM data');
        }
      }
    } catch (error) {
      console.error('Error in updateUserMetadataWithUTM:', error);
    }
  };

  // Auth methods
  const signInWithEmail = async (email: string, password: string) => {
    try {
      // Track login attempt
      trackAuthEvent('login_attempt', { method: 'email', email });

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Check for "Email not confirmed" error
        if (error.message === "Email not confirmed") {
          // Track verification needed
          trackAuthEvent('login_failure', {
            method: 'email',
            email,
            reason: 'email_not_confirmed',
            needsVerification: true
          });

          // Use URL parameter instead of state
          navigate(`/verify?email=${encodeURIComponent(email)}`);
          return { success: false, error: { needsVerification: true, message: error.message } };
        }

        // Track login failure
        trackAuthEvent('login_failure', {
          method: 'email',
          email,
          reason: error.message,
          errorCode: error.status || 'unknown'
        });

        return { success: false, error: error.message };
      }

      // Track login success
      trackAuthEvent('login_success', {
        method: 'email',
        email,
        userId: data?.user?.id
      });

      // Check if this is the user's first login
      const isFirstLogin = localStorage.getItem('first_login_completed') !== 'true';
      if (isFirstLogin && data?.user) {
        localStorage.setItem('first_login_completed', 'true');
        trackUserOnboarding('first_login', {
          userId: data.user.id,
          method: 'email',
          email
        });

        // Track user source on first login
        trackUserSource({
          isNewUser: true,
          userId: data.user.id,
          loginMethod: 'email'
        });
      }

      return { success: true };
    } catch (error: any) {
      console.error("Error signing in:", error);

      // Track login error
      trackAuthEvent('login_failure', {
        method: 'email',
        email,
        reason: error.message || 'unknown_error',
        isException: true
      });

      return { success: false, error: error.message || "Failed to sign in" };
    }
  };

  const signUpWithEmail = async (name: string, email: string, password: string) => {
    try {
      // Track signup attempt
      trackAuthEvent('signup_attempt', { method: 'email', email });

      // Get UTM data from localStorage
      const utmSource = localStorage.getItem('utm_source') || '';
      const utmCampaign = localStorage.getItem('utm_campaign') || '';
      const utmMedium = localStorage.getItem('utm_medium') || '';
      const utmContent = localStorage.getItem('utm_content') || '';
      const utmTerm = localStorage.getItem('utm_term') || '';
      const userReferrer = localStorage.getItem('user_referrer') || '';

      // Preserve current URL parameters in email verification link
      const currentParams = new URLSearchParams(window.location.search);
      const emailRedirectUrl = currentParams.toString()
        ? `${window.location.origin}/activate?${currentParams.toString()}`
        : `${window.location.origin}/activate`;

      const { data: { user }, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: emailRedirectUrl,
          data: {
            full_name: name,
            utm_source: utmSource,
            utm_campaign: utmCampaign,
            utm_medium: utmMedium,
            utm_content: utmContent,
            utm_term: utmTerm,
            user_referrer: userReferrer,
          },
        },
      });

      if (error) {
        // Track signup failure
        trackAuthEvent('signup_failure', {
          method: 'email',
          email,
          reason: error.message,
          errorCode: error.status || 'unknown'
        });

        return { success: false, error: error.message };
      }

      // Check if user already exists (identities length will be 0)
      if (user && user.identities && user.identities.length === 0) {
        // Track user already exists
        trackAuthEvent('signup_failure', {
          method: 'email',
          email,
          reason: 'user_already_exists'
        });

        return { success: false, error: "User already exists" };
      }

      // @ts-ignore
      // Check if email verification is required
      if (user && !user.email_verified) {
        // Track signup success but needs verification
        trackAuthEvent('signup_success', {
          method: 'email',
          email,
          userId: user.id,
          needsVerification: true
        });

        // Track first-time user signup with verification needed
        trackUserOnboarding('first_login', {
          userId: user.id,
          method: 'email',
          email,
          isSignup: true,
          needsVerification: true
        });

        // Track user source on signup with verification
        trackUserSource({
          isNewUser: true,
          userId: user.id,
          signupMethod: 'email',
          needsVerification: true
        });

        return { success: true, needsVerification: true };
      }

      // Track signup success
      trackAuthEvent('signup_success', {
        method: 'email',
        email,
        userId: user?.id
      });

      // Track first-time user signup
      trackUserOnboarding('first_login', {
        userId: user?.id,
        method: 'email',
        email,
        isSignup: true
      });

      // Track user source on signup
      trackUserSource({
        isNewUser: true,
        userId: user?.id,
        signupMethod: 'email'
      });

      return { success: true };
    } catch (error: any) {
      console.error("Error signing up:", error);

      // Track signup error
      trackAuthEvent('signup_failure', {
        method: 'email',
        email,
        reason: error.message || 'unknown_error',
        isException: true
      });

      return { success: false, error: error.message || "Failed to sign up" };
    }
  };

  const signInWithOAuth = async (provider: "google" | "github" | "apple") => {
    try {
      // Track OAuth login attempt
      trackAuthEvent('login_attempt', { method: provider });

      // Preserve all URL parameters during OAuth flow
      const currentParams = new URLSearchParams(window.location.search);

      // Also check for referral code in localStorage as fallback
      const localStorageReferralCode = localStorage.getItem('pending_referral_code');
      if (localStorageReferralCode && !currentParams.has('referral') && !currentParams.has('ref')) {
        currentParams.set('referral', localStorageReferralCode);
      }

      const redirectTo = currentParams.toString()
        ? `${window.location.origin}/activate?${currentParams.toString()}`
        : `${window.location.origin}/activate`;

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo,
          ...(provider === "github" ? { scopes: "repo,read:user,user:email" } : {}),
        },
      });

      if (error) {
        // Track OAuth login failure
        trackAuthEvent('login_failure', {
          method: provider,
          reason: error.message,
          errorCode: error.status || 'unknown'
        });

        return { success: false, error: error.message };
      }

      // Store info to track first login on callback
      sessionStorage.setItem('oauth_login_pending', 'true');
      sessionStorage.setItem('oauth_provider', provider);

      return { success: true };
    } catch (error: any) {
      console.error(`Error signing in with ${provider}:`, error);

      // Track OAuth login error
      trackAuthEvent('login_failure', {
        method: provider,
        reason: error.message || 'unknown_error',
        isException: true
      });

      return { success: false, error: error.message || `Failed to sign in with ${provider}` };
    }
  };

  const resetPassword = async (email: string) => {
    try {
      // Preserve current URL parameters in password reset link
      const currentParams = new URLSearchParams(window.location.search);
      const resetRedirectUrl = currentParams.toString()
        ? `${window.location.origin}/reset-password?${currentParams.toString()}`
        : `${window.location.origin}/reset-password`;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: resetRedirectUrl,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      console.error("Error resetting password:", error);
      return { success: false, error: error.message || "Failed to send password reset email" };
    }
  };

  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password,
      });

      await signOut();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      console.error("Error updating password:", error);
      return { success: false, error: error.message || "Failed to update password" };
    }
  };

  const resendVerificationEmail = async (email: string) => {
    try {
      // Preserve current URL parameters in resent email verification link
      const currentParams = new URLSearchParams(window.location.search);
      const emailRedirectUrl = currentParams.toString()
        ? `${window.location.origin}/activate?${currentParams.toString()}`
        : `${window.location.origin}/activate`;

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
        options: {
          emailRedirectTo: emailRedirectUrl,
        },
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      console.error("Error resending verification email:", error);
      return { success: false, error: error.message || "Failed to resend verification email" };
    }
  };

  const verifyInviteCode = async (inviteCode: string, userId?: string) => {
    try {
      const { data } = await supabase.functions.invoke('verify-invite-code', {
        body: {
          inviteCode: inviteCode.trim(),
          userId: userId || user?.id
        }
      });

      if (data === null) {
        return { success: false, error: "Failed to verify invite code" };
      }

      if (!data.success) {
        return { success: false, error: data.error || "Failed to verify invite code" };
      }

      if (data.banned) {
        return { success: false, banned: true, error: "Your account has been banned" };
      }

      return { success: true };
    } catch (error: any) {
      console.error("Error verifying invite code:", error);
      return { success: false, error: error.message || "Failed to verify invite code" };
    }
  };

  const signOut = async () => {
    try {
      // Track logout attempt
      trackAuthEvent('logout', { userId: user?.id });

      // await supabase.auth.signOut();
      clearLocalStorageSafely();

      // Clear user identification
      identifyPostHogUser(null);
      window.location.href = '/login';


      if (window?.ReactNativeWebView) {
        // Send message to React Native to open external browser
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'LOGOUT_EVENT'
        }));
      }
      // navigate("/");
    } catch (error: any) {
      console.error("Error signing out:", error);
      captureError(error, { context: 'sign_out' });


      toast({
        title: "Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive",
      });
    }
  };

  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error("Error refreshing session:", error);
        captureError(error, { context: 'refresh_session' });
        return { success: false, error: error.message };
      }

      if (data.session) {
        setSession(data.session);
        setUser(data.session.user);
        identifyPostHogUser(data.session.user);
      }

      return { success: true };
    } catch (error: any) {
      console.error("Error refreshing session:", error);
      return { success: false, error: error.message || "Failed to refresh session" };
    }
  };

  const value = {
    user,
    session,
    loading,
    signOut,
    signInWithEmail,
    signUpWithEmail,
    signInWithOAuth,
    resetPassword,
    updatePassword,
    resendVerificationEmail,
    verifyInviteCode,
    refreshSession,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};