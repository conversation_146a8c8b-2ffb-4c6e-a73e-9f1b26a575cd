<svg width="80" height="85" viewBox="0 0 80 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12059_111984)">
<path d="M54.0912 36.7525H53.1824V35.8436H52.2736V34.9348H51.3647H50.4559H49.5471H48.6383H47.7295H46.8207H45.9119H44.9985H44.0896H43.1808H42.272H41.3632H40.4544H39.5456V34.026H38.6368V33.1172H37.728H36.8192H35.9103V34.026H35.0015V34.9348H34.0881H33.1793H32.2705H31.3617H30.4529H29.544H28.6352H27.7264V35.8436H26.8176V36.7525H25.9088V37.6613H25V38.5701V39.4789V40.3924V41.3012V42.21V43.1188V44.0276V44.9364V45.8453V46.7541V47.6629V48.5717V49.4805V50.3893V51.3028V52.2116V53.1204V54.0293H25.9088V54.9381H26.8176V55.8469H27.7264V56.7557V57.6645H26.8176V58.5733H25.9088V59.4821H26.8176H27.7264H28.6352H29.544V58.5733H30.4529V57.6645H31.3617V56.7557H32.2705H33.1793H34.0881H35.0015H35.9103H36.8192H37.728H38.6368H39.5456H40.4544H41.3632H42.272H43.1808H44.0896H44.9985H45.9119H46.8207H47.7295H48.6383V57.6645H49.5471V58.5733H50.4559V59.4821H51.3647H52.2736H53.1824H54.0912V58.5733H53.1824V57.6645H52.2736V56.7557V55.8469H53.1824V54.9381H54.0912V54.0293H55V53.1204V52.2116V51.3028V50.3893V49.4805V48.5717V47.6629V46.7541V45.8453V44.9364V44.0276V43.1188V42.21V41.3012V40.3924V39.4789V38.5701V37.6613H54.0912V36.7525ZM53.1824 41.3012H52.2736H51.3647H50.4559H49.5471H48.6383V40.3924H49.5471H50.4559H51.3647H52.2736H53.1824V41.3012ZM53.1824 43.1188H52.2736H51.3647H50.4559H49.5471H48.6383V42.21H49.5471H50.4559H51.3647H52.2736H53.1824V43.1188ZM53.1824 44.9364H52.2736H51.3647H50.4559H49.5471H48.6383V44.0276H49.5471H50.4559H51.3647H52.2736H53.1824V44.9364ZM53.1824 46.7541H52.2736H51.3647H50.4559H49.5471H48.6383V45.8453H49.5471H50.4559H51.3647H52.2736H53.1824V46.7541ZM52.2736 50.3893V51.3028H51.3647V52.2116H50.4559H49.5471V51.3028H48.6383V50.3893V49.4805H49.5471V48.5717H50.4559H51.3647V49.4805H52.2736V50.3893ZM27.7264 37.6613V36.7525H28.6352H29.544H30.4529H31.3617H32.2705H33.1793H34.0881H35.0015H35.9103H36.8192H37.728H38.6368H39.5456H40.4544H41.3632H42.272H43.1808H44.0896H44.9985H45.9119V37.6613H46.8207V38.5701V39.4789V40.3924V41.3012V42.21V43.1188V44.0276V44.9364V45.8453V46.7541V47.6629V48.5717V49.4805V50.3893V51.3028V52.2116V53.1204H45.9119V54.0293H44.9985H44.0896H43.1808H42.272H41.3632H40.4544H39.5456H38.6368H37.728H36.8192H35.9103H35.0015H34.0881H33.1793H32.2705H31.3617H30.4529H29.544H28.6352H27.7264V53.1204H26.8176V52.2116V51.3028V50.3893V49.4805V48.5717V47.6629V46.7541V45.8453V44.9364V44.0276V43.1188V42.21V41.3012V40.3924V39.4789V38.5701V37.6613H27.7264Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter1_d_12059_111984)">
<path d="M51.3645 49.4766H50.4557H49.5469V50.3854V51.2988H50.4557H51.3645V50.3854V49.4766Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter2_d_12059_111984)">
<path d="M34.9994 32.2109H34.0859V33.1197V34.0286H34.9994V33.1197V32.2109Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter3_d_12059_111984)">
<path d="M34.0885 30.3906H33.1797V31.2995V32.2083H34.0885V31.2995V30.3906Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter4_d_12059_111984)">
<path d="M33.1783 28.5781H32.2695V29.487V30.3957H33.1783V29.487V28.5781Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter5_d_12059_111984)">
<path d="M32.2721 26.75H31.3633V27.6588V28.5723H32.2721V27.6588V26.75Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter6_d_12059_111984)">
<path d="M40.6256 32.2109H41.5391V33.1197V34.0286H40.6256V33.1197V32.2109Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter7_d_12059_111984)">
<path d="M41.5365 30.3906H42.4453V31.2995V32.2083H41.5365V31.2995V30.3906Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter8_d_12059_111984)">
<path d="M42.4467 28.5781H43.3555V29.487V30.3957H42.4467V29.487V28.5781Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter9_d_12059_111984)">
<path d="M43.3529 26.75H44.2617V27.6588V28.5723H43.3529V27.6588V26.75Z" fill="#00FFCC"/>
</g>
<g filter="url(#filter10_d_12059_111984)">
<path d="M44.3724 25.5H45.2812V26.4088V27.3223H44.3724V26.4088V25.5Z" fill="#00FFCC"/>
</g>
<defs>
<filter id="filter0_d_12059_111984" x="0" y="8.11719" width="80" height="76.3672" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter1_d_12059_111984" x="24.5469" y="24.4766" width="51.8164" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter2_d_12059_111984" x="9.08594" y="7.21094" width="50.9141" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter3_d_12059_111984" x="8.17969" y="5.39062" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter4_d_12059_111984" x="7.26953" y="3.57812" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter5_d_12059_111984" x="6.36328" y="1.75" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter6_d_12059_111984" x="15.625" y="7.21094" width="50.9141" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter7_d_12059_111984" x="16.5352" y="5.39062" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter8_d_12059_111984" x="17.4453" y="3.57812" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter9_d_12059_111984" x="18.3516" y="1.75" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
<filter id="filter10_d_12059_111984" x="19.3711" y="0.5" width="50.9102" height="51.8203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.8 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12059_111984"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12059_111984" result="shape"/>
</filter>
</defs>
</svg>
