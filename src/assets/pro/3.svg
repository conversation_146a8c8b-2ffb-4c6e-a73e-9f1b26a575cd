<svg width="56" height="65" viewBox="0 0 56 65" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="28" cy="24" r="24" fill="#151517"/>
<g filter="url(#filter0_ddiii_12099_114999)">
<path d="M8.80078 24.0047C8.80078 13.4008 17.3969 4.80469 28.0008 4.80469V4.80469C38.6047 4.80469 47.2008 13.4008 47.2008 24.0047V24.0047C47.2008 34.6086 38.6047 43.2047 28.0008 43.2047V43.2047C17.3969 43.2047 8.80078 34.6086 8.80078 24.0047V24.0047Z" fill="white" fill-opacity="0.05"/>
<circle cx="28.0008" cy="24.0047" r="19.2" fill="url(#paint0_linear_12099_114999)" fill-opacity="0.7"/>
<circle cx="28.0008" cy="24.0047" r="19.2" fill="url(#paint1_linear_12099_114999)" fill-opacity="0.2"/>
<path d="M27.439 29.0133C29.5857 29.0133 30.099 27.9167 30.099 26.96C30.099 25.9333 29.4223 25 27.6023 25H25.9923V22.0133H27.6023C28.7457 22.0133 29.5857 21.36 29.5857 20.3567C29.5857 19.54 28.9323 18.6533 27.3223 18.6533C25.6657 18.6533 24.919 19.82 24.8257 20.9867L22.0257 19.68C22.5157 17.3 24.359 15.4567 27.4857 15.4567C31.1723 15.4567 33.039 17.72 33.039 20.0767C33.039 21.8967 31.7323 22.9233 31.009 23.25C32.4323 23.7633 33.5523 25.0933 33.5523 26.9367C33.5523 29.62 31.6857 32.21 27.6023 32.21C24.2423 32.21 22.1423 30.1567 21.7923 27.9167L24.5923 26.61C24.709 27.73 25.5023 29.0133 27.439 29.0133Z" fill="#80FFF9"/>
</g>
<defs>
<filter id="filter0_ddiii_12099_114999" x="0.663301" y="4.80375" width="54.6734" height="59.7612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.76405"/>
<feGaussianBlur stdDeviation="2.88249"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12099_114999"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="13.2243"/>
<feGaussianBlur stdDeviation="4.06874"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_12099_114999" result="effect2_dropShadow_12099_114999"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_12099_114999" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.37343"/>
<feGaussianBlur stdDeviation="2.37343"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_12099_114999"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.03437"/>
<feGaussianBlur stdDeviation="0.678123"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.02 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_12099_114999" result="effect4_innerShadow_12099_114999"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.339062"/>
<feGaussianBlur stdDeviation="0.169998"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_12099_114999" result="effect5_innerShadow_12099_114999"/>
</filter>
<linearGradient id="paint0_linear_12099_114999" x1="28.0008" y1="4.80469" x2="28.0008" y2="43.2047" gradientUnits="userSpaceOnUse">
<stop stop-color="#242528"/>
<stop offset="1" stop-color="#111113"/>
</linearGradient>
<linearGradient id="paint1_linear_12099_114999" x1="28.0008" y1="37.9304" x2="28.0008" y2="43.2047" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0"/>
<stop offset="1"/>
</linearGradient>
</defs>
</svg>
