export interface ImageData {
  mime_type: string;
  img_base64: string;
  originalFile: File;
}

export interface ResponseImageData {
  mime_type: string;
  img_base64: string;
}

// Base message interface
export interface BaseMessage {
  id: string;
  content: string;
  job_id?: string | null;
  step_number?: number;
  container_id?: string;
  request_id?: string;
  status?: 'pending' | 'confirmed'; // Add status field
  base64_image_list?: ResponseImageData[];
  timestamp?: string;
}

// Agent specific message interface
export interface AgentMessage extends BaseMessage {
  thought: string;
  enable_rollback: boolean;
  role: "user" | "assistant";
  action?: string | null;
  observation?: string | null;
  env_success?: boolean;
  containerId?: string;
  function_name?: string | null;
  acc_cost?: number;
  max_budget?: number;
  cmd_execution_mode?: string;
  error?: boolean;
  error_message?: string;
  request_id?: string;
  subagent_trajectory : any;
  timestamp: string;
  args: any;
  warning_for_token_limit?: boolean;
  current_token_count?: number;
  max_token_count?: number;
  error_ts?: string;
  error_message_exists?: boolean;
}

// UI utils message interface
export interface UIMessage extends BaseMessage {
  role: "data" | "user" | "assistant" | "system";
  warning_for_token_limit?: boolean;
  current_token_count?: number;
  max_token_count?: number;
  agent_name?: string;
  subagent_trajectory?: any;
  artifacts?: {
    name: string;
    url: string;
  }[];
  artifact_shared_data?: {
    artifacts?: {
      name: string;
      url: string;
    }[];
    base64_image_list?: ResponseImageData[];
    content: string;
    agent_name?: string;
  };
}



export interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  job_id?: string | null;
  step_number?: number;
  container_id?: string;
  error?: boolean;
  error_message?: string;
  timestamp: string;
  expertise_type?: string;
  request_id?: string;
  warning_for_token_limit?: boolean;
  current_token_count?: number;
  max_token_count?: number;
  error_ts?: string;
  base64_image_list?: {
    img_base64: string;
    mime_type: string;
  }[];
  subagent_trajectory?: {
    id: string;
    role: "user" | "assistant";
    content: string;
    timestamp: string;
    error?: boolean;
    error_message?: string;
    expertise_type?: string;
  }[];
}
