import { useState, useEffect, useCallback } from 'react';
import { clearLocalStorageSafely } from '../lib/utils/modalStateManager';

/**
 * Custom hook for managing localStorage with TypeScript support
 * Provides safe localStorage operations with error handling and type safety
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error also return initialValue
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        // Save state
        setStoredValue(valueToStore);
        // Save to local storage
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      } catch (error) {
        // A more advanced implementation would handle the error case
        console.error(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // Function to remove the item from localStorage
  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

/**
 * Hook for managing multiple localStorage keys at once
 * Useful for bulk operations and cleanup
 */
export function useLocalStorageManager() {
  const setItem = useCallback(<T>(key: string, value: T): void => {
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, []);

  const getItem = useCallback(<T>(key: string, defaultValue?: T): T | null => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch (error) {
      console.error(`Error getting localStorage key "${key}":`, error);
      return defaultValue || null;
    }
  }, []);

  const removeItem = useCallback((key: string): void => {
    try {
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, []);

  const removeItems = useCallback((keys: string[]): void => {
    keys.forEach(key => removeItem(key));
  }, [removeItem]);

  const clearAll = useCallback((): void => {
    try {
      // Use the safe clearing function to preserve modal states and tracking data
      clearLocalStorageSafely();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      // Fallback to direct clear if safe clearing fails
      window.localStorage.clear();
    }
  }, []);

  const hasItem = useCallback((key: string): boolean => {
    try {
      return window.localStorage.getItem(key) !== null;
    } catch (error) {
      console.error(`Error checking localStorage key "${key}":`, error);
      return false;
    }
  }, []);

  const getAllKeys = useCallback((): string[] => {
    try {
      return Object.keys(window.localStorage);
    } catch (error) {
      console.error('Error getting localStorage keys:', error);
      return [];
    }
  }, []);

  const getItemsByPrefix = useCallback((prefix: string): Record<string, any> => {
    try {
      const items: Record<string, any> = {};
      const keys = getAllKeys();
      
      keys.forEach(key => {
        if (key.startsWith(prefix)) {
          items[key] = getItem(key);
        }
      });
      
      return items;
    } catch (error) {
      console.error(`Error getting localStorage items with prefix "${prefix}":`, error);
      return {};
    }
  }, [getAllKeys, getItem]);

  const removeItemsByPrefix = useCallback((prefix: string): void => {
    try {
      const keys = getAllKeys();
      keys.forEach(key => {
        if (key.startsWith(prefix)) {
          removeItem(key);
        }
      });
    } catch (error) {
      console.error(`Error removing localStorage items with prefix "${prefix}":`, error);
    }
  }, [getAllKeys, removeItem]);

  return {
    setItem,
    getItem,
    removeItem,
    removeItems,
    clearAll,
    hasItem,
    getAllKeys,
    getItemsByPrefix,
    removeItemsByPrefix,
  };
}

/**
 * Interface for referral modal display data
 */
export interface ReferralModalData {
  shouldShow: boolean;
  message: string;
  reward: number;
  timestamp: number;
}

/**
 * Hook specifically for referral-related localStorage management
 * Provides convenient methods for referral data cleanup and modal management
 */
export function useReferralLocalStorage() {
  const { removeItems, getItem, hasItem } = useLocalStorageManager();

  const clearReferralData = useCallback(() => {
    const referralKeys = [
      'pending_referral_code',
      'referral_applied',
      'referral_modal_data',
    ];

    removeItems(referralKeys);
    console.log('Cleared referral localStorage data');
  }, [removeItems]);

  const hasReferralData = useCallback(() => {
    return hasItem('pending_referral_code') || hasItem('referral_applied') || hasItem('referral_modal_data');
  }, [hasItem]);

  const getReferralData = useCallback(() => {
    return {
      pendingReferralCode: getItem('pending_referral_code'),
      referralApplied: getItem('referral_applied'),
      referralModalData: getItem<ReferralModalData>('referral_modal_data'),
    };
  }, [getItem]);

  // Note: setReferralModalData is now handled directly in the API transformResponse

  // Get referral modal data and check if it should be shown
  const getReferralModalData = useCallback((): ReferralModalData | null => {
    const modalData = getItem<ReferralModalData>('referral_modal_data');

    if (!modalData || !modalData.shouldShow) {
      return null;
    }

    // Check if data is not too old (24 hours)
    const EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 hours
    if (Date.now() - modalData.timestamp > EXPIRY_TIME) {
      console.log('Referral modal data expired, removing');
      removeItems(['referral_modal_data']);
      return null;
    }

    return modalData;
  }, [getItem, removeItems]);

  // Clear referral modal data (called when modal is closed)
  const clearReferralModalData = useCallback(() => {
    removeItems(['referral_modal_data']);
    console.log('Cleared referral modal data');
  }, [removeItems]);

  return {
    clearReferralData,
    hasReferralData,
    getReferralData,
    getReferralModalData,
    clearReferralModalData,
  };
}

/**
 * Hook for session-based localStorage management
 * Automatically cleans up data when component unmounts
 */
export function useSessionLocalStorage<T>(
  key: string,
  initialValue: T,
  clearOnUnmount: boolean = false
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const [value, setValue, removeValue] = useLocalStorage(key, initialValue);

  useEffect(() => {
    if (clearOnUnmount) {
      return () => {
        removeValue();
      };
    }
  }, [clearOnUnmount, removeValue]);

  return [value, setValue, removeValue];
}
