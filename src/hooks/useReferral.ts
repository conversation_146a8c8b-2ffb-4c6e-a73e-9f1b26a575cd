import { useCallback, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import {
  useGetGitHubUserDetailsQuery
} from '@/store/api/apiSlice';
import { isOnAuthRoute } from '@/utils/routeUtils';
import { useAuth } from '@/contexts';

export interface ReferralInfo {
  referralCode: string;
  referrerEmail: string | null;
  isReferred: boolean;
  canApplyReferralCode: boolean;
}

export interface UseReferralReturn {
  // Data
  referralInfo: ReferralInfo | null;
  isLoading: boolean;

  // Utilities
  copyReferralCode: () => Promise<boolean>;
  isValidReferralCode: (code: string) => boolean;

  // Status checks
  isUserReferred: boolean;
  canUserApplyReferral: boolean;
}

export const useReferral = (): UseReferralReturn => {
  const location = useLocation();
  const { user } = useAuth();
  // Define routes where we should NOT call user details API
const isOnAuthRouteCheck = isOnAuthRoute(location.pathname);
  // Fetch user details to get referral info
  const {
    data: userDetails,
    isLoading: isUserDetailsLoading
  } = useGetGitHubUserDetailsQuery(undefined, {
    skip: isOnAuthRouteCheck || !user
  });



  // Extract referral info from user details
  const referralInfo: ReferralInfo | null = useMemo(() => {
    if (!userDetails?.referral_info) return null;

    const { referral_code, referrer_email } = userDetails.referral_info;
    
    return {
      referralCode: referral_code || '',
      referrerEmail: referrer_email,
      isReferred: !!referrer_email,
      canApplyReferralCode: !referrer_email, // Can only apply if not already referred
    };
  }, [userDetails]);

  // Computed status checks
  const isUserReferred = useMemo(() => {
    return !!referralInfo?.isReferred;
  }, [referralInfo]);

  const canUserApplyReferral = useMemo(() => {
    return !!referralInfo?.canApplyReferralCode;
  }, [referralInfo]);



  // Copy referral code to clipboard
  const copyReferralCode = useCallback(async (): Promise<boolean> => {
    if (!referralInfo?.referralCode) {
      return false;
    }

    try {
      await navigator.clipboard.writeText(referralInfo.referralCode);
      return true;
    } catch (error) {
      console.error('Failed to copy referral code:', error);
      return false;
    }
  }, [referralInfo?.referralCode]);

  // Validate referral code format
  const isValidReferralCode = useCallback((code: string): boolean => {
    if (!code || typeof code !== 'string') return false;
    
    // Basic validation - adjust based on your referral code format
    const trimmedCode = code.trim();
    
    // Check if it's not empty and has reasonable length
    if (trimmedCode.length < 3 || trimmedCode.length > 50) return false;
    
    // Check if it contains only alphanumeric characters (adjust as needed)
    const alphanumericRegex = /^[a-zA-Z0-9]+$/;
    return alphanumericRegex.test(trimmedCode);
  }, []);

  // Determine loading state
  const isLoading = isUserDetailsLoading;

  // Note: Error handling removed as requested - errors are handled internally

  return {
    // Data
    referralInfo,
    isLoading,

    // Utilities
    copyReferralCode,
    isValidReferralCode,

    // Status checks
    isUserReferred,
    canUserApplyReferral,
  };
};

// Additional utility functions that can be used independently
export const getReferralShareMessage = (referralCode: string): string => {
  return `Join me on Emergent Agent and get 5 ECU! Use my referral code: ${referralCode}`;
};

export const getReferralShareUrl = (referralCode: string, baseUrl: string = window.location.origin): string => {
  return `${baseUrl}/register?ref=${encodeURIComponent(referralCode)}`;
};

export const extractReferralCodeFromUrl = (url: string = window.location.href): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.get('ref');
  } catch {
    return null;
  }
};
