import { useState } from "react";
import { useLazyGetCodeDownloadUrlQuery } from "@/store/api/apiSlice";
import { downloadFileFromUrl } from "@/lib/utils/downloadFile";

/**
 * Custom hook for handling code download functionality
 * Used by components that need to download code archives for jobs
 */
export const useCodeDownload = () => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [getCodeDownloadUrl] = useLazyGetCodeDownloadUrlQuery();

  const handleDownload = async (jobId: string | undefined) => {
    if (!jobId || isDownloading) return;

    setIsDownloading(true);
    try {
      const response = await getCodeDownloadUrl(jobId).unwrap();
      if (response?.download_url) {
        // Use the filename from the API response, or fallback to a default
        const fileName = response.file_name || `code-backup-${jobId}.tar.gz`;
        await downloadFileFromUrl(response.download_url, fileName);
      } else {
        console.error('No download URL received from API');
      }
    } catch (error) {
      console.error('Error downloading code:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  return {
    isDownloading,
    handleDownload,
  };
};
