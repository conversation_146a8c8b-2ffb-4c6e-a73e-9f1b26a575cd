import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { githubConfig } from '@/config';
import {
  useGetGitHubUserDetailsQuery,
  useGetGitHubInstallationsQuery,
  useGetGitHubRepositoriesQuery,
  useGetGitHubBranchesQuery,
  useCreateGitHubRepositoryMutation,
  usePushToGitHubMutation,
  useSaveGitHubInstallationMutation,
  GitHubInstallation,
} from '@/store/api/apiSlice';
import {
  setConnected,
  setConnecting,
  setConnectionError,
  setPrimaryAccount,
  setInstallations,
  setAuthWindowOpen,
  selectGitHubState,
  selectIsGitHubConnected,
  selectShouldCheckConnection,
} from '@/store/githubSlice';
import { isOnAuthRoute } from '@/utils/routeUtils';
import { useAuth } from '@/contexts';

// Main GitHub hook that replaces GitHubContext
export function useGitHub() {
  const dispatch = useDispatch();
  const location = useLocation();
  const { toast } = useToast();
  const githubState = useSelector(selectGitHubState);
  const isConnected = useSelector(selectIsGitHubConnected);
  const shouldCheckConnection = useSelector(selectShouldCheckConnection);

  // Define routes where we should NOT call user details API
  const isOnAuthRouteCheck = isOnAuthRoute(location.pathname);

  const { user } = useAuth();

  
  // RTK Query hooks
  const {
    data: userDetails,
    error: userDetailsError,
    isLoading: isLoadingUserDetails,
    refetch: refetchUserDetails,
  } = useGetGitHubUserDetailsQuery(undefined, {
    skip: !shouldCheckConnection || isOnAuthRouteCheck || !user ,
    pollingInterval: 5 * 60 * 1000, // Poll every 5 minutes
  });

  const {
    data: installations = [],
    isLoading: isLoadingInstallations,
  } = useGetGitHubInstallationsQuery(undefined, {
    skip: !isConnected,
  });

  // Check GitHub connection
  const checkGitHubConnection = useCallback(async (disableStateUpdate = false) => {
    try {
      if (!disableStateUpdate) {
        dispatch(setConnecting(true));
      }

      const result = await refetchUserDetails();

      if (result.data) {
        const connected = result.data.github.authorized;
        const accountName = result.data.github.account_name;

        if (!disableStateUpdate) {
          dispatch(setConnected(connected));
          dispatch(setPrimaryAccount(accountName));
        }

        if (connected && accountName) {
          // Create installations array with primary account
          const primaryInstallation: GitHubInstallation = {
            installation_id: accountName,
            account_login: accountName,
            account_type: 'User',
            user_github_login: accountName,
            app_slug: 'emergent-ai',
            isPrimary: true,
            account: {
              login: accountName,
              id: 0,
              type: 'User',
            },
            target_type: 'User',
          };

          dispatch(setInstallations([primaryInstallation]));
        }

        return connected;
      }

      throw new Error('Failed to fetch user details');
    } catch (error: any) {
      console.error('Error checking GitHub connection:', error);
      const errorMessage = error.message || 'Failed to check GitHub connection';

      if (!disableStateUpdate) {
        dispatch(setConnectionError(errorMessage));
      }

      return false;
    }
  }, [dispatch, refetchUserDetails]);

  // Authenticate with GitHub
  const authenticate = useCallback(async () => {
    try {
      dispatch(setConnecting(true));
      redirectToGitHubInstallation();
    } catch (error: any) {
      console.error('Error authenticating with GitHub:', error);
      dispatch(setConnectionError(error.message || 'Authentication failed'));
    }
  }, [dispatch]);

  // Redirect to GitHub installation
  const redirectToGitHubInstallation = useCallback((options?: {
    isPopup?: boolean;
    onSuccess?: () => void;
    pollingEnabled?: boolean;
  }) => {
    const { isPopup = false, onSuccess, pollingEnabled = true } = options || {};
    
    try {
      const githubAppUrl = githubConfig.appUrl;
      
      if (isPopup) {
        dispatch(setAuthWindowOpen(true));
        const popup = window.open(githubAppUrl, 'github-auth', 'width=600,height=700');
        
        if (pollingEnabled && popup) {
          const pollTimer = setInterval(() => {
            try {
              if (popup.closed) {
                clearInterval(pollTimer);
                dispatch(setAuthWindowOpen(false));
                
                // Check connection after popup closes
                setTimeout(() => {
                  checkGitHubConnection().then((connected) => {
                    if (connected && onSuccess) {
                      onSuccess();
                    }
                  });
                }, 1000);
              }
            } catch (error) {
              // Cross-origin error when popup navigates - this is expected
            }
          }, 1000);
        }
      } else {
        window.open(githubAppUrl, '_blank');
      }
    } catch (error: any) {
      console.error('Error redirecting to GitHub installation:', error);
      dispatch(setConnectionError(error.message || 'Failed to open GitHub installation'));
      dispatch(setAuthWindowOpen(false));
    }
  }, [dispatch, checkGitHubConnection]);

  // Save installation
  const [saveInstallationMutation] = useSaveGitHubInstallationMutation();

  const saveInstallation = useCallback(async (installationId: string, code: string) => {
    try {
      const result = await saveInstallationMutation({ installationId, code }).unwrap();

      if (result.success) {
        toast({
          title: "GitHub Connected",
          description: "Successfully connected to GitHub!",
        });

        // Refresh connection status
        await checkGitHubConnection();
        return true;
      }

      throw new Error(result.error || 'Failed to save installation');
    } catch (error: any) {
      console.error('Error saving GitHub installation:', error);
      const errorMessage = error.message || 'Failed to save GitHub installation';

      dispatch(setConnectionError(errorMessage));
      toast({
        title: "GitHub Connection Failed",
        description: errorMessage,
        variant: "destructive",
      });

      return false;
    }
  }, [saveInstallationMutation, toast, dispatch, checkGitHubConnection]);

  // Update installations when RTK Query data changes
  useEffect(() => {
    if (installations.length > 0) {
      dispatch(setInstallations(installations));
    }
  }, [installations, dispatch]);

  // Handle user details updates
  useEffect(() => {
    if (userDetails) {
      dispatch(setConnected(userDetails.github.authorized));
      dispatch(setPrimaryAccount(userDetails.github.account_name));
    }
  }, [userDetails, dispatch]);

  // Handle errors
  useEffect(() => {
    if (userDetailsError) {
      dispatch(setConnectionError('Failed to fetch user details'));
    }
  }, [userDetailsError, dispatch]);

  return {
    // State
    isConnected: githubState.isConnected,
    isConnecting: githubState.isConnecting,
    primaryAccount: githubState.primaryAccount,
    installations: githubState.installations,
    connectionError: githubState.connectionError,
    authWindowOpen: githubState.authWindowOpen,

    // Loading states from RTK Query
    isLoadingUserDetails,
    isLoadingInstallations,

    // Actions
    checkGitHubConnection,
    authenticate,
    redirectToGitHubInstallation,
    saveInstallation,

    // For backward compatibility
    setIsConnected: (connected: boolean) => dispatch(setConnected(connected)),
    setIsConnecting: (connecting: boolean) => dispatch(setConnecting(connecting)),
  };
}

// Hook for GitHub repositories with caching
export function useGitHubRepositories(accountLogin?: string) {
  const {
    data: repositories = [],
    error,
    isLoading,
    refetch,
  } = useGetGitHubRepositoriesQuery(accountLogin, {
    skip: !accountLogin,
  });

  return {
    repositories,
    error,
    isLoading,
    refetch,
  };
}

// Hook for GitHub branches with caching
export function useGitHubBranches(accountLogin?: string, repoName?: string) {
  const {
    data: branches = [],
    error,
    isLoading,
    refetch,
  } = useGetGitHubBranchesQuery(
    { accountLogin: accountLogin!, repoName: repoName! },
    {
      skip: !accountLogin || !repoName,
    }
  );

  return {
    branches,
    error,
    isLoading,
    refetch,
  };
}

// Hook for GitHub operations (create repo, push, etc.)
export function useGitHubOperations() {
  const { toast } = useToast();

  const [createRepository, { isLoading: isCreatingRepo }] = useCreateGitHubRepositoryMutation();
  const [pushToGitHub, { isLoading: isPushing }] = usePushToGitHubMutation();

  const createGitHubRepository = useCallback(async (data: {
    installation_id: string;
    org?: string;
    name: string;
  }) => {
    try {
      const result = await createRepository(data).unwrap();

      toast({
        title: "Repository Created",
        description: `Successfully created repository: ${data.name}`,
      });

      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to create repository';

      toast({
        title: "Repository Creation Failed",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  }, [createRepository, toast]);

  const pushToGitHubRepository = useCallback(async (jobId: string, data: {
    account_login: string;
    repo_name: string;
    branch_name: string;
    is_new_repo?: boolean;
    force?: boolean;
  }) => {
    try {
      const result = await pushToGitHub({ jobId, data }).unwrap();

      toast({
        title: "Push Successful",
        description: `Successfully pushed to ${data.repo_name}`,
      });

      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to push to GitHub';

      toast({
        title: "Push Failed",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  }, [pushToGitHub, toast]);

  return {
    createGitHubRepository,
    pushToGitHubRepository,
    isCreatingRepo,
    isPushing,
  };
}

// Enhanced GitHub Push hook that replaces useGitHubPush
export function useGitHubPush() {
  const { installations, isLoadingInstallations } = useGitHub();
  const { pushToGitHubRepository, isPushing } = useGitHubOperations();

  // Local state for UI
  const [selectedInstallation, setSelectedInstallation] = useState('');
  const [selectedRepository, setSelectedRepository] = useState('');
  const [isCreatingNewRepo, setIsCreatingNewRepo] = useState(false);
  const [newRepoName, setNewRepoName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Get repositories for selected installation
  const { repositories, isLoading: isLoadingRepositories } = useGitHubRepositories(selectedInstallation);

  // Reset state
  const reset = useCallback(() => {
    setSelectedInstallation('');
    setSelectedRepository('');
    setIsCreatingNewRepo(false);
    setNewRepoName('');
    setError(null);
    setSuccess(false);
  }, []);

  // Push to GitHub with all the logic from original hook
  const pushToGitHub = useCallback(async (params: {
    jobId: string;
    accountLogin: string;
    repoName: string;
    branchName: string;
    isNewRepo?: boolean;
    force?: boolean;
  }) => {
    try {
      setError(null);
      setSuccess(false);

      await pushToGitHubRepository(params.jobId, {
        account_login: params.accountLogin,
        repo_name: params.repoName,
        branch_name: params.branchName,
        is_new_repo: params.isNewRepo,
        force: params.force,
      });

      setSuccess(true);
      return true;
    } catch (error: any) {
      setError(error.message || 'Push failed');
      return false;
    }
  }, [pushToGitHubRepository]);

  return {
    // State
    isPushing,
    error,
    success,
    installations,
    repositories,
    selectedInstallation,
    selectedRepository,
    isLoadingInstallations,
    isLoadingRepositories,
    isCreatingNewRepo,
    newRepoName,

    // Actions
    fetchInstallations: () => {}, // No-op since RTK Query handles this
    fetchRepositories: () => {}, // No-op since RTK Query handles this
    setSelectedInstallation,
    setSelectedRepository,
    setIsCreatingNewRepo,
    setNewRepoName,
    pushToGitHub,
    reset,
  };
}
