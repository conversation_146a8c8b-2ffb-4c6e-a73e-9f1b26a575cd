import { useCallback } from 'react';
import { useGetBudgetQuery, useUpdateBudgetMutation } from '@/store/api/apiSlice';
import { useToast } from './use-toast';

interface BudgetInfo {
  max_budget: number;
  current_usage: number;
}

interface UseBudgetOptions {
  jobId?: string;
  initialFetch?: boolean;
}

interface UseBudgetReturn {
  budgetInfo: BudgetInfo | null;
  isLoading: boolean;
  error: string | null;
  fetchBudgetInfo: () => Promise<BudgetInfo | undefined>;
  updateBudget: (amount: number, showFeedback?: boolean) => Promise<BudgetInfo | undefined>;
}

/**
 * Custom hook for managing budget information for a job using RTK Query
 *
 * @param options Configuration options for the hook
 * @returns Budget information and functions to fetch and update budget
 */
export function useBudget({ jobId, initialFetch = true }: UseBudgetOptions = {}): UseBudgetReturn {
  const { toast } = useToast();

  // RTK Query hooks
  const {
    data: budgetInfo,
    isLoading: isFetching,
    error: fetchError,
    refetch
  } = useGetBudgetQuery(jobId!, {
    skip: !jobId || !initialFetch,
  });

  const [updateBudgetMutation, {
    isLoading: isUpdating,
    error: updateError
  }] = useUpdateBudgetMutation();

  // Combine loading states
  const isLoading = isFetching || isUpdating;

  // Combine errors
  const error = fetchError ?
    ('error' in fetchError ? fetchError.error as string : 'Failed to fetch budget information') :
    updateError ?
    ('error' in updateError ? updateError.error as string : 'Failed to update budget') :
    null;

  /**
   * Fetches the current budget information for the job
   */
  const fetchBudgetInfo = useCallback(async (): Promise<BudgetInfo | undefined> => {
    if (!jobId) {
      return undefined;
    }

    try {
      const result = await refetch();
      if (result.data) {
        return result.data;
      }
      return undefined;
    } catch (error) {
      console.error("Error fetching budget info:", error);
      return undefined;
    }
  }, [jobId, refetch]);

  /**
   * Updates the budget for the job
   *
   * @param amount The new budget amount
   * @param showFeedback Whether to show success/error feedback
   */
  const updateBudget = useCallback(async (amount: number, showFeedback: boolean = true): Promise<BudgetInfo | undefined> => {
    if (!jobId) {
      return undefined;
    }

    try {
      const result = await updateBudgetMutation({ jobId, amount }).unwrap();

      if (showFeedback) {
        toast({
          title: "Budget Updated",
          description: `Successfully increased budget to ${result.max_budget.toFixed(3)} Credits`,
        });
      }

      return result;
    } catch (error: any) {
      console.error("Error updating budget:", error);

      const errorMessage = error?.error || error?.data?.error || error?.message || "Failed to update budget";

      if (showFeedback) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }

      return undefined;
    }
  }, [jobId, updateBudgetMutation, toast]);

  return {
    budgetInfo: budgetInfo || null,
    isLoading,
    error,
    fetchBudgetInfo,
    updateBudget
  };
}
