import { AppDispatch as MainAppDispatch, RootState as MainRootState } from '@/store';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Typed hooks for the main Redux store
export type AppDispatch = MainAppDispatch;
export type RootState = MainRootState;

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;