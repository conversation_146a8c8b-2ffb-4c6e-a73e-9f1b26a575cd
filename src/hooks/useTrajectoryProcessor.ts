import { useCallback } from 'react';
import { TrajectoryItem } from '@/types/trajectory';
import { AgentMessage,ResponseImageData, UIMessage } from '@/types/message';

interface UseTrajectoryProcessorProps {
  task?: string;
  containerId?: string;
  jobId?: string;
  showSubagentPanel?: boolean;
  selectedMessageId?: string;
  selectedSubagent?: string;
  setSelectedSubagentMessages: (messages: AgentMessage['subagent_trajectory']) => void;
  setIsSubagentActive: (active: boolean) => void;
  setAiMessages: (messages: UIMessage[]) => void;
  getCurrentMessages?: () => UIMessage[]; // Add function to get current messages
}

interface ProcessedMessages {
  messages: UIMessage[];
  hasActiveSubagent: boolean;
}

/**
 * Creates the initial user task message
 */
const createInitialTaskMessage = (task: string, firstItemTimestamp?: string, base64_image_list?: ResponseImageData[]): UIMessage => ({
  id: `user-task`,
  role: 'user',
  content: task,
  timestamp: firstItemTimestamp || new Date().toISOString(),
  base64_image_list: base64_image_list || []
});

/**
 * Creates a user message from a trajectory item
 */
const createUserMessage = (item: TrajectoryItem): UIMessage => ({
  id: `user-${item.step_num}`,
  role: 'user',
  content: item.human_message || '',
  timestamp: item.timestamp,
  base64_image_list: item.base64_image_list,
  warning_for_token_limit: false,
  current_token_count: 0,
  max_token_count: 200000,
});

/**
 * Creates an assistant message from a trajectory item
 */
const createAssistantMessage = (item: TrajectoryItem, containerId?: string): UIMessage => ({
  id: `message-${item.step_num}`,
  role: 'assistant',
  content: item.thought,
  // @ts-ignore
  action: item.action,
  observation: item.observation,
  env_success: item.env_success,
  function_name: item.function_name,
  agent_name: item.agent_name || "",
  timestamp: item.timestamp,
  step_number: item.step_num,
  containerId: containerId,
  error: item?.error || false,
  error_message: item?.error_message || '',
  error_ts: item.error_ts || '',
  expertise_type: item.expertise_type,
  request_id: item.request_id,
  acc_cost: item.acc_cost,
  max_budget: item.max_bugdet || item.max_budget,
  artifacts: item.artifacts,
  // base64_image_list: item.base64_image_list || [],
  enable_rollback: item.enable_rollback,
  warning_for_token_limit: item.traj_payload?.warning_for_token_limit || false,
  current_token_count: item.traj_payload?.current_token_count || 0,
  max_token_count: item.traj_payload?.max_token_count || 200000,
  switching_to_build_mode: item.traj_payload?.switching_to_build_mode || false,
  forked_job_id: item.traj_payload?.forked_job_id || null,
  fork_status: item.fork_status || null,
});

/**
 * Creates a subagent user message from a trajectory item
 */
const createSubagentUserMessage = (item: TrajectoryItem, jobId?: string, containerId?: string): AgentMessage => ({
  id: `subagent-user-${item.step_num}`,
  role: 'user',
  content: item.human_message || '',
  timestamp: item.timestamp,
  step_number: item.step_num,
  job_id: jobId || null,
  container_id: containerId,
  error: false,
  error_message: '',
  action: undefined,
  observation: undefined,
  env_success: undefined,
  function_name: undefined,
  // @ts-ignore
  agent_name: undefined,
  base64_image_list: item.base64_image_list
});

/**
 * Creates a subagent assistant message from a trajectory item
 */
const createSubagentAssistantMessage = (item: TrajectoryItem, jobId?: string): AgentMessage => ({
  id: `subagent-${item.step_num}`,
  role: 'assistant',
  content: item.thought,
  action: item.action || '',
  observation: item.observation || '',
  env_success: item.env_success,
  function_name: item.function_name,
  // @ts-ignore
  agent_name: item.agent_name,
  timestamp: item.timestamp,
  step_number: item.step_num,
  job_id: jobId || null,
  error: item?.error || false,
  request_id: item.request_id,
  error_message: item?.error_message || '',
  expertise_type: item.expertise_type,
  // Only include base64_image_list if there's no human_message (meaning images are from agent, not user)
  base64_image_list: item.human_message ? [] : (item.base64_image_list || []),
  switching_to_build_mode: item.traj_payload?.switching_to_build_mode || false,
});

/**
 * Determines if a subagent is active based on the trajectory
 */
const determineSubagentStatus = (
  trajectoryItems: TrajectoryItem[],
  currentAgentName: string,
  mainAgentName: string,
  currentIndex: number
): boolean => {
  // If this is a finish action, mark subagent as not active
  if (currentAgentName !== mainAgentName && trajectoryItems[currentIndex].action === 'finish') {
    return false;
  }
  
  // If the subagent has an action but no observation, it's still running
  if (
    currentAgentName !== mainAgentName && 
    trajectoryItems[currentIndex].action && 
    !trajectoryItems[currentIndex].observation
  ) {
    return true;
  }
  
  // If this is the last message from this subagent and not a finish action
  const isLastMessageFromThisSubagent = !trajectoryItems.slice(currentIndex + 1).some(
    nextItem => nextItem.agent_name === currentAgentName
  );
  
  if (currentAgentName !== mainAgentName && isLastMessageFromThisSubagent) {
    return true;
  }
  
  return false;
};

/**
 * Hook for processing trajectory data into UI messages
 */
export const useTrajectoryProcessor = ({
  task,
  containerId,
  jobId,
  showSubagentPanel,
  selectedMessageId,
  selectedSubagent,
  setSelectedSubagentMessages,
  setIsSubagentActive,
  setAiMessages,
  getCurrentMessages
}: UseTrajectoryProcessorProps) => {
  
  /**
   * Process trajectory data into UI messages
   */
  const processTrajectory = useCallback((trajectoryItems: TrajectoryItem[], lastPendingMessage: any): void => {
    // if (!trajectoryItems || trajectoryItems.length === 0) {
    //   //console.log('Skipping trajectory processing - empty trajectory');
    //   return;
    // }
    const { messages, hasActiveSubagent } = processTrajectoryItems(trajectoryItems);

    // After processing all messages, update the selectedSubagentMessages if needed
    if (showSubagentPanel && selectedMessageId) {
      const selectedMessage = messages.find(
        (msg) => msg.id === selectedMessageId
      );
      if (
        selectedMessage &&
        selectedMessage.subagent_trajectory &&
        selectedMessage.subagent_trajectory.length > 0
      ) {
        // Only update if we're viewing the same agent and there are new messages
        if (selectedMessage.agent_name === selectedSubagent) {
          setSelectedSubagentMessages(selectedMessage.subagent_trajectory);
        }
      }
    }

    // Check if we need to update messages by comparing with current state
    const finalMessages = lastPendingMessage ? [...messages, lastPendingMessage] : messages;
    if (lastPendingMessage) {
      setAiMessages(finalMessages);
    } else {
      setAiMessages(messages);
    }

    setIsSubagentActive(hasActiveSubagent);
  }, [
    task,
    containerId,
    jobId,
    showSubagentPanel,
    selectedMessageId,
    selectedSubagent,
    setSelectedSubagentMessages,
    setIsSubagentActive,
    setAiMessages,
    getCurrentMessages,
  ]);

  /**
   * Merges artifact_shared messages with the next human message
   */
  const mergeArtifactSharedWithNextMessage = (messages: UIMessage[]): UIMessage[] => {
    const processedMessages: UIMessage[] = [];
    let i = 0;

    while (i < messages.length) {
      const currentMessage = messages[i];

      // Check if current message is artifact_shared
      if ((currentMessage as any).function_name === "artifact_shared") {
        console.log('Merging artifact_shared message with next human message', currentMessage);

        // Look for the next human message
        let nextHumanMessageIndex = -1;
        for (let j = i + 1; j < messages.length; j++) {
          if (messages[j].role === "user") {
            nextHumanMessageIndex = j;
            break;
          }
        }

        if (nextHumanMessageIndex !== -1) {
          // Merge artifact_shared data with the next human message
          const nextHumanMessage = messages[nextHumanMessageIndex];
          const mergedMessage = {
            ...nextHumanMessage,
            // Add artifact data from artifact_shared message
            artifacts: (currentMessage as any).artifacts || (nextHumanMessage as any).artifacts,
            // Prefer artifact URLs over base64 for better quality
            base64_image_list: nextHumanMessage.base64_image_list || currentMessage.base64_image_list,
            // Add artifact_shared metadata for fallback logic
            artifact_shared_data: {
              artifacts: (currentMessage as any).artifacts,
              base64_image_list: currentMessage.base64_image_list,
              content: currentMessage.content,
              agent_name: (currentMessage as any).agent_name
            }
          };

          // Add all messages between current and next human message (excluding artifact_shared)
          for (let k = i + 1; k < nextHumanMessageIndex; k++) {
            processedMessages.push(messages[k]);
          }

          // Add the merged message
          processedMessages.push(mergedMessage);

          // Skip to after the next human message
          i = nextHumanMessageIndex + 1;
        } else {
          // No next human message found, skip artifact_shared message entirely
          // since we've processed its data and don't need to display it
          i++;
        }
      } else {
        // Regular message, add as is
        processedMessages.push(currentMessage);
        i++;
      }
    }

    return processedMessages;
  };

  /**
   * Process trajectory items into UI messages and determine subagent status
   */
  const processTrajectoryItems = useCallback((trajectoryItems: TrajectoryItem[]): ProcessedMessages => {
    const processedMessages: UIMessage[] = [];
    let currentSubagentMessages: AgentMessage[] = [];
    let lastAgentName: string | null = null;
    let hasActiveSubagent = false;
    let lastAssistantMessageIndex = -1;

    // Add the initial task message, preserving original images if they exist
    if (task) {
      // Try to get the original initial message to preserve its images
      const currentMessages = getCurrentMessages?.() || [];
      const originalInitialMessage = currentMessages.find(msg => msg.id === 'user-task');

      // Check if the first trajectory item is an artifact_share action
      const firstItem = trajectoryItems[0];
      const isFirstItemArtifactShare = firstItem?.traj_payload?.action === "artifact_share";

      // Use original images if they exist, otherwise fall back to trajectory images
      let imagesToUse = originalInitialMessage?.base64_image_list?.length
        ? originalInitialMessage.base64_image_list
        : firstItem?.base64_image_list;

      // Create the initial task message
      let initialMessage = createInitialTaskMessage(
        task,
        firstItem?.timestamp,
        imagesToUse
      );

      // If the first trajectory item is artifact_share, merge its artifacts with the initial message
      if (isFirstItemArtifactShare && firstItem?.traj_payload?.artifacts) {
        console.log('Merging first artifact_share action with initial task message', firstItem.traj_payload);

        initialMessage = {
          ...initialMessage,
          // Add artifacts from the artifact_share action
          artifacts: firstItem.traj_payload.artifacts,
          // Add artifact_shared metadata for fallback logic
          artifact_shared_data: {
            artifacts: firstItem.traj_payload.artifacts,
            base64_image_list: firstItem.base64_image_list || [],
            content: firstItem.thought || "USER SHARED ASSETS WITH AGENT",
            agent_name: firstItem.agent_name || undefined
          }
        };
      }

      processedMessages.push(initialMessage);
    }

    trajectoryItems.forEach((item: TrajectoryItem, index: number) => {
      // Skip the first item if it's an artifact_share action since we've already merged it with the initial message
      if (index === 0 && item.traj_payload?.action === "artifact_share") {
        return;
      }

      const currentAgentName = item.agent_name || 'EmergentAssistant';
      const mainAgentName = trajectoryItems[0].agent_name || 'EmergentAssistant';
      const previousAgentName = lastAgentName || 'EmergentAssistant';

      // Check if this is a subagent message and update active status
      if (currentAgentName !== mainAgentName) {
        hasActiveSubagent = determineSubagentStatus(
          trajectoryItems,
          currentAgentName,
          mainAgentName,
          index
        ) || hasActiveSubagent;
      }

      // Add human message if present and it's part of main agent conversation
      if (item.human_message && currentAgentName === mainAgentName) {
        const userMessage = createUserMessage(item);
        processedMessages.push(userMessage);
      }

      // Handle subagent messages
      if (lastAgentName !== null && currentAgentName !== previousAgentName) {
        // Agent name changed, attach accumulated subagent messages to the last assistant message
        if (currentSubagentMessages.length > 0 && lastAssistantMessageIndex >= 0) {
          const lastAssistantMessage = processedMessages[lastAssistantMessageIndex];
          lastAssistantMessage.subagent_trajectory = [...currentSubagentMessages];
          currentSubagentMessages = [];
        }
      }

      // If this is a subagent message (different from main agent), add to current subagent messages
      if (currentAgentName !== mainAgentName) {
        // Add human message to subagent trajectory if present
        if (item.human_message) {
          currentSubagentMessages.push(createSubagentUserMessage(item, jobId, containerId));
        }

        currentSubagentMessages.push(createSubagentAssistantMessage(item, jobId));
      } else {
        // This is a main agent message, add it to processed messages
        const assistantMessage = createAssistantMessage(item, containerId);
        processedMessages.push(assistantMessage);
        lastAssistantMessageIndex = processedMessages.length - 1;
      }

      lastAgentName = currentAgentName;
    });

    // Handle the last set of subagent messages at the end of trajectory
    if (currentSubagentMessages.length > 0 && lastAssistantMessageIndex >= 0) {
      const lastAssistantMessage = processedMessages[lastAssistantMessageIndex];
      lastAssistantMessage.subagent_trajectory = [...currentSubagentMessages];
    }

    // Additional check: if the last message in the trajectory is from a subagent
    if (trajectoryItems.length > 0) {
      const lastItem = trajectoryItems[trajectoryItems.length - 1];
      const mainAgentName = trajectoryItems[0].agent_name || 'EmergentAssistant';

      // If it's a subagent's finish action, mark as not active
      if ((lastItem.agent_name !== mainAgentName && lastItem.action === 'finish') || lastItem.agent_name === mainAgentName) {
        hasActiveSubagent = false;
      }
    }

    // Apply artifact_shared merging to improve image quality display
    const mergedMessages = mergeArtifactSharedWithNextMessage(processedMessages);

    return { messages: mergedMessages, hasActiveSubagent };
  }, [task, containerId, jobId, getCurrentMessages]);

  return { processTrajectory };
};
