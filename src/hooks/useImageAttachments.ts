import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { ImageData } from '@/types/message';
import { useImageCompression } from '@/hooks/useImageCompression';

interface UseImageAttachmentsOptions {
  maxImages?: number;
  maxSizeInMB?: number;
  maxPixelDimensions?: number;
  maxPixelDimensionsMultiple?: number;
  initialImages?: ImageData[];
  onImagesChange?: (images: ImageData[]) => void;
}

/**
 * A hook for handling image attachments with validation and processing
 */
export function useImageAttachments({
  maxImages = 5,
  maxSizeInMB = 5,
  maxPixelDimensions = 8000,
  maxPixelDimensionsMultiple = 2000,
  initialImages = [],
  onImagesChange
}: UseImageAttachmentsOptions = {}) {
  const [images, setImages] = useState<ImageData[]>(initialImages);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Initialize image compression hook
  const { compressImage, getMimeType } = useImageCompression({
    maxWidth: 2000,
    maxHeight: 2000,
    quality: 30, // 10% quality for maximum compression
    format: 'jpeg' // Convert all images to JPEG
  });

  /**
   * Process an image file using compression and convert to base64
   */
  const processImage = useCallback(async (file: File): Promise<ImageData | null> => {
    try {
      console.log('Processing image with compression:', {
        fileName: file.name,
        originalSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
        originalType: file.type
      });

      // Compress the image
      const compressionResult = await compressImage(file);
      if (!compressionResult) {
        console.error('Failed to compress image:', file.name);
        return null;
      }

      console.log(`Compressed ${file.name}:`, {
        originalSize: `${(compressionResult.originalSize / 1024 / 1024).toFixed(2)}MB`,
        compressedSize: `${(compressionResult.compressedSize / 1024 / 1024).toFixed(2)}MB`,
        compressionRatio: `${compressionResult.compressionRatio.toFixed(1)}%`,
        finalFormat: 'JPEG'
      });

      return {
        mime_type: getMimeType('jpeg'), // Always JPEG after compression
        img_base64: compressionResult.base64,
        originalFile: file
      };
    } catch (error) {
      console.error('Failed to process image:', file.name, error);
      return null;
    }
  }, [compressImage, getMimeType]);

  /**
   * Check image dimensions to ensure they don't exceed the maximum allowed
   * Uses different limits for single vs multiple image uploads
   */
  const checkImageDimensions = useCallback(async (file: File, isMultipleUpload: boolean): Promise<{ width: number; height: number; isValid: boolean; maxDimension: number }> => {
    return new Promise((resolve) => {
      const img = new Image();
      const maxDimension = isMultipleUpload ? maxPixelDimensionsMultiple : maxPixelDimensions;

      img.onload = () => {
        const isValid = img.width <= maxDimension && img.height <= maxDimension;
        // Clean up the object URL to prevent memory leaks
        URL.revokeObjectURL(img.src);
        resolve({
          width: img.width,
          height: img.height,
          isValid,
          maxDimension
        });
      };

      img.onerror = () => {
        // Clean up the object URL to prevent memory leaks
        URL.revokeObjectURL(img.src);
        resolve({
          width: 0,
          height: 0,
          isValid: false,
          maxDimension
        });
      };

      img.src = URL.createObjectURL(file);
    });
  }, [maxPixelDimensions, maxPixelDimensionsMultiple]);

  /**
   * Handle image selection from file input or File array
   */
  const handleImageSelect = useCallback(async (files: FileList | File[] | null) => {
    if (!files || files.length === 0) {
      return;
    }

    // Convert FileList or File[] to Array immediately to prevent it from being cleared
    const filesArray = Array.isArray(files) ? files : Array.from(files);

    // Check if adding these files would exceed the max image limit
    if (images.length + filesArray.length > maxImages) {
      toast({
        title: "Too many images",
        description: `You can only upload a maximum of ${maxImages} images`,
        variant: "destructive",
      });
      return;
    }

    // Check file sizes
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    const oversizedFiles = filesArray.filter(file => file.size > maxSizeInBytes);
    if (oversizedFiles.length > 0) {
      toast({
        title: "File too large",
        description: `${oversizedFiles.length > 1 ? 'Some files are' : 'File is'} larger than ${maxSizeInMB}MB limit`,
        variant: "destructive",
      });
      return;
    }

    // Check file types - only allow jpeg, png, gif, and webp
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const invalidTypeFiles = filesArray.filter(file => !allowedTypes.includes(file.type));
    if (invalidTypeFiles.length > 0) {
      toast({
        title: "Invalid file type",
        description: `Only JPEG, PNG, GIF, and WebP images are allowed`,
        variant: "destructive",
      });
      return;
    }

    // Check image dimensions - determine if this is a multiple upload
    const isMultipleUpload = images.length + filesArray.length > 1;
    const dimensionChecks = await Promise.all(
      filesArray.map(file => checkImageDimensions(file, isMultipleUpload))
    );

    const oversizedDimensionFiles = dimensionChecks.filter(check => !check.isValid);
    if (oversizedDimensionFiles.length > 0) {
      // Get the max dimension from the first check (they should all be the same)
      const maxDimension = dimensionChecks[0]?.maxDimension || maxPixelDimensions;
      const uploadType = isMultipleUpload ? 'multiple images' : 'single image';
      toast({
        title: "Image dimensions too large",
        description: `${oversizedDimensionFiles.length > 1 ? 'Some images have' : 'Image has'} dimensions larger than ${maxDimension}x${maxDimension} pixels (limit for ${uploadType})`,
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const imagePromises = filesArray.map(file => processImage(file));
      const processedImages = await Promise.all(imagePromises);

      // Filter out null results and add valid images to the existing selection
      const validImages = processedImages.filter((img): img is ImageData => img !== null);
      const updatedImages = [...images, ...validImages];

      setImages(updatedImages);
      onImagesChange?.(updatedImages);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process images. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  }, [images, maxImages, maxSizeInMB, maxPixelDimensions, onImagesChange, processImage, checkImageDimensions, toast]);

  /**
   * Create and return a file input ref for mobile compatibility
   */
  const createFileInputRef = useCallback(() => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/jpeg,image/png,image/gif,image/webp';
    fileInput.multiple = true;
    fileInput.style.display = 'none';

    fileInput.onchange = (e) => {
      const target = e.target as HTMLInputElement;
      handleImageSelect(target.files);
      // Clean up the input after use
      document.body.removeChild(fileInput);
    };

    return fileInput;
  }, [handleImageSelect]);

  /**
   * Open file picker dialog with mobile compatibility
   */
  const openFilePicker = useCallback(() => {
    const fileInput = createFileInputRef();

    // Append to body temporarily for mobile compatibility
    document.body.appendChild(fileInput);

    // Use setTimeout to ensure the input is properly attached before clicking
    setTimeout(() => {
      fileInput.click();
    }, 0);
  }, [createFileInputRef]);

  /**
   * Remove an image by index
   */
  const removeImage = useCallback((index: number) => {
    const updatedImages = [...images];
    updatedImages.splice(index, 1);
    setImages(updatedImages);
    onImagesChange?.(updatedImages);
  }, [images, onImagesChange]);

  /**
   * Clear all images
   */
  const clearImages = useCallback(() => {
    setImages([]);
    onImagesChange?.([]);
  }, [onImagesChange]);

  return {
    images,
    isProcessing,
    openFilePicker,
    removeImage,
    handleImageSelect,
    clearImages
  };
}
