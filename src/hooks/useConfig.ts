import { useState, useEffect, useRef, useCallback } from 'react';
// import {agent<PERSON>pi} from '@/services/agentApi';
import { fetchGlobalConfig } from '@/services/configService';
import { useAuth } from '@/contexts';

interface Config {
  // Add your config type here
  [key: string]: any;
}

// configStore.ts
class ConfigStore {
  private config: Config | null = null;
  private promise: Promise<Config> | null = null;
  private lastFetch: number = 0;
  private ttl: number = 5 * 60 * 1000; // 5 minutes

  async getConfig(forceRefresh = false): Promise<Config> {
    const now = Date.now();
    const isExpired = now - this.lastFetch > this.ttl;

    if (!forceRefresh && this.config && !isExpired) {
      return this.config;
    }

    // If a request is already in flight, return that promise
    if (this.promise) {
      return this.promise;
    }

    this.promise = fetchGlobalConfig()
      .then(config => {
        this.config = config;
        this.lastFetch = now;
        this.promise = null;
        return config;
      })
      .catch(error => {
        this.promise = null;
        throw error;
      });

    return this.promise;
  }

  clearCache() {
    this.config = null;
    this.lastFetch = 0;
  }
}

const configStore = new ConfigStore();

// Hook
export const useConfig = () => {
  const [data, setData] = useState<Config | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const {user} = useAuth();

  const fetchConfig = useCallback(async (forceRefresh = false) => {
    setLoading(true);
    setError(null);

    if(!user){
      setLoading(false);
      return;
    }

    try {
      const config = await configStore.getConfig(forceRefresh);
      setData(config);
      return config;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!data) {
      fetchConfig();
    }
  }, []);

  return {
    config: data,
    loading,
    error,
    refetch: () => fetchConfig(true),
  };
};