import { useState, useEffect, useCallback, RefObject } from 'react';
import { ITab } from '@/components/TabBar';

/**
 * Custom hook for calculating tab widths with configurable settings for different tab types.
 *
 * @example
 * ```tsx
 * const { tabWidth, showScrollButtons, getTabWidth } = useTabWidth(tabs, tabsContainerRef, {
 *   createAgent: 350,      // Custom width for create agent tabs
 *   createSubagent: 280,   // Custom width for create subagent tabs
 *   regular: {
 *     default: 180,
 *     min: 100,
 *     max: 200,
 *   },
 * });
 * ```
 */

export interface TabWidthConfig {
  /** Width for create agent tabs */
  createAgent: number;
  /** Width for create subagent tabs */
  createSubagent: number;
  /** Configuration for regular tabs */
  regular: {
    /** Default width for regular tabs when there's enough space */
    default: number;
    /** Minimum width for regular tabs when space is constrained */
    min: number;
    /** Maximum width for regular tabs */
    max: number;
  };
  /** Layout configuration for calculating available space */
  layout: {
    /** Width allocated for the home tab */
    homeTabWidth: number;
    /** Width allocated for the plus button */
    plusButtonWidth: number;
    /** Width allocated for scroll buttons when visible */
    scrollButtonsWidth: number;
  };
}

export interface TabWidthResult {
  tabWidth: number;
  showScrollButtons: boolean;
  getTabWidth: (tab: ITab) => number;
}

const DEFAULT_CONFIG: TabWidthConfig = {
  createAgent: 300,
  createSubagent: 300,
  regular: {
    default: 180,
    min: 100,
    max: 180,
  },
  layout: {
    homeTabWidth: 80,
    plusButtonWidth: 40,
    scrollButtonsWidth: 70,
  },
};

export function useTabWidth(
  tabs: ITab[],
  tabsContainerRef: RefObject<HTMLDivElement>,
  config: Partial<TabWidthConfig> = {}
): TabWidthResult {
  const [tabWidth, setTabWidth] = useState(DEFAULT_CONFIG.regular.default);
  const [showScrollButtons, setShowScrollButtons] = useState(false);

  // Merge config with defaults
  const mergedConfig: TabWidthConfig = {
    createAgent: config.createAgent ?? DEFAULT_CONFIG.createAgent,
    createSubagent: config.createSubagent ?? DEFAULT_CONFIG.createSubagent,
    regular: {
      default: config.regular?.default ?? DEFAULT_CONFIG.regular.default,
      min: config.regular?.min ?? DEFAULT_CONFIG.regular.min,
      max: config.regular?.max ?? DEFAULT_CONFIG.regular.max,
    },
    layout: {
      homeTabWidth: config.layout?.homeTabWidth ?? DEFAULT_CONFIG.layout.homeTabWidth,
      plusButtonWidth: config.layout?.plusButtonWidth ?? DEFAULT_CONFIG.layout.plusButtonWidth,
      scrollButtonsWidth: config.layout?.scrollButtonsWidth ?? DEFAULT_CONFIG.layout.scrollButtonsWidth,
    },
  };

  // Helper function to determine tab type
  const getTabType = useCallback((tab: ITab): 'createAgent' | 'createSubagent' | 'regular' => {
    if (tab.title.toLowerCase().includes("create new agent") || tab.path === "/create-agent") {
      return 'createAgent';
    }
    if (tab.title.toLowerCase().includes("create subagent") || tab.path === "/create-subagent") {
      return 'createSubagent';
    }
    return 'regular';
  }, []);

  // Function to get width for a specific tab
  const getTabWidth = useCallback((tab: ITab): number => {
    const tabType = getTabType(tab);
    switch (tabType) {
      case 'createAgent':
        return mergedConfig.createAgent;
      case 'createSubagent':
        return mergedConfig.createSubagent;
      case 'regular':
      default:
        return tabWidth;
    }
  }, [tabWidth, mergedConfig, getTabType]);

  const calculateTabWidth = useCallback(() => {
    if (!tabsContainerRef.current) return;

    const nonHomeTabs = tabs.filter(tab => tab.id !== 'home' && tab.path !== "/not-defined");
    const nonHomeTabCount = nonHomeTabs.length;
    const containerWidth = tabsContainerRef.current.clientWidth;
    const scrollButtonsWidth = showScrollButtons ? mergedConfig.layout.scrollButtonsWidth : 0;
    const availableWidth = containerWidth - mergedConfig.layout.homeTabWidth - mergedConfig.layout.plusButtonWidth - scrollButtonsWidth;

    if (nonHomeTabCount > 0) {
      // Categorize tabs by type
      const createAgentTabs = nonHomeTabs.filter(tab => getTabType(tab) === 'createAgent');
      const createSubagentTabs = nonHomeTabs.filter(tab => getTabType(tab) === 'createSubagent');
      const regularTabs = nonHomeTabs.filter(tab => getTabType(tab) === 'regular');

      // Calculate required width for special tabs
      const createAgentTabsWidth = createAgentTabs.length * mergedConfig.createAgent;
      const createSubagentTabsWidth = createSubagentTabs.length * mergedConfig.createSubagent;
      const specialTabsWidth = createAgentTabsWidth + createSubagentTabsWidth;
      const remainingWidth = availableWidth - specialTabsWidth;

      if (regularTabs.length === 0) {
        // Only special tabs (create agent/subagent)
        setShowScrollButtons(specialTabsWidth > availableWidth);
      } else if (createAgentTabs.length === 0 && createSubagentTabs.length === 0) {
        // Only regular tabs
        if ((mergedConfig.regular.default * regularTabs.length) <= availableWidth) {
          setTabWidth(mergedConfig.regular.default);
          setShowScrollButtons(false);
        } else {
          const calculatedWidth = Math.floor(availableWidth / regularTabs.length);
          const newWidth = Math.max(mergedConfig.regular.min, Math.min(calculatedWidth, mergedConfig.regular.max));
          setTabWidth(newWidth);
          setShowScrollButtons(true);
        }
      } else {
        // Mixed tabs - special tabs get their configured width, regular tabs share remaining space
        if (remainingWidth > 0 && (mergedConfig.regular.default * regularTabs.length) <= remainingWidth) {
          setTabWidth(mergedConfig.regular.default);
          setShowScrollButtons(false);
        } else if (remainingWidth > 0) {
          const calculatedWidth = Math.floor(remainingWidth / regularTabs.length);
          const newWidth = Math.max(mergedConfig.regular.min, Math.min(calculatedWidth, mergedConfig.regular.max));
          setTabWidth(newWidth);
          const needsScroll = remainingWidth < (mergedConfig.regular.min * regularTabs.length) || 
                             specialTabsWidth > (availableWidth - (mergedConfig.regular.min * regularTabs.length));
          setShowScrollButtons(needsScroll);
        } else {
          // Not enough space even for special tabs at their configured width
          setTabWidth(Math.max(mergedConfig.regular.min, mergedConfig.regular.default));
          setShowScrollButtons(true);
        }
      }
    }
  }, [tabs, tabsContainerRef, showScrollButtons, mergedConfig, getTabType]);

  useEffect(() => {
    calculateTabWidth();

    const resizeObserver = new ResizeObserver(() => {
      calculateTabWidth();
    });

    if (tabsContainerRef.current) {
      resizeObserver.observe(tabsContainerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [calculateTabWidth]);

  return {
    tabWidth,
    showScrollButtons,
    getTabWidth,
  };
}
