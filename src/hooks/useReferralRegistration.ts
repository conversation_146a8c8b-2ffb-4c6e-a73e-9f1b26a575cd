import { useAuth } from '@/contexts';
import { ReferralService } from '@/services/referralService';

/**
 * Hook that automatically registers referral codes after user authentication
 * This hook monitors authentication state and registers pending referral codes
 *
 * NOTE: This hook is deprecated and disabled. Referral registration now happens
 * automatically in the user/details API call.
 */
export const useReferralRegistration = () => {
  // Hook is disabled - referral registration now happens in user/details API
  console.log('useReferralRegistration: Hook is deprecated and disabled');

  return {
    // This hook doesn't return anything, it just handles the side effect
    // Could return status if needed in the future
  };
};



/**
 * Hook that checks if a referral was recently applied and should show success modal
 */
export const useReferralSuccess = () => {
  const { user } = useAuth();

  const checkReferralSuccess = (): { 
    shouldShow: boolean; 
    code?: string; 
    reward?: number 
  } => {
    // Only check if user is authenticated
    if (!user) {
      return { shouldShow: false };
    }

    const recentlyApplied = ReferralService.wasReferralRecentlyApplied();
    
    if (recentlyApplied.applied) {
      return {
        shouldShow: true,
        code: recentlyApplied.code,
        reward: recentlyApplied.reward
      };
    }

    return { shouldShow: false };
  };

  const clearReferralSuccess = () => {
    ReferralService.clearAppliedReferralData();
  };

  return {
    checkReferralSuccess,
    clearReferralSuccess
  };
};
