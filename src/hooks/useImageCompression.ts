import { useCallback } from 'react';
import { fromBlob } from 'image-resize-compress';

interface UseImageCompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp' | 'png';
}

interface CompressedImageResult {
  blob: Blob;
  base64: string;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}

/**
 * Hook for compressing images before uploading
 * Compresses images to fit within specified dimensions and quality
 */
export function useImageCompression({
  maxWidth = 2000,
  maxHeight = 2000,
  quality = 30, // 10% quality for maximum compression
  format = 'jpeg'
}: UseImageCompressionOptions = {}) {

  /**
   * Compress a single image file or blob
   */
  const compressImage = useCallback(async (
    input: File | Blob,
    options?: Partial<UseImageCompressionOptions>
  ): Promise<CompressedImageResult | null> => {
    try {
      const finalOptions = {
        maxWidth: options?.maxWidth || maxWidth,
        maxHeight: options?.maxHeight || maxHeight,
        quality: options?.quality || quality,
        format: options?.format || format
      };

      console.log('Compressing image:', {
        originalSize: input.size,
        targetDimensions: `${finalOptions.maxWidth}x${finalOptions.maxHeight}`,
        quality: `${finalOptions.quality}%`,
        format: finalOptions.format
      });

      // Use image-resize-compress to compress the image
      const compressedBlob = await fromBlob(
        input,
        finalOptions.quality,
        finalOptions.maxWidth,
        finalOptions.maxHeight,
        finalOptions.format
      );

      // Convert compressed blob to base64
      const base64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const result = reader.result as string;
          // Remove the data URL prefix to get just the base64 data
          const base64Data = result.split(',')[1];
          resolve(base64Data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(compressedBlob);
      });

      const compressionRatio = ((input.size - compressedBlob.size) / input.size) * 100;

      console.log('Image compression complete:', {
        originalSize: `${(input.size / 1024 / 1024).toFixed(2)}MB`,
        compressedSize: `${(compressedBlob.size / 1024 / 1024).toFixed(2)}MB`,
        compressionRatio: `${compressionRatio.toFixed(1)}%`,
        format: finalOptions.format
      });

      return {
        blob: compressedBlob,
        base64,
        originalSize: input.size,
        compressedSize: compressedBlob.size,
        compressionRatio
      };

    } catch (error) {
      console.error('Image compression failed:', error);
      return null;
    }
  }, [maxWidth, maxHeight, quality, format]);

  /**
   * Compress multiple images in parallel
   */
  const compressImages = useCallback(async (
    inputs: (File | Blob)[],
    options?: Partial<UseImageCompressionOptions>
  ): Promise<(CompressedImageResult | null)[]> => {
    try {
      const compressionPromises = inputs.map(input => compressImage(input, options));
      return await Promise.all(compressionPromises);
    } catch (error) {
      console.error('Batch image compression failed:', error);
      return inputs.map(() => null);
    }
  }, [compressImage]);

  /**
   * Get the MIME type for the specified format
   */
  const getMimeType = useCallback((format: string): string => {
    switch (format) {
      case 'jpeg':
        return 'image/jpeg';
      case 'webp':
        return 'image/webp';
      case 'png':
        return 'image/png';
      default:
        return 'image/jpeg';
    }
  }, []);

  /**
   * Check if compression is needed based on file size and dimensions
   */
  const shouldCompress = useCallback(async (file: File | Blob): Promise<boolean> => {
    // Always compress if file is larger than 1MB
    if (file.size > 1024 * 1024) {
      return true;
    }

    // Check image dimensions
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const needsResize = img.width > maxWidth || img.height > maxHeight;
        URL.revokeObjectURL(img.src);
        resolve(needsResize);
      };
      img.onerror = () => {
        URL.revokeObjectURL(img.src);
        resolve(true); // Compress if we can't determine dimensions
      };
      img.src = URL.createObjectURL(file);
    });
  }, [maxWidth, maxHeight]);

  return {
    compressImage,
    compressImages,
    getMimeType,
    shouldCompress
  };
}
