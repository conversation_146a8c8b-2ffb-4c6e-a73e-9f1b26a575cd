import { useCallback, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import {
  useGetEmergentKeyQuery,
  useLazyGetEmergentKeyQuery,
  useUpdateEmergentKeyConfigMutation,
  useToggleEmergentKeyMutation,
  useRegenerateEmergentKeyMutation,
  useGetUserBudgetQuery,
  useLazyGetUserBudgetQuery,
  useIncreaseBudgetMutation,
  UpdateKeyConfigRequest,
  ToggleKeyRequest,
  IncreaseBudgetRequest,
} from '@/store/api/universalKeyApiSlice';

interface UseUniversalKeyOptions {
  /**
   * Skip initial data fetching. When true, data will only be fetched when explicitly requested.
   * Useful for components that don't need immediate data loading (e.g., modals that aren't open yet)
   */
  skipInitialFetch?: boolean;
}

/**
 * Custom hook for managing Universal Key functionality
 * Provides easy access to all Universal Key API operations with proper error handling
 */
export function useUniversalKey(options: UseUniversalKeyOptions = {}) {
  const { skipInitialFetch = false } = options;
  const { toast } = useToast();

  // State to control when queries should start (for skipInitialFetch mode)
  const [shouldFetch, setShouldFetch] = useState(!skipInitialFetch);

  // Query hooks for fetching data
  const {
    data: emergentKey,
    error: emergentKeyError,
    isLoading: isLoadingEmergentKey,
    refetch: refetchEmergentKey,
  } = useGetEmergentKeyQuery(undefined, {
    skip: !shouldFetch,
  });

  const {
    data: userBudget,
    error: userBudgetError,
    isLoading: isLoadingUserBudget,
    refetch: refetchUserBudget,
  } = useGetUserBudgetQuery(undefined, {
    skip: !shouldFetch,
  });

  // Lazy query hooks for manual fetching
  const [triggerGetEmergentKey] = useLazyGetEmergentKeyQuery();
  const [triggerGetUserBudget] = useLazyGetUserBudgetQuery();

  // Mutation hooks
  const [updateEmergentKeyConfig, { isLoading: isUpdatingConfig }] = useUpdateEmergentKeyConfigMutation();
  const [toggleEmergentKey, { isLoading: isTogglingKey }] = useToggleEmergentKeyMutation();
  const [regenerateEmergentKey, { isLoading: isRegeneratingKey }] = useRegenerateEmergentKeyMutation();
  const [increaseBudget, { isLoading: isIncreasingBudget }] = useIncreaseBudgetMutation();

  // Wrapped mutation functions with error handling and toast notifications
  const handleUpdateKeyConfig = useCallback(async (config: UpdateKeyConfigRequest) => {
    try {
      const result = await updateEmergentKeyConfig(config).unwrap();
      toast({
        title: "Configuration Updated",
        description: "Your Universal Key configuration has been updated successfully.",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error?.data?.message || error?.message || 'Failed to update configuration';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  }, [updateEmergentKeyConfig, toast]);

  const handleToggleKey = useCallback(async (toggleData: ToggleKeyRequest) => {
    try {
      const result = await toggleEmergentKey(toggleData).unwrap();
      toast({
        title: toggleData.activate ? "Key Activated" : "Key Deactivated",
        description: `Your Universal Key has been ${toggleData.activate ? 'activated' : 'deactivated'} successfully.`,
      });
      return result;
    } catch (error: any) {
      const errorMessage = error?.data?.message || error?.message || 'Failed to toggle key';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  }, [toggleEmergentKey, toast]);

  const handleRegenerateKey = useCallback(async () => {
    try {
      const result = await regenerateEmergentKey().unwrap();
      toast({
        title: "Key Regenerated",
        description: "Your Universal Key has been regenerated successfully. Please update your applications with the new key.",
      });
      return result;
    } catch (error: any) {
      const errorMessage = error?.data?.message || error?.message || 'Failed to regenerate key';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  }, [regenerateEmergentKey, toast]);

  const handleIncreaseBudget = useCallback(async (budgetData: IncreaseBudgetRequest) => {
    try {
      const result = await increaseBudget(budgetData).unwrap();

      // Only show toast notification when ECU is actually being transferred
      if (budgetData.ecu > 0) {
        toast({
          title: "Credits Transferred",
          description: `Successfully transferred ${budgetData.ecu.toFixed(2)} ECU to your API key. New balance: ${result.new_budget.toFixed(2)} ECU`,
        });
      }

      return result;
    } catch (error: any) {
      const errorMessage = error?.data?.message || error?.message || 'Failed to increase budget';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  }, [increaseBudget, toast]);

  // Function to start fetching data (for skipInitialFetch mode)
  const startFetching = useCallback(() => {
    setShouldFetch(true);
  }, []);

  // Manual refresh functions
  const refreshEmergentKey = useCallback(async () => {
    try {
      const result = await triggerGetEmergentKey().unwrap();
      return result;
    } catch (error) {
      console.error('Failed to refresh emergent key:', error);
      throw error;
    }
  }, [triggerGetEmergentKey]);

  const refreshUserBudget = useCallback(async () => {
    try {
      const result = await triggerGetUserBudget().unwrap();
      return result;
    } catch (error) {
      console.error('Failed to refresh user budget:', error);
      throw error;
    }
  }, [triggerGetUserBudget]);

  // Enhanced refetch functions that handle skipInitialFetch mode
  const enhancedRefetchEmergentKey = useCallback(async () => {
    if (!shouldFetch) {
      // Start fetching first, then refetch
      setShouldFetch(true);
      // Use lazy query for immediate result
      return await refreshEmergentKey();
    } else {
      return await refetchEmergentKey().unwrap();
    }
  }, [shouldFetch, refreshEmergentKey, refetchEmergentKey]);

  const enhancedRefetchUserBudget = useCallback(async () => {
    if (!shouldFetch) {
      // Start fetching first, then refetch
      setShouldFetch(true);
      // Use lazy query for immediate result
      return await refreshUserBudget();
    } else {
      return await refetchUserBudget().unwrap();
    }
  }, [shouldFetch, refreshUserBudget, refetchUserBudget]);

  return {
    // Data
    emergentKey,
    userBudget,

    // Loading states
    isLoadingEmergentKey,
    isLoadingUserBudget,
    isUpdatingConfig,
    isTogglingKey,
    isRegeneratingKey,
    isIncreasingBudget,

    // Errors
    emergentKeyError,
    userBudgetError,

    // Actions with error handling
    updateKeyConfig: handleUpdateKeyConfig,
    toggleKey: handleToggleKey,
    regenerateKey: handleRegenerateKey,
    increaseBudget: handleIncreaseBudget,

    // Manual refresh functions
    refreshEmergentKey,
    refreshUserBudget,
    refetchEmergentKey: enhancedRefetchEmergentKey,
    refetchUserBudget: enhancedRefetchUserBudget,

    // Control functions
    startFetching,
  };
}
