import { useEffect, useRef, useState, useCallback } from 'react';
import { agentApi } from '@/services/agentApi';
import { useDocumentVisibility } from './useDocumentVisibility';

// Constants moved from polling.worker.ts
const POLLING_INTERVAL = 2000;

interface UsePollingWorkerOptions {
  isCloudFlow?: boolean;
  containerId?: string;
  jobId?: string;
  trajPath?: string;
  isActive: boolean;
  onJobDetailsResult?: (data: any) => void;
  onTrajectoryResult: (data: any) => void;
  onAgentStateResult: (data: any) => void;
  onCurrentChunkResult: (data: any) => void;
  onBrainStormingResult?: (data: any) => void;
  onLogsResult?: (logs: string) => void;
  onError: (error: string) => void;
  onMessage?: (event: any) => void;
}

export function usePollingWorker({
  isCloudFlow,
  containerId,
  jobId,
  trajPath,
  isActive,
  onJobDetailsResult = () => {},
  onTrajectoryResult,
  onAgentStateResult,
  onCurrentChunkResult,
  onBrainStormingResult,
  onLogsResult = () => {},
  onError,
  onMessage = () => {}
}: UsePollingWorkerOptions) {
  const instanceId = useRef(`polling-${Date.now().toString(36)}`);

  const isPollingRef = useRef<boolean>(false);

  const paramsRef = useRef({ containerId, jobId, trajPath });

  const [connectionStatus, setConnectionStatus] = useState<string>('disconnected');
  const [autoReconnect, setAutoReconnect] = useState<boolean>(true);
  const isFirstMount = useRef(true);

  // Check if the browser tab is visible
  const isTabVisible = useDocumentVisibility();

  // Cache for storing previous results
  const cacheRef = useRef({
    jobDetails: null,
    trajectory: [] as any[],
    agentState: null,
    logs: null,
    currentChunk: null,
    lastRequestId:null,
  });

  // Polling interval reference
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Create a function ref to avoid circular dependencies
  const stopPollingRef = useRef<() => void>(() => {
    if (isPollingRef.current) {
      //console.log(`Stopping polling for ${instanceId.current}`);
      isPollingRef.current = false;
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
        intervalRef.current = null;
      }
      setConnectionStatus('disconnected');
    }
  });

  // Update params ref when they change
  useEffect(() => {
    paramsRef.current = { containerId, jobId, trajPath };
  }, [containerId, jobId, trajPath]);

  const poll = useCallback(async () => {
    if (!isPollingRef.current) {
      console.log(`Poll called but polling is not active for ${instanceId.current}`);
      return;
    }

    try {
      console.log(`Polling for ${instanceId.current} with jobId: ${paramsRef.current.jobId}`);
      setConnectionStatus('connecting');
      let response : any = { trajectories: { data: [], updated_data: [], agent_status: false, agent_status_new: "paused", chat_mode: null } };

      // Get trajectory data if we have a job ID
      if (paramsRef.current.jobId) {
        try {
          // Use agentApi.fetchTrajectory with callbacks
          response = await agentApi.fetchTrajectory(
            paramsRef.current.jobId,
            cacheRef.current.lastRequestId || undefined,
            {
              onTrajectoryResult,
              onCurrentChunkResult,
              cache: cacheRef
            }
          );

          if (response.trajectories) {
            let updatedTrajectory = [...cacheRef.current.trajectory];

            // Handle updated items
            if (response.trajectories.updated_data?.length > 0) {
              // Sort updated items by created_at
              const sortedUpdatedItems = response.trajectories.updated_data
                .sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
                .map((item: any) => ({
                  ...item.traj_payload,
                  id: item.id,
                  request_id: item.request_id,
                  job_id: item.job_id,
                  traj_payload: item.traj_payload,
                  commit_id: item.commit_id,
                  parent_request_id: item.parent_request_id,
                  status: item.status,
                  created_at: item.created_at,
                  updated_at: item.updated_at
                }));

              for (const updatedItem of sortedUpdatedItems) {
                // Find trajectory with matching id
                const trajIndex = updatedTrajectory.findIndex(
                  (t: any) => t.id === updatedItem.id
                );

                // If we find a matching trajectory, update it
                if (trajIndex !== -1) {
                  // Update the trajectory
                  updatedTrajectory[trajIndex] = {
                    ...updatedTrajectory[trajIndex],
                    ...updatedItem
                  };
                } else {
                  // If not found, add it as a new item
                  updatedTrajectory.push(updatedItem);
                }
              }
            }

            // Always update the cache and use callbacks directly for consistent data
            cacheRef.current.trajectory = updatedTrajectory;

            onTrajectoryResult(updatedTrajectory);

            const agentState = {
              agent_running: response.trajectories.agent_status,
              job_running: response.trajectories.agent_status,
              agent_status_new: response.trajectories.agent_status_new,
              chat_mode: response.trajectories.chat_mode
            };

            const chatModeState = {
              chat_mode: response.trajectories.chat_mode || null
            }

            onBrainStormingResult && onBrainStormingResult(chatModeState);
            onAgentStateResult(agentState);

            // Always update the lastRequestId when we get data
            const allItems: any[] = [
              ...(response.trajectories.data || []),
              ...(response.trajectories.updated_data || [])
            ];

            if (allItems.length > 0) {
              // Find the item with the latest timestamp
              const latestItem = allItems.reduce((latest, current) => {
                const currentTimestamp = new Date(current.created_at).getTime();
                const latestTimestamp = new Date(latest.created_at).getTime();
                return currentTimestamp > latestTimestamp ? current : latest;
              }, allItems[0]);

              const newestRequestId = latestItem.request_id;

              cacheRef.current.lastRequestId = newestRequestId;

              if (newestRequestId !== cacheRef.current.lastRequestId) {
                // setLastRequestId(newestRequestId);
                cacheRef.current.lastRequestId = newestRequestId;
              }
            }
          }
        } catch (error) {
          console.error('[PollingWorker] Error fetching trajectory:', error);
          onError(error instanceof Error ? error.message : String(error));
          setConnectionStatus('error');

          // If auto-reconnect is disabled, stop polling
          if (!autoReconnect) {
            stopPollingRef.current();
          } else {
            setConnectionStatus('reconnecting');
          }
          return;
        }
      }

      // Send heartbeat to indicate connection is still alive
      setConnectionStatus('connected');

    } catch (error) {
      console.error('[PollingWorker] Error during polling:', error);
      onError(error instanceof Error ? error.message : String(error));
      setConnectionStatus('error');

      if (error instanceof Error && error.message === "Container is not running") {
        stopPollingRef.current();
      }
    }

    // Schedule next poll if still active
    if (isPollingRef.current) {
      intervalRef.current = setTimeout(poll, POLLING_INTERVAL);
    }
  }, [
    autoReconnect,
    isCloudFlow,
    // Include all callbacks in the dependency array
    onJobDetailsResult,
    onTrajectoryResult,
    onAgentStateResult,
    onCurrentChunkResult,
    onLogsResult,
    onError,
    onMessage
  ]);

  // Function to start polling - only starts if this is the active tab and browser tab is visible
  const startPolling = useCallback(() => {
    // Guard against already polling, inactive state, or hidden browser tab
    if (isPollingRef.current) {
      console.log(`startPolling called but already polling for ${instanceId.current}`);
      return;
    }

    if (!isActive) {
      console.log(`startPolling called but tab is not active for ${instanceId.current}`);
      return;
    }

    if (!isTabVisible) {
      console.log(`startPolling called but browser tab is not visible for ${instanceId.current}`);
      return;
    }

    // Double check we have valid params
    const hasParams = paramsRef.current.containerId || paramsRef.current.jobId || paramsRef.current.trajPath;
    if (!hasParams) {
      console.log(`startPolling called but no valid params for ${instanceId.current}`);
      return;
    }

    // Set polling state before starting
    console.log(`Starting polling for ${instanceId.current} with jobId: ${paramsRef.current.jobId}`);
    isPollingRef.current = true;
    setConnectionStatus('connecting');

    // Start polling immediately
    poll();
  }, [isActive, isTabVisible, poll]);

  // Update stopPollingRef implementation
  useEffect(() => {
    stopPollingRef.current = () => {
      if (isPollingRef.current) {
        console.log(`Stopping polling for ${instanceId.current} with jobId: ${paramsRef.current.jobId}`);
        isPollingRef.current = false;
        if (intervalRef.current) {
          console.log(`Clearing interval for ${instanceId.current}`);
          clearTimeout(intervalRef.current);
          intervalRef.current = null;
        }
        setConnectionStatus('disconnected');
      } else {
        console.log(`stopPolling called but not polling for ${instanceId.current}`);
      }
    };
  }, []);

  // Function to stop polling - simply calls the ref function
  const stopPolling = useCallback(() => {
    stopPollingRef.current();
  }, []);

  // Function to reset polling
  const resetPolling = useCallback(() => {
    // Reset the cache and lastRequestId
    cacheRef.current.trajectory = [];
    cacheRef.current.lastRequestId = null;

    // If currently polling, restart it
    if (isPollingRef.current) {
      stopPollingRef.current();

      // Only restart if this is the active tab and the browser tab is visible
      if (isActive && isTabVisible) {
        startPolling();
      }
    }
  }, [isActive, isTabVisible, startPolling]);

  // Main function here we are handling the state like when polling will work and when it should stop
  useEffect(() => {
    if (isFirstMount.current) {
      isFirstMount.current = false;

      const hasRequiredParams = paramsRef.current.containerId || paramsRef.current.jobId || paramsRef.current.trajPath;
      if (isActive && isTabVisible && hasRequiredParams && !isPollingRef.current) {
        setTimeout(() => {
          if (isActive && isTabVisible && !isPollingRef.current) {
            startPolling();
          }
        }, 50);
      }
      return;
    }

    // Only poll if both the tab is active in the application AND the browser tab is visible
    if (isActive && isTabVisible) {
      const hasRequiredParams = paramsRef.current.containerId || paramsRef.current.jobId || paramsRef.current.trajPath;
      if (hasRequiredParams && !isPollingRef.current) {
        console.log(`Starting polling for ${instanceId.current} - tab is visible`);
        startPolling();
      }
    } else {
      if (isPollingRef.current) {
        console.log(`Stopping polling for ${instanceId.current} - tab is ${isActive ? 'active' : 'inactive'} and ${isTabVisible ? 'visible' : 'hidden'}`);
        stopPollingRef.current();
      }
    }
  }, [isActive, isTabVisible, startPolling]);

  // Ensure cleanup when the component unmounts
  useEffect(() => {
    return () => {
      if (isPollingRef.current) {
        if (intervalRef.current) {
          clearTimeout(intervalRef.current);
          intervalRef.current = null;
        }
        isPollingRef.current = false;
      }
    };
  }, []);

  return {
    startPolling,
    stopPolling,
    resetPolling,
    connectionStatus,
    autoReconnect,
    setAutoReconnect,
    isTabVisible,
  };
}