import { useCallback } from 'react';
import { useDeployExpoAppMutation } from '@/store/api/apiSlice';
import { useToast } from './use-toast';

interface UsePublishProps {
  jobId?: string;
  onPublishSuccess?: () => void;
}

interface UsePublishReturn {
  isPublishing: boolean;
  publishApp: () => Promise<void>;
}

export function usePublish({ jobId, onPublishSuccess }: UsePublishProps): UsePublishReturn {
  const { toast } = useToast();
  const [deployExpoApp, { isLoading: isPublishing }] = useDeployExpoAppMutation();

  const publishApp = useCallback(async () => {
    if (!jobId) {
      toast({
        title: "Error",
        description: "No job ID available for publishing",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await deployExpoApp(jobId).unwrap();

      toast({
        title: "Success",
        description: "App published successfully!",
        variant: "default",
      });

      console.log("Publish response:", response);

      // Call the success callback to refresh job details
      onPublishSuccess?.();
    } catch (error: any) {
      console.error("Error publishing app:", error);

      const errorMessage = error.error || error.message || "Failed to publish app. Please try again.";
      toast({
        title: "Publish Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }, [jobId, toast, onPublishSuccess, deployExpoApp]);

  return {
    isPublishing,
    publishApp,
  };
}
