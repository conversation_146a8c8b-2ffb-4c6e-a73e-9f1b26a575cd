import { useState, useEffect } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";

// Components
import { But<PERSON> } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import EmergentButton from "@/components/EmergentButton";
import RetroGrid from "@/components/RetroGrid";
import Footer from "@/components/Footer";

// Assets
import BorderedLogoDark from "@/assets/logo/borded-logo-dark.svg";

// Hooks
import { useErrorToast } from "@/components/ui/ErrorToast";
import { useAuth } from "@/contexts";

export default function EmailVerification() {
  // Hooks
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { showErrorToast } = useErrorToast();
  const { resendVerificationEmail, signOut } = useAuth();


  // State
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [userEmail, setUserEmail] = useState("");

  // Set the email from URL query parameter or location state
  useEffect(() => {
    // First try to get email from URL query parameters
    const searchParams = new URLSearchParams(location.search);
    const emailFromUrl = searchParams.get('email');

    if (emailFromUrl) {
      // If email is in URL, use it
      setUserEmail(emailFromUrl);
    } else {
      // Fall back to location state if available
      const state = location.state as { email?: string } | null;
      if (state && state.email) {
        setUserEmail(state.email);
      } else {
        // If no email is available, redirect to login
        navigate("/login");
      }
    }
  }, [location, navigate]);


  // Cooldown timer for resend button
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (resendCooldown > 0) {
      timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [resendCooldown]);

  const handleResendVerification = async () => {
    if (resendCooldown > 0 || !userEmail) return;

    setIsSubmitting(true);
    try {
      const { success, error } = await resendVerificationEmail(userEmail);

      if (!success) {
        throw new Error(error || "Failed to resend verification email");
      }

      // Set cooldown for 60 seconds
      setResendCooldown(60);
    } catch (error: any) {
      console.error("Error resending verification email:", error);
      showErrorToast("Error", error.message || "Failed to resend verification email");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogout = async () => {
    setIsSubmitting(true);
    try {
      await signOut();
    } catch (error) {
      console.error("Error signing out:", error);
      showErrorToast("Error signing out", "Failed to sign out");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="relative flex flex-col items-center justify-center w-full h-screen p-4 Z-[999]">
        <div className="absolute top-4 right-4 z-[999]">
          <Button
            variant="ghost"
            className="text-white/40 hover:text-white hover:bg-[#313133] max-h-[44px] min-w-[100px] flex items-center justify-center transition-colors"
            onClick={handleLogout}
            disabled={isSubmitting}
          >
            <div className="flex items-center justify-center w-full">
              {isSubmitting ? <Spinner className="w-4 h-4 mr-2" /> : null}
              Log out
            </div>
          </Button>
        </div>

        <div className="flex flex-col items-center mb-[3rem] z-[999] ">
          <img
            src={BorderedLogoDark}
            className="w-20 h-20 mb-9"
            alt="Emergent Logo"
          />
          <div className="flex flex-col items-center justify-center font-sans font-medium text-center">
            <span className="text-[28px] md:text-[40px] font-brockmann text-white">
              Verify Your Email
            </span>
            <span className="block text-[#898e98] max-md:text-[13px] text-base mt-3 w-full whitespace-nowrap text-center">
              We've sent a verification link to <span className="font-medium text-white">{userEmail}</span>.
            </span>
            <span className="text-[#898e98] text-base max-md:text-[13px] mt-3 max-w-[414px] w-full">
            Please check your inbox and click the link to continue.
            </span>
          </div>
        </div>

        <div className="p-4 pt-0 space-y-6 max-w-[414px] w-full z-[999]">
            <EmergentButton
                type="button"
                disabled={isSubmitting || resendCooldown > 0}
                onClick={handleResendVerification}
                variant="light"
                className="w-full p-0 max-md:text-[14px]"
              >
                {isSubmitting ? (
                  <>
                    <span>Sending</span>
                    <Spinner className="ml-2" />
                  </>
                ) : resendCooldown > 0 ? (
                  `Resend Email (${resendCooldown}s)`
                ) : (
                  "Resend Verification Email"
                )}
              </EmergentButton>

            <Button
                type="button"
                className={`w-full h-[48px] max-md:text-[14px] p-0 bg-[#18181A] hover:bg-[#313133] border-[#292929] border text-[#CCCCCC] hover:text-white flex items-center justify-center`}
                onClick={() => {
                  const currentParams = searchParams.toString();
                  const redirectUrl = currentParams ? `/login?${currentParams}` : "/login";
                  navigate(redirectUrl);
                }}
                disabled={isSubmitting}
              >
                <div className="flex items-center w-full relative font-medium tracking-[-1%]">
                  {isSubmitting ? (
                    <Spinner className="mx-auto" />
                  ) : (
                    <>
                      {/* <img src={icon} alt={`${provider} icon`} className="absolute w-6 h-6 left-4" /> */}
                      <span className="w-full text-center">Switch Account</span>
                    </>
                  )}
                </div>
              </Button>
        </div>

        <Footer className="bottom-0 z-[998] pointer-events-none" />
        <RetroGrid
          height="50%"
          top="50%"
          gridSizeX={70}
          gridSizeY={30}
          fadeIntensity={90}
          gridLineWidth={0.3}
          gridOpacity={0.6}
          backgroundColor="#131314"
        />
      </div>
    </>
  );
}