import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { FLUSH, PAUSE, PERSIST, persistReducer, persistStore, PURGE, REGISTER, REHYDRATE } from "redux-persist";

import storage from 'redux-persist/lib/storage';

import progressReducer from "./progressSlice";
import containerReducer from "./containerSlice";
import commandQueueReducer from "../features/commandQueue/commandQueueSlice";
import tabsReducer from "./tabSlice";
import configReducer from "./configSlice";
import deployReducer from "./deploySlice";
import githubReducer from "./githubSlice";
import { apiSlice } from "./api/apiSlice";
import { universalKeyApiSlice } from "./api/universalKeyApiSlice";
import { promodeApiSlice } from "./api/promodeApiSlice";

const tabsPersistConfig = {
  key: "tabs",
  storage,
}

const rootReducer = combineReducers({
  progress: progressReducer,
  container: containerReducer,
  commandQueue: commandQueueReducer,
  tabs: persistReducer(tabsPersistConfig, tabsReducer),
  config: configReducer,
  deploy: deployReducer,
  github: githubReducer,
  [apiSlice.reducerPath]: apiSlice.reducer,
  [universalKeyApiSlice.reducerPath]: universalKeyApiSlice.reducer,
  [promodeApiSlice.reducerPath]: promodeApiSlice.reducer,
})


export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
    },
  }).concat(apiSlice.middleware, universalKeyApiSlice.middleware, promodeApiSlice.middleware)
});

export const persistor = persistStore(store)

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
