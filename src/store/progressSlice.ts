import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface TabProgressState {
  isVisible: boolean;
  progress: number;
  title: string;
  description: string;
  error?: string;
  steps: {
    name: string;
    status: 'pending' | 'in_progress' | 'completed';
  }[];
  currentStep: number;
}

interface ProgressState {
  // Tab-based progress tracking: tabId -> TabProgressState
  tabProgress: Record<string, TabProgressState>;
  activeTabId: string | null;
}

const initialTabProgressState: TabProgressState = {
  isVisible: false,
  progress: 0,
  title: "",
  description: "",
  error: undefined,
  steps: [],
  currentStep: -1,
};

const initialState: ProgressState = {
  tabProgress: {},
  activeTabId: null,
};

export const progressSlice = createSlice({
  name: "progress",
  initialState,
  reducers: {
    setActiveTabId: (state, action: PayloadAction<string>) => {
      state.activeTabId = action.payload;
    },
    showProgress: (
      state,
      action: PayloadAction<{
        tabId: string;
        title: string;
        description: string;
        steps: { name: string; status: 'pending' | 'in_progress' | 'completed' }[];
      }>
    ) => {
      const { tabId, title, description, steps } = action.payload;
      if (!state.tabProgress[tabId]) {
        state.tabProgress[tabId] = { ...initialTabProgressState };
      }
      const tabProgress = state.tabProgress[tabId];
      tabProgress.isVisible = true;
      tabProgress.progress = 0;
      tabProgress.title = title;
      tabProgress.description = description;
      tabProgress.error = undefined;
      tabProgress.steps = steps;
      tabProgress.currentStep = steps.findIndex(step => step.status === 'in_progress');
    },
    updateProgress: (state, action: PayloadAction<{ tabId: string; progress: number }>) => {
      const { tabId, progress } = action.payload;
      if (!state.tabProgress[tabId]) {
        state.tabProgress[tabId] = { ...initialTabProgressState };
      }
      const tabProgress = state.tabProgress[tabId];
      tabProgress.progress = progress;

      // If we're at 100%, mark all steps as completed
      if (progress === 100) {
        tabProgress.steps = tabProgress.steps.map(step => ({
          ...step,
          status: 'completed' as const
        }));
        tabProgress.currentStep = tabProgress.steps.length;
      }
    },
    nextStep: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      if (!state.tabProgress[tabId]) {
        state.tabProgress[tabId] = { ...initialTabProgressState };
      }
      const tabProgress = state.tabProgress[tabId];

      if (tabProgress.currentStep >= 0 && tabProgress.currentStep < tabProgress.steps.length) {
        // Mark current step as completed
        tabProgress.steps[tabProgress.currentStep].status = 'completed';
        tabProgress.currentStep++;

        // Update progress based on completed steps
        tabProgress.progress = (tabProgress.currentStep / tabProgress.steps.length) * 100;

        // If there's a next step, mark it as in_progress
        if (tabProgress.currentStep < tabProgress.steps.length) {
          tabProgress.steps[tabProgress.currentStep].status = 'in_progress';
        } else {
          // If all steps are complete, hide the progress
          tabProgress.isVisible = false;
        }
      }
    },
    hideProgress: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      if (!state.tabProgress[tabId]) {
        state.tabProgress[tabId] = { ...initialTabProgressState };
      }
      const tabProgress = state.tabProgress[tabId];

      // Mark all steps as completed before hiding
      tabProgress.steps = tabProgress.steps.map(step => ({
        ...step,
        status: 'completed' as const
      }));
      tabProgress.isVisible = false;
      tabProgress.error = undefined;
      tabProgress.steps = [];
      tabProgress.currentStep = -1;
    },
    setError: (state, action: PayloadAction<{ tabId: string; error: string }>) => {
      const { tabId, error } = action.payload;
      if (!state.tabProgress[tabId]) {
        state.tabProgress[tabId] = { ...initialTabProgressState };
      }
      const tabProgress = state.tabProgress[tabId];

      tabProgress.error = error;
      tabProgress.isVisible = true;
      tabProgress.progress = 100;
      if (tabProgress.currentStep >= 0 && tabProgress.currentStep < tabProgress.steps.length) {
        tabProgress.steps[tabProgress.currentStep].status = 'completed';
      }
    },
  },
});

export const {
  setActiveTabId,
  showProgress,
  updateProgress,
  hideProgress,
  setError,
  nextStep
} = progressSlice.actions;

// Selectors
export const selectTabProgress = (state: any, tabId: string): TabProgressState => {
  return state.progress.tabProgress[tabId] || initialTabProgressState;
};

export const selectActiveTabProgress = (state: any): TabProgressState => {
  const activeTabId = state.progress.activeTabId;
  return activeTabId ? selectTabProgress(state, activeTabId) : initialTabProgressState;
};

export default progressSlice.reducer;
