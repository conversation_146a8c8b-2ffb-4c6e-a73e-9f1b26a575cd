import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase';
import { config } from '@/config';

// Types for Universal Key API responses
export interface EmergentKey {
  user_id: string;
  emergent_key: string;
  key_config: {
    auto_topup: boolean;
    auto_topup_amount: number;
    llm: {
      allowed_providers: string[];
    };
  };
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface UpdateKeyConfigRequest {
  llm_providers: string[];
  auto_topup: boolean;
  auto_topup_ecu: number;
}

export interface ToggleKeyRequest {
  activate: boolean;
}

export interface UserBudget {
  user_id: string;
  max_budget: number;
  spend: number;
  remaining: number;
}

export interface IncreaseBudgetRequest {
  ecu: number;
  auto_topup: boolean;
  auto_topup_ecu: number;
}

export interface IncreaseBudgetResponse {
  transaction_key: string;
  user_id: string;
  amount: number;
  status: string;
  new_budget: number;
}

// Base query with authentication for LLM Router endpoints
const universalKeyBaseQuery = fetchBaseQuery({
  baseUrl: config.apiBaseUrl,
  prepareHeaders: async (headers) => {
    const session = await supabase.auth.getSession();
    if (session.data.session?.access_token) {
      headers.set('Authorization', `Bearer ${session.data.session.access_token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const universalKeyApiSlice = createApi({
  reducerPath: 'universalKeyApi',
  baseQuery: universalKeyBaseQuery,
  tagTypes: ['EmergentKey', 'UserBudget'],
  endpoints: (builder) => ({
    // Fetch emergent key
    getEmergentKey: builder.query<EmergentKey, void>({
      query: () => '/llm-router/v0/emergent-key',
      providesTags: ['EmergentKey'],
      transformResponse: (response: EmergentKey) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to fetch emergent key'
      }),
    }),

    // Update emergent key config
    updateEmergentKeyConfig: builder.mutation<EmergentKey, UpdateKeyConfigRequest>({
      query: (data) => ({
        url: '/llm-router/v0/emergent-key/config',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['EmergentKey'],
      transformResponse: (response: EmergentKey) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to update emergent key config'
      }),
    }),

    // Activate/Deactivate emergent key
    toggleEmergentKey: builder.mutation<EmergentKey, ToggleKeyRequest>({
      query: (data) => ({
        url: '/llm-router/v0/emergent-key/toggle',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['EmergentKey'],
      transformResponse: (response: EmergentKey) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to toggle emergent key'
      }),
    }),

    // Regenerate emergent key
    regenerateEmergentKey: builder.mutation<EmergentKey, void>({
      query: () => ({
        url: '/llm-router/v0/emergent-key/regenerate',
        method: 'POST',
      }),
      invalidatesTags: ['EmergentKey'],
      transformResponse: (response: EmergentKey) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to regenerate emergent key'
      }),
    }),

    // Fetch user budget
    getUserBudget: builder.query<UserBudget, void>({
      query: () => '/llm-router/v0/budget',
      providesTags: ['UserBudget'],
      transformResponse: (response: UserBudget) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to fetch user budget'
      }),
    }),

    // Increase budget
    increaseBudget: builder.mutation<IncreaseBudgetResponse, IncreaseBudgetRequest>({
      query: (data) => ({
        url: '/llm-router/v0/budget/increase',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['UserBudget'],
      transformResponse: (response: IncreaseBudgetResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to increase budget'
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetEmergentKeyQuery,
  useLazyGetEmergentKeyQuery,
  useUpdateEmergentKeyConfigMutation,
  useToggleEmergentKeyMutation,
  useRegenerateEmergentKeyMutation,
  useGetUserBudgetQuery,
  useLazyGetUserBudgetQuery,
  useIncreaseBudgetMutation,
} = universalKeyApiSlice;
