import { ResponseImageData } from "@/types/message";
import {createSlice, PayloadAction} from "@reduxjs/toolkit"
import { RootState } from '../store';
import { PendingArtifact } from '@/types/artifact';

export interface ITab {
    id: string;
    title: string;
    path: string;
    created_by?: string;
    state?: {
      containerId?: string;
      setupInProgress?: boolean;
      copyToApp?: boolean;
      sourceTabId?: string;
      fromTabBar?: boolean;
      projectName?: string;
      projectPath?: string;
      logo_url?: string;
      selectedImage?: string;
      task?: string;
      jobId?: string;
      trajPath?: string;
      [key: string]: any;
    };
  }

  export interface PendingMessage {
    id: string;
    content: string;
    timestamp: string;
    base64_image_list: ResponseImageData[];
    artifacts?: PendingArtifact[]; // Add artifacts to pending message for high-quality display
  }

  export interface CreateAgentFormData {
    agentName: string;
    systemConstraints: string;
    dynamicVariableValues: Record<string, string>;
    selectedTools: string[];
    selectedSubAgents: string[];
    selectedIcon: { id: string; icon: string; name: string };
    currentStep: number;
  }

  export interface CreateSubagentFormData {
    subagentName: string;
    subagentGuidelines: string;
    systemConstraints: string;
    dynamicVariableValues: Record<string, string>;
    selectedTools: string[];
    currentStep: number;
  }

  export interface TabState {
    [key: string]: any;
    isActive?: boolean;
    activeTab?: string;
    pendingMessage?: PendingMessage;
    pendingArtifacts?: PendingArtifact[];
    chatInputText?: string;
    createAgentFormData?: CreateAgentFormData;
    createSubagentFormData?: CreateSubagentFormData;
  }

  export interface TabsState {
    activeTab: string;
    tabs: ITab[];
    isActive: boolean;
    tabStates: {
      [key: string]: TabState;
    };
  }

  const initialState: TabsState = {
    activeTab: 'home',
    tabs: [
        {
            id:"home",
            title:"Home",
            path:"/",
        }

    ],
    isActive: true,
    tabStates: {},
  };

  export const tabSlice = createSlice({
    name: 'tabs',
    initialState,
    reducers:{
        // Control Active Tabs from here
        setActiveTab:(state, action:PayloadAction<string>)=>{
            state.activeTab = action.payload;
            state.isActive = true;

            Object.keys(state.tabStates).forEach((key) => {
                state.tabStates[key] = {
                    ...state.tabStates[key],
                    isActive: key === action.payload,
                }
            })

            if(!state.tabStates[action.payload]){
                state.tabStates[action.payload] = {
                    isActive: true,
                    activeTab: action.payload,
                }
            }
        },
        // Set Tab State
        setTabs: (state, action: PayloadAction<ITab[]>)=>{
            // Sort tabs to ensure '/not-defined' tabs always appear at the end
            const normalTabs = action.payload.filter(tab => tab.path !== "/not-defined");
            const notDefinedTabs = action.payload.filter(tab => tab.path === "/not-defined");

            // Set tabs with '/not-defined' at the end
            state.tabs = [...normalTabs, ...notDefinedTabs];

            // Clean up any tab states that don't have corresponding tabs
            const tabIds = new Set(action.payload.map(tab => tab.id));
            Object.keys(state.tabStates).forEach(tabStateId => {
                if (!tabIds.has(tabStateId) && tabStateId !== 'home') {
                    const tabState = state.tabStates[tabStateId];
                    const hasImportantData = tabState?.createAgentFormData || tabState?.createSubagentFormData;
                    if (!hasImportantData) {
                        delete state.tabStates[tabStateId];
                    }
                }
            });
        },

        updateTabState: (state, action: PayloadAction<{ tabId: string; state: TabState }>) => {
            const {tabId, state: newState} = action.payload;
            const tabExists = tabId in state.tabStates;

            state.tabStates[tabId] = {
                ...(tabExists ? state.tabStates[tabId] : {status: false, isCloudFlow:true}),
                ...newState,
            }
        },

        removeTab: (state, action: PayloadAction<string>) => {
            const tabId = action.payload;

            // Clean up blob URLs before removing tab
            if (state.tabStates[tabId]) {
                const artifacts = state.tabStates[tabId].pendingArtifacts || [];
                artifacts.forEach(artifact => {
                    if (artifact.local_preview_url && artifact.local_preview_url.startsWith('blob:')) {
                        URL.revokeObjectURL(artifact.local_preview_url);
                    }
                });
            }

            state.tabs = state.tabs.filter((tab) => tab.id !== tabId);
            delete state.tabStates[tabId];

            // TODO - Handle the case where the active tab is removed

            if (state.activeTab === tabId) {
                state.activeTab = 'home';
            }

        },

        // Clean up tab states that don't have corresponding tabs
        cleanupTabStates: (state) => {
            const tabIds = new Set(state.tabs.map(tab => tab.id));
            Object.keys(state.tabStates).forEach(tabStateId => {
                if (!tabIds.has(tabStateId) && tabStateId !== 'home') {
                    delete state.tabStates[tabStateId];
                }
            });
        },

        setPendingMessage: (state, action: PayloadAction<{
            tabId: string;
            messageId: string;
            content: string;
            base64_image_list: ResponseImageData[];
            artifacts?: PendingArtifact[];
        }>) => {
            const { tabId, messageId, content, base64_image_list, artifacts } = action.payload;

            if (!state.tabStates[tabId]) {
                state.tabStates[tabId] = {};
            }

            state.tabStates[tabId].pendingMessage = {
                id: messageId,
                content,
                timestamp: new Date().toISOString().replace(/\.\d{3}Z$/, match =>
                    match.replace('Z', '000+00:00')
                  ),
                base64_image_list,
                artifacts,
            };
        },

        clearPendingMessage: (state, action: PayloadAction<{
            tabId: string;
            content?: string;
        }>) => {
            const { tabId, content } = action.payload;

            if (state.tabStates[tabId]) {
                if (content) {
                    const pendingMessage = state.tabStates[tabId]?.pendingMessage;
                    if (!pendingMessage || pendingMessage.content !== content) {
                        // If the content doesn't match, don't clear the message
                        return;
                    }
                }
                state.tabStates[tabId] ={
                    ...state.tabStates[tabId],
                    pendingMessage: undefined,
                };
            }
        },

        removePendingMessage: (state, action: PayloadAction<{
            tabId: string;
            messageId?: string;
            content?: string;
        }>) => {
            const { tabId, messageId, content } = action.payload;

            if (state.tabStates[tabId]) {
                const pendingMessage = state.tabStates[tabId]?.pendingMessage;
                if (pendingMessage && pendingMessage.content === content) {
                    state.tabStates[tabId] = {
                        ...state.tabStates[tabId],
                        pendingMessage: undefined,
                    };
                }
            }
        },

        // Pending artifacts management
        addPendingArtifacts: (state, action: PayloadAction<{
            tabId: string;
            artifacts: PendingArtifact[];
        }>) => {
            const { tabId, artifacts } = action.payload;

            if (!state.tabStates[tabId]) {
                state.tabStates[tabId] = {};
            }

            const existingArtifacts = state.tabStates[tabId].pendingArtifacts || [];
            state.tabStates[tabId].pendingArtifacts = [...existingArtifacts, ...artifacts];
        },

        clearPendingArtifacts: (state, action: PayloadAction<{
            tabId: string;
        }>) => {
            const { tabId } = action.payload;

            if (state.tabStates[tabId]) {
                // Clean up blob URLs before clearing artifacts
                const artifacts = state.tabStates[tabId].pendingArtifacts || [];
                artifacts.forEach(artifact => {
                    if (artifact.local_preview_url && artifact.local_preview_url.startsWith('blob:')) {
                        URL.revokeObjectURL(artifact.local_preview_url);
                    }
                });

                state.tabStates[tabId].pendingArtifacts = [];
            }
        },

        removePendingArtifact: (state, action: PayloadAction<{
            tabId: string;
            artifactId: string;
        }>) => {
            const { tabId, artifactId } = action.payload;

            if (state.tabStates[tabId]?.pendingArtifacts) {
                // Find the artifact to remove and clean up its blob URL
                const artifactToRemove = state.tabStates[tabId].pendingArtifacts!.find(
                    artifact => artifact.artifact_id === artifactId
                );

                if (artifactToRemove?.local_preview_url && artifactToRemove.local_preview_url.startsWith('blob:')) {
                    URL.revokeObjectURL(artifactToRemove.local_preview_url);
                }

                state.tabStates[tabId].pendingArtifacts = state.tabStates[tabId].pendingArtifacts!.filter(
                    artifact => artifact.artifact_id !== artifactId
                );
            }
        },

        // Chat input text management
        setChatInputText: (state, action: PayloadAction<{
            tabId: string;
            text: string;
        }>) => {
            const { tabId, text } = action.payload;

            if (!state.tabStates[tabId]) {
                state.tabStates[tabId] = {};
            }

            state.tabStates[tabId].chatInputText = text;
        },

        clearChatInputText: (state, action: PayloadAction<{
            tabId: string;
        }>) => {
            const { tabId } = action.payload;

            if (state.tabStates[tabId]) {
                state.tabStates[tabId].chatInputText = "";
            }
        },

        // Create Agent Form Data Management
        setCreateAgentFormData: (state, action: PayloadAction<{
            tabId: string;
            formData: CreateAgentFormData;
        }>) => {
            const { tabId, formData } = action.payload;

            if (!state.tabStates[tabId]) {
                state.tabStates[tabId] = {};
            }

            state.tabStates[tabId].createAgentFormData = formData;
        },

        clearCreateAgentFormData: (state, action: PayloadAction<{
            tabId: string;
        }>) => {
            const { tabId } = action.payload;

            if (state.tabStates[tabId]) {
                delete state.tabStates[tabId].createAgentFormData;
            }
        },

        // Create Subagent Form Data Management
        setCreateSubagentFormData: (state, action: PayloadAction<{
            tabId: string;
            formData: CreateSubagentFormData;
        }>) => {
            const { tabId, formData } = action.payload;

            if (!state.tabStates[tabId]) {
                state.tabStates[tabId] = {};
            }

            state.tabStates[tabId].createSubagentFormData = formData;
        },

        clearCreateSubagentFormData: (state, action: PayloadAction<{
            tabId: string;
        }>) => {
            const { tabId } = action.payload;

            if (state.tabStates[tabId]) {
                delete state.tabStates[tabId].createSubagentFormData;
            }
        }
    }
  });

  export const {
    setActiveTab,
    setTabs,
    updateTabState,
    removeTab,
    setPendingMessage,
    clearPendingMessage,
    removePendingMessage,
    cleanupTabStates,
    addPendingArtifacts,
    clearPendingArtifacts,
    removePendingArtifact,
    setChatInputText,
    clearChatInputText,
    setCreateAgentFormData,
    clearCreateAgentFormData,
    setCreateSubagentFormData,
    clearCreateSubagentFormData
  } = tabSlice.actions;




export const selectActiveTab = (state: RootState): string => state.tabs.activeTab;
export const selectTabs = (state: RootState): ITab[] => state.tabs.tabs;
export const selectIsActive = (state: RootState): boolean => state.tabs.isActive;
export const selectTabState = (state: RootState, tabId: string): TabState =>
  state.tabs.tabStates[tabId] || {};
export const selectPendingMessage = (state: RootState, tabId: string): PendingMessage | undefined =>
  state.tabs.tabStates[tabId]?.pendingMessage;
export const selectPendingArtifacts = (state: RootState, tabId: string): PendingArtifact[] =>
  state.tabs.tabStates[tabId]?.pendingArtifacts || [];
export const selectChatInputText = (state: RootState, tabId: string): string =>
  state.tabs.tabStates[tabId]?.chatInputText || "";
export const selectActiveTabData = (state: RootState): ITab => {
  const activeTabId = state.tabs.activeTab;
  // If activeTabId is not found in tabs, default to home tab
  const activeTab = state.tabs.tabs.find(tab => tab.id === activeTabId);

  // If no active tab is found, or if active tab ID doesn't exist in tabs, default to home
  if (!activeTab || !activeTabId) {
    // Find home tab in tabs
    const homeTab = state.tabs.tabs.find(tab => tab.id === 'home');

    // If home tab exists, return it
    if (homeTab) {
      // Update the active tab in the state to 'home'
      // This doesn't actually mutate state since this is a selector
      // The component using this selector needs to dispatch setActiveTab('home')
      return homeTab;
    }
  }

  return activeTab || state.tabs.tabs[0];
};
export const selectTabByJobId = (state: RootState, jobId: string): ITab | undefined => {
  return state.tabs.tabs.find(tab => tab.state?.jobId === jobId);
};
export const selectCreateAgentFormData = (state: RootState, tabId: string): CreateAgentFormData | undefined =>
  state.tabs.tabStates[tabId]?.createAgentFormData;
export const selectCreateSubagentFormData = (state: RootState, tabId: string): CreateSubagentFormData | undefined =>
  state.tabs.tabStates[tabId]?.createSubagentFormData;

export default tabSlice.reducer;