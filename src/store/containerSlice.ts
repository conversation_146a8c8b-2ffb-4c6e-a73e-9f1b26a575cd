import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface TaskState {
  image: string;
  task: string;
  githubToken: string;
  volumePath: string;
  diff?: string;
}

interface SingleContainerState {
  isInitialized: boolean;
  isJobPolling: boolean;
  jobDetails: {
    job_id: string;
    traj_path: string;
    initial_commit_id?: string;
    acc_cost?: number;
    max_budget?: number;
  } | undefined;
  trajectory: any[];
  lastProcessedStep: number;
  taskState: TaskState | null;
}

interface ContainerState {
  activeContainerId: string | null;
  containers: Record<string, SingleContainerState>;
  // Tab-based container mapping: tabId -> containerId
  tabContainers: Record<string, string>;
}

const initialSingleContainerState: SingleContainerState = {
  isInitialized: false,
  isJobPolling: false,
  jobDetails: undefined,
  trajectory: [],
  lastProcessedStep: -1,
  taskState: null,
};

const initialState: ContainerState = {
  activeContainerId: null,
  containers: {},
  tabContainers: {},
};

export const containerSlice = createSlice({
  name: 'container',
  initialState,
  reducers: {
    initializeContainer: (state, action: PayloadAction<string>) => {
      const containerId = action.payload;
      if (!containerId) {
        console.error('No container ID provided');
        return;
      }
      if (!state.containers) {
        state.containers = {};
      }
      if (!state.containers[containerId]) {
        state.containers[containerId] = { ...initialSingleContainerState };
      }
      state.activeContainerId = containerId;
    },
    setInitialized: (state, action: PayloadAction<{ containerId: string; isInitialized: boolean }>) => {
      const { containerId, isInitialized } = action.payload;
      if (!containerId) {
        console.error('No container ID provided');
        return;
      }
      if (!state.containers) {
        state.containers = {};
      }
      if (!state.containers[containerId]) {
        state.containers[containerId] = { ...initialSingleContainerState };
      }
      state.containers[containerId].isInitialized = isInitialized;
    },
    setJobPolling: (state, action: PayloadAction<{ containerId: string; value: boolean }>) => {
      const { containerId, value } = action.payload;
      if (state.containers[containerId]) {
        state.containers[containerId].isJobPolling = value;
      }
    },
    setJobDetails: (state, action: PayloadAction<{ 
      containerId: string; 
      details: { 
        job_id: string; 
        traj_path: string; 
        initial_commit_id?: string;
        acc_cost?: number;
        max_budget?: number;
      }
    }>) => {
      const { containerId, details } = action.payload;
      if (state.containers[containerId]) {
        state.containers[containerId].jobDetails = details;
      }
    },
    setTrajectory: (state, action: PayloadAction<{
      containerId: string;
      trajectory: any[];
    }>) => {
      const { containerId, trajectory } = action.payload;
      if (state.containers[containerId]) {
        state.containers[containerId].trajectory = trajectory;
      }
    },
    setLastProcessedStep: (state, action: PayloadAction<{
      containerId: string;
      step: number;
    }>) => {
      const { containerId, step } = action.payload;
      if (state.containers[containerId]) {
        state.containers[containerId].lastProcessedStep = step;
      }
    },
    setTaskState: (state, action: PayloadAction<{
      containerId: string;
      taskState: TaskState;
    }>) => {
      const { containerId, taskState } = action.payload;
      if (state.containers[containerId]) {
        state.containers[containerId].taskState = taskState;
      }
    },
    updateTaskState: (state, action: PayloadAction<{
      containerId: string;
      taskState: Partial<TaskState>;
    }>) => {
      const { containerId, taskState } = action.payload;
      if (state.containers[containerId]) {
        const container = state.containers[containerId];
        container.taskState = container.taskState 
          ? { ...container.taskState, ...taskState }
          : taskState as TaskState;
      }
    },
    resetContainer: (state, action: PayloadAction<string>) => {
      const containerId = action.payload;
      const { [containerId]: _, ...remainingContainers } = state.containers;
      state.containers = remainingContainers;
      if (state.activeContainerId === containerId) {
        state.activeContainerId = null;
      }
    },
    resetAllContainers: (state) => {
      state.containers = {};
      state.activeContainerId = null;
      state.tabContainers = {};
    },
    setTabContainer: (state, action: PayloadAction<{ tabId: string; containerId: string }>) => {
      const { tabId, containerId } = action.payload;
      state.tabContainers[tabId] = containerId;
    },
    removeTabContainer: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      delete state.tabContainers[tabId];
    },
    setActiveContainerForTab: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      const containerId = state.tabContainers[tabId];
      if (containerId) {
        state.activeContainerId = containerId;
      }
    },
  },
});

export const {
  initializeContainer,
  setInitialized,
  setJobPolling,
  setJobDetails,
  setTrajectory,
  setLastProcessedStep,
  setTaskState,
  updateTaskState,
  resetContainer,
  resetAllContainers,
  setTabContainer,
  removeTabContainer,
  setActiveContainerForTab,
} = containerSlice.actions;

// Selectors
export const selectContainerForTab = (state: any, tabId: string) => {
  const containerId = state.container.tabContainers[tabId];
  return containerId ? state.container.containers[containerId] : undefined;
};

export const selectContainerIdForTab = (state: any, tabId: string) => {
  return state.container.tabContainers[tabId];
};

export const selectActiveContainer = (state: any) => {
  const activeContainerId = state.container.activeContainerId;
  return activeContainerId ? state.container.containers[activeContainerId] : undefined;
};

export default containerSlice.reducer;
