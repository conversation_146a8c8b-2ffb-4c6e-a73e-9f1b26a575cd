// @ts-nocheck

function validateEnv() {
  const requiredEnvVars = [
    "VITE_SUPABASE_URL",
    "VITE_SUPABASE_ANON_KEY",
    "VITE_API_BASE_URL",
    "VITE_GITHUB_APP_URL",
    "VITE_GITHUB_CLIENT_ID",
    "VITE_API_BASE_URL_APP_PUBLIC_POSTHOG_KEY",
    "VITE_APP_PUBLIC_POSTHOG_HOST",
    "VITE_APP_UPLOAD_URL",
    "VITE_CLOUDINARY_CLOUD_NAME"
  ];

  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !import.meta.env[envVar]
  );

  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingEnvVars.join(", ")}`
    );
  }
}

// Re-export Cloudinary utilities from the dedicated service
export { createOptimizedImage, cld } from '@/services/cloudinaryService';

validateEnv();

interface Config {
  supabaseUrl: string;
  supabaseAnonKey: string;
  githubToken: string;
  pluginLibraryVersion: string;
  apiBaseUrl: string;
  blitzApiKey: string;
  fileUploadUrl: string;
}

// Create a config object with a getter/setter for apiBaseUrl
export const config = {
  supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
  supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY,
  githubToken: import.meta.env.VITE_GITHUB_TOKEN,
  _pluginLibraryVersion: "1.0.6-beta",
  _apiBaseUrl: import.meta.env.VITE_API_BASE_URL as string,
  _blitzApiKey: import.meta.env.VITE_APP_BLITZ_API_KEY as string,
  _file_upload_url: import.meta.env.VITE_APP_UPLOAD_URL as string,
  get apiBaseUrl() {
    return this._apiBaseUrl;
  },
  set apiBaseUrl(value: string) {
    this._apiBaseUrl = value;
    // Save to localStorage for persistence
    localStorage.setItem('apiBaseUrl', value);
  },
  get blitzApiKey() {
    return this._blitzApiKey;
  },
} as const;


// GitHub configuration
export const githubConfig = {
  appUrl: import.meta.env.VITE_GITHUB_APP_URL,
  clientId:  import.meta.env.VITE_GITHUB_CLIENT_ID,
  getCallbackUrl: () => `${window.location.origin}/github-callback`,
};

export const postHog = {
  apiKey:  import.meta.env.VITE_API_BASE_URL_APP_PUBLIC_POSTHOG_KEY,
  apiHost:  import.meta.env.VITE_APP_PUBLIC_POSTHOG_HOST,
};
