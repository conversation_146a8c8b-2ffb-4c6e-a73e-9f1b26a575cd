import React from "react";
import { motion } from "framer-motion";

// Base shimmer skeleton component
const ShimmerSkeleton: React.FC<{
  className?: string;
  children?: React.ReactNode;
}> = ({ className = "", children }) => (
  <div className={`relative overflow-hidden bg-[#FFFFFF05] rounded-lg ${className}`}>
    <div className="absolute inset-0 shimmer-effect" />
    {children}
  </div>
);

// System Constraint Section Skeleton
export const SystemConstraintSkeleton: React.FC = () => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex flex-col gap-3"
  >
    {/* Title skeleton */}
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center gap-2">
        <ShimmerSkeleton className="w-4 h-4 rounded" />
        <ShimmerSkeleton className="w-32 h-4 rounded" />
      </div>
      <div className="flex items-center gap-2">
        <ShimmerSkeleton className="w-24 h-4 rounded" />
        <ShimmerSkeleton className="w-5 h-5 rounded" />
      </div>
    </div>
    
    {/* Textarea skeleton */}
    <ShimmerSkeleton className="w-full h-32 rounded-lg border border-[#1F1F1F]">
      <div className="p-4 space-y-2">
        <div className="w-full h-3 bg-[#FFFFFF08] rounded" />
        <div className="w-4/5 h-3 bg-[#FFFFFF08] rounded" />
        <div className="w-3/4 h-3 bg-[#FFFFFF08] rounded" />
        <div className="w-5/6 h-3 bg-[#FFFFFF08] rounded" />
      </div>
    </ShimmerSkeleton>
  </motion.div>
);

// Tool Item Skeleton
export const ToolItemSkeleton: React.FC = () => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="flex items-center justify-between p-4 bg-[#FFFFFF05] rounded-lg border border-[#222224]"
  >
    <div className="flex items-center gap-3">
      {/* Tool icon skeleton */}
      <ShimmerSkeleton className="w-8 h-8 rounded-lg" />
      
      <div className="space-y-2">
        {/* Tool name skeleton */}
        <ShimmerSkeleton className="w-24 h-4 rounded" />
        {/* Tool description skeleton */}
        <ShimmerSkeleton className="w-40 h-3 rounded" />
      </div>
    </div>
    
    {/* Toggle skeleton */}
    <ShimmerSkeleton className="w-11 h-6 rounded-full" />
  </motion.div>
);

// Sub-agent Item Skeleton
export const SubAgentItemSkeleton: React.FC = () => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="flex items-center justify-between p-4 bg-[#FFFFFF05] rounded-lg border border-[#222224]"
  >
    <div className="flex items-center gap-3">
      {/* Sub-agent icon skeleton */}
      <ShimmerSkeleton className="w-8 h-8 rounded-lg" />
      
      <div className="space-y-2">
        {/* Sub-agent name skeleton */}
        <ShimmerSkeleton className="w-28 h-4 rounded" />
        {/* Sub-agent description skeleton */}
        <ShimmerSkeleton className="w-48 h-3 rounded" />
      </div>
    </div>
    
    {/* Toggle skeleton */}
    <ShimmerSkeleton className="w-11 h-6 rounded-full" />
  </motion.div>
);

// Multiple Tool Items Skeleton (for loading multiple tools)
export const ToolsListSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="space-y-3">
    {Array.from({ length: count }).map((_, index) => (
      <ToolItemSkeleton key={index} />
    ))}
  </div>
);

// Multiple Sub-agent Items Skeleton (for loading multiple sub-agents)
export const SubAgentsListSkeleton: React.FC<{ count?: number }> = ({ count = 4 }) => (
  <div className="space-y-3">
    {Array.from({ length: count }).map((_, index) => (
      <SubAgentItemSkeleton key={index} />
    ))}
  </div>
);

// System Constraints Section Skeleton (for multiple sections)
export const SystemConstraintsSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="flex flex-col gap-12">
    {Array.from({ length: count }).map((_, index) => (
      <SystemConstraintSkeleton key={index} />
    ))}
  </div>
);

// Loading message with shimmer effect
export const LoadingMessage: React.FC<{ message?: string }> = ({ 
  message = "Loading..." 
}) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex items-center justify-center py-8"
  >
    <div className="flex items-center gap-3">
      <ShimmerSkeleton className="w-6 h-6 rounded-full" />
      <span className="text-[#8A8F98] font-medium">{message}</span>
    </div>
  </motion.div>
);
