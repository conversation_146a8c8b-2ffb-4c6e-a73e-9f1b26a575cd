import { X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAlert } from "@/contexts/AlertContext";

// Step 1 content component
const Step1Content = ({ onBack }: { onBack: () => void }) => {
  return (
    <motion.div
      className="relative flex flex-col w-full gap-2 pb-4 md:gap-4 md:pb-8"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <motion.div
        className="sticky top-0 p-4 bg-[#1C1C1F] md:px-6 md:pt-6 z-[10] border-b-[1px] border-[#242424]"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-[#1C1C1F] flex items-center justify-between">
          <h2 className="text-white/80 text-[18px] md:text-[24px] font-semibold">Creating Your Perfect AI Agent</h2>
          <button
            type="button"
            onClick={onBack}
            className="w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        className="px-4 space-y-6 md:px-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          staggerChildren: 0.1,
          delayChildren: 0.3
        }}
      >
        {/* Introduction */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <p className="text-[#FFFFFF99] text-[16px] font-['Inter'] leading-relaxed">
            Welcome! Creating an AI agent might sound complex, but think of it like training a skilled assistant who's eager to help with your specific needs. Let's walk through this together, step by step.
          </p>
        </motion.div>

        {/* Step 1: Give Your Agent a Personality */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 1: Give Your Agent a Personality (The Persona)
          </h3>
          
          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Just like people excel in different areas, AI agents work best when they know their role.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Think of this as introducing your agent to their new job. Be specific about who they are and what they're good at:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Need a Research Assistant?</span> Try: <em>"You are an expert researcher, profound at searching through information and synthesizing complex topics into clear insights..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Want a Creative Designer?</span> Start with: <em>"You are an experienced Designer who excels at creating beautiful, user-friendly interfaces..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Building Something Custom?</span> Consider: <em>"You are Emergent, a full-stack app builder who excels in creating innovative solutions..."</em>
                </p>
              </div>
            </div>
          </div>

          <div className="bg-[#F8FF9914] p-4 rounded-[8px] border border-[#F8FF9933]">
            <p className="text-[#F8FF99] text-[14px] font-medium">
              Pro tip: The more specific you are about their expertise, the better they'll perform!
            </p>
          </div>
        </motion.div>

        {/* Step 2: Explain What You Need Done */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 2: Explain What You Need Done (The Task)
          </h3>
          
          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Clear instructions lead to better results - just like giving directions to a friend.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            This is where you describe your actual goal. Be as clear as you would be when asking a colleague for help:
          </p>

          <div className="space-y-2">
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              • Want to automate repetitive business tasks? Describe which ones and how they currently work
            </p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              • Need a study app? Explain what subjects and what features would help you learn best
            </p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              • Building something unique? Paint a picture of what success looks like
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed font-medium">
            Remember: Your agent wants to help, so don't hold back on details!
          </p>
        </motion.div>

        {/* Step 3: Share Current Context */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 3: Share Current Context (Optional but Helpful)
          </h3>
          
          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Sometimes your agent needs to know about recent developments or specific circumstances.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            If your task involves current events or recent changes in technology, give your agent a heads up. For example:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"It's currently July 2025, and Emergent just launched E1.2, their most capable model yet!"</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"Our company just switched to a new system last month..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"The latest regulations in our industry require..."</em>
                </p>
              </div>
            </div>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            This helps your agent provide timely, relevant solutions rather than outdated information.
          </p>
        </motion.div>

        {/* Step 4: Map Out Your Ideal Workflow */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.5 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 4: Map Out Your Ideal Workflow
          </h3>

          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Sharing your thought process helps the agent work the way YOU work.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Think about how you'd tackle this problem yourself, then guide your agent through those same steps:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">1</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Start with Planning:</span> <em>"First, analyze the requirements and create a plan..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">2</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Build the Foundation:</span> <em>"Begin with the basic functionality..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">3</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Add Advanced Features:</span> <em>"Once the core is working, implement..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">4</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">Customize to Perfection:</span> <em>"Finally, add these specific touches I need..."</em>
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Working with Sub-agents */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Working with Sub-agents (Team Players)
          </h3>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            If your main agent needs to delegate specialized tasks to other agents (like a manager working with specialists), mention this in your workflow:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"When you need detailed code analysis, consult the Code Reviewer agent..."</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"For design decisions, collaborate with the UI/UX specialist agent..."</em>
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Step 5: Set Your Guidelines and Preferences */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.7 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 5: Set Your Guidelines and Preferences
          </h3>

          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              These are your "house rules" - they help your agent work exactly how you prefer.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Share any specific preferences, constraints, or things to be careful about:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"Always prioritize user-friendly solutions over complex ones"</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"Use the troubleshooter tool primarily for debugging issues"</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"When testing frontend features, use the frontend testing tool exclusively"</em>
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <em>"Double-check any financial calculations for accuracy"</em>
                </p>
              </div>
            </div>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed font-medium">
            Think of these as friendly reminders that keep your agent on track.
          </p>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

// Step 2 content component - Tools
const Step2Content = ({ onBack }: { onBack: () => void }) => {
  return (
    <motion.div
      className="relative flex flex-col w-full gap-2 pb-4 md:gap-4 md:pb-8"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
           <motion.div
        className="sticky top-0 p-4 bg-[#1C1C1F] md:px-6 md:pt-6 z-[10] border-b-[1px] border-[#242424]"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-[#1C1C1F] flex items-center justify-between">
          <h2 className="text-white/80 text-[18px] md:text-[24px] font-semibold">Learn about Tools</h2>
          <button
            type="button"
            onClick={onBack}
            className="w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        className="px-4 space-y-6 md:px-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          staggerChildren: 0.1,
          delayChildren: 0.3
        }}
      >
        {/* Selecting Tools: Equipping Your Agent */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Selecting Tools: Equipping Your Agent
          </h3>

          <p className="text-[#FFFFFF99] text-[16px] font-['Inter'] leading-relaxed">
            <span className="font-medium text-white">Think of tools as your agent's superpowers</span> – each one gives your agent a specific ability to help you better. Just like a chef needs the right utensils or a carpenter needs the right tools, your AI agent needs the right digital tools to accomplish its tasks effectively.
          </p>
        </motion.div>

        {/* What Exactly Are Tools? */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            What Exactly Are Tools?
          </h3>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Imagine your agent as a talented professional who needs access to different resources to do their job. Tools are these resources:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">A Web Search tool</span> is like giving your agent internet access to find current information
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">A File Reader tool</span> helps your agent access and understand documents you provide
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">A Screenshot tool</span> helps your agent take screenshots
                </p>
              </div>
            </div>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed font-medium">
            Think of each tool as adding a new skill to your agent's toolkit!
          </p>
        </motion.div>

        {/* The Art of Tool Selection */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            The Art of Tool Selection
          </h3>

          {/* Purpose-Driven Selection */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              Purpose-Driven Selection
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
              Each tool should have a clear job to do
            </p>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Before adding a tool, ask yourself: "Will my agent actually need this for the task I'm giving it?"
            </p>

            <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
              <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Examples:</p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                Building a research assistant? Definitely include Web Search and Document Analysis tools
              </p>
            </div>
          </div>

          {/* Quality Over Quantity */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              Quality Over Quantity
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Giving your agent too many tools is like giving someone a Swiss Army knife when they just need a screwdriver. More tools means:
            </p>

            <div className="ml-4 space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Your agent takes longer to decide which tool to use
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • It might get distracted from the main task
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • The responses become less focused and more scattered
              </p>
            </div>

            <div className="bg-[#F8FF9914] p-4 rounded-[8px] border border-[#F8FF9933]">
              <p className="text-[#F8FF99] text-[14px] font-medium">
                <span className="font-semibold">Rule of thumb:</span> If you're not sure whether you need a tool, start without it.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Important Notes */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Important Notes
          </h3>

          {/* About Default Tools */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              About Default Tools
            </h4>

            <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#FFAE66]">
              <p className="text-[#FFAE66] text-[14px] font-medium mb-2 italic">
                The tools selected by default are mandatorily required by the agents to function.
              </p>
            </div>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Think of default tools as the "starter pack" – they're like the engine in a car. Your agent needs these to:
            </p>

            <div className="ml-4 space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Understand and process your requests
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Communicate responses effectively
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Maintain its core functionality
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

// Step 3 content component - Sub-Agents
const Step3Content = ({ onBack }: { onBack: () => void }) => {
  return (
    <motion.div
      className="relative flex flex-col w-full gap-2 pb-4 md:gap-4 md:pb-8"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
          <motion.div
        className="sticky top-0 p-4 bg-[#1C1C1F] md:px-6 md:pt-6 z-[10] border-b-[1px] border-[#242424]"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-[#1C1C1F] flex items-center justify-between">
          <h2 className="text-white/80 text-[18px] md:text-[24px] font-semibold">Learn about Sub-Agents</h2>
          <button
            type="button"
            onClick={onBack}
            className="w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        className="px-4 space-y-6 md:px-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          staggerChildren: 0.1,
          delayChildren: 0.3
        }}
      >
        {/* What are Sub-Agents? */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            What are Sub-Agents?
          </h3>

          <p className="text-[#FFFFFF99] text-[16px] font-['Inter'] leading-relaxed">
            Think of sub-agents as your agent's network of expert colleagues. Just like how a general contractor might call in an electrician for wiring or a plumber for pipes, your main agent can call on sub-agents when it needs specialized help.
          </p>
        </motion.div>

        {/* Hyper-Specialization */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <h4 className="text-white text-[18px] font-semibold">
            Hyper-Specialization: Masters of Their Craft
          </h4>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Each sub-agent is like a Master in their specific field.
          </p>
        </motion.div>

        {/* On-Demand Collaboration */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <h4 className="text-white text-[18px] font-semibold">
            On-Demand Collaboration: Right Expert, Right Time
          </h4>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Your main agent intelligently decides when to bring in the sub agents based on their use case.
          </p>
        </motion.div>

        {/* How Main Agent and Sub-Agent interact */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            How Main Agent and Sub-Agent interact:
          </h3>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">1</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  You give your main agent a task
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">2</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  It recognizes what subagent is needed, for a specific subtask
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">3</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  Automatically hands over the required subtask to the right sub-agents
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">4</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  Sub agent performs its function and outputs the result
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                <span className="text-[#80FFF9] text-[12px] font-bold">5</span>
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  Receives the response from the subagent
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* How to Create Sub-agents Effectively */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.5 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            How to Create Sub-agents Effectively
          </h3>

          {/* 1. Identify Your Needs */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              1. Identify Your Needs:
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Start by giving your Sub-Agent a name, Ask yourself:
            </p>

            <div className="ml-4 space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • What specialized tasks come up repeatedly, that needs to be handed off the main Agent?
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Where do I need a highly specific agent?
              </p>
            </div>
          </div>

          {/* 2. Give definition to the Sub-Agent */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              2. Give definition to the Sub-Agent:
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              This definition will go to the main agent, please clearly state what this Sub Agent can do, and when it should be called.
            </p>
          </div>

          {/* 3. Define System Prompt */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              3. Define System Prompt:
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Give your agent step-by-step approach to approach the required task. Set guidelines for best practices, constraints, and things to avoid.
            </p>
          </div>

          {/* 4. Configure Sub-Agent Tools */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              4. Configure Sub-Agent Tools:
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Assign tools that are required by your subagent to complete their work and
            </p>
          </div>
        </motion.div>

        {/* Best Practices for Sub-agent Success */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Best Practices for Sub-agent Success
          </h3>

          {/* DO's */}
          <div className="space-y-3">
            <h4 className="text-[#80FFF9] text-[18px] font-semibold">
              DO's:
            </h4>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                  <div className="w-2 h-2 bg-[#80FFF9] rounded-[1.5px]" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    Give each sub-agent a clear, focused role
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                  <div className="w-2 h-2 bg-[#80FFF9] rounded-[1.5px]" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    Provide context about when they should be consulted
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                  <div className="w-2 h-2 bg-[#80FFF9] rounded-[1.5px]" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    Allow sub-agents to communicate with each other when needed
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                  <div className="w-2 h-2 bg-[#80FFF9] rounded-[1.5px]" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    Test the handoffs between agents to ensure smooth operation
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* DON'Ts */}
          <div className="space-y-3">
            <h4 className="text-[#FF6B6B] text-[18px] font-semibold">
              DON'Ts:
            </h4>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#FF6B6B]/20 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                  <div className="w-2 h-2 bg-[#FF6B6B] rounded-[1.5px]" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    Make sub-agents too broad (defeats the purpose of specialization)
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#FF6B6B]/20 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                  <div className="w-2 h-2 bg-[#FF6B6B] rounded-[1.5px]" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    Overwhelm with too many sub-agents initially (start small, grow as needed)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

// Step 1 content component for Sub-agents
const Step1SubagentContent = ({ onBack }: { onBack: () => void }) => {
  return (
    <motion.div
      className="relative flex flex-col w-full gap-2 pb-4 md:gap-4 md:pb-8"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
          <motion.div
        className="sticky top-0 p-4 bg-[#1C1C1F] md:px-6 md:pt-6 z-[10] border-b-[1px] border-[#242424]"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-[#1C1C1F] flex items-center justify-between">
          <h2 className="text-white/80 text-[18px] md:text-[24px] font-semibold">Creating Your Sub-Agent: A Step-by-Step Guide</h2>
          <button
            type="button"
            onClick={onBack}
            className="w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        className="px-4 space-y-6 md:px-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          staggerChildren: 0.1,
          delayChildren: 0.3
        }}
      >
        {/* Introduction */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <p className="text-[#FFFFFF99] text-[16px] font-['Inter'] leading-relaxed">
            Sub-agents are like specialist consultants that your main agent can call upon for specific expertise. While creating them follows similar principles to main agents, there are key differences in how you approach each step.
          </p>
        </motion.div>

        {/* Step 1: Define Your Sub-Agent's Hyper-Specialized Persona */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 1: Define Your Sub-Agent's Hyper-Specialized Persona
          </h3>

          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Sub-agents excel through laser-focused expertise, not broad capabilities.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Unlike main agents who might wear many hats, sub-agents should be masters of ONE specific domain.
          </p>

          <div className="flex items-start gap-3">
            <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
              <div className="w-2 h-2 bg-white rounded-[1.5px]" />
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                <span className="font-medium text-white">Code Review Specialist:</span> <em>"You are an expert code reviewer who specializes in identifying bugs, security vulnerabilities, and optimization opportunities in code..."</em>
              </p>
            </div>
          </div>

          <div className="bg-[#F8FF9914] p-4 rounded-[8px] border border-[#F8FF9933]">
            <p className="text-[#F8FF99] text-[14px] font-medium">
              <span className="font-semibold">Key difference from main agents:</span> Be ultra-specific. If your main agent is a general contractor, your sub-agent should be the master electrician who only does wiring.
            </p>
          </div>
        </motion.div>

        {/* Step 2: Create Two Descriptions */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 2: Create Two Descriptions - One for Each Audience
          </h3>

          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Your sub-agent needs to be understood by both the main agent AND perform its specialized task.
            </p>
          </div>

          {/* A. The Definition (For the Main Agent) */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              A. The Definition (For the Main Agent)
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              This tells your main agent WHEN and WHY to call this sub-agent:
            </p>

            <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border border-[#333333]">
              <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Example:</p>
              <div className="space-y-2">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                  "The Code Review Sub-Agent should be consulted when:
                </p>
                <div className="ml-4 space-y-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    • You need to verify code quality before deployment
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    • You encounter bugs or errors in existing code
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    • You want to optimize performance of specific functions
                  </p>
                </div>
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                  This agent specializes in JavaScript, Python, and React applications."
                </p>
              </div>
            </div>
          </div>

          {/* B. The Task Description (For the Sub-Agent) */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              B. The Task Description (For the Sub-Agent)
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              This tells the sub-agent WHAT to do when called:
            </p>

            <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border border-[#333333]">
              <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Example:</p>
              <div className="space-y-2">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                  "When you receive code to review, analyze it for:
                </p>
                <div className="ml-4 space-y-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    1. Syntax errors and bugs
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    2. Security vulnerabilities
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    3. Performance bottlenecks
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                    4. Code style and best practices
                  </p>
                </div>
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
                  Provide specific, actionable feedback with examples."
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Step 3: Design the System Prompt with Focused Workflow */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 3: Design the System Prompt with Focused Workflow
          </h3>

          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Sub-agents need clear, repeatable processes since they handle specific types of requests.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Structure your sub-agent's workflow around its specialization:
          </p>

          <div className="bg-[#1A1A1C] p-4 rounded-[8px] border border-[#333333]">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                  <span className="text-[#80FFF9] text-[12px] font-bold">1</span>
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    <span className="font-medium text-white">Receive the Specific Request:</span> Understand what the main agent needs
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                  <span className="text-[#80FFF9] text-[12px] font-bold">2</span>
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    <span className="font-medium text-white">Apply Your Expertise:</span> Use your specialized knowledge to analyze
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                  <span className="text-[#80FFF9] text-[12px] font-bold">3</span>
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    <span className="font-medium text-white">Deliver Targeted Results:</span> Provide exactly what was requested
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center rounded-[8px] bg-[#80FFF9]/20 backdrop-blur-lg max-h-6 max-w-6 min-h-6 min-w-6">
                  <span className="text-[#80FFF9] text-[12px] font-bold">4</span>
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    <span className="font-medium text-white">Include Recommendations:</span> Suggest improvements within your domain
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Step 4: Set Interaction Guidelines */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.5 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Step 4: Set Interaction Guidelines
          </h3>

          <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
            <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why this matters:</p>
            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Sub-agents need to know how to communicate with the main agent and potentially other sub-agents.
            </p>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Include guidelines for:
          </p>

          {/* Communication Protocol */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              Communication Protocol:
            </h4>

            <div className="space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • <em>"Always format your responses with clear sections: Analysis, Findings, Recommendations"</em>
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • <em>"Flag critical issues immediately at the start of your response"</em>
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • <em>"Use technical terms when communicating with the main agent"</em>
              </p>
            </div>
          </div>

          {/* Constraints and Best Practices */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              Constraints and Best Practices:
            </h4>

            <div className="space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • <em>"Stay within your domain of expertise"</em>
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • <em>"Prioritize accuracy over speed in your analysis"</em>
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • <em>"If you cannot complete a request, explain why and suggest alternatives"</em>
              </p>
            </div>
          </div>
        </motion.div>

        {/* System Constraints */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <div className="border-t border-[#333333] pt-6">
            <h3 className="text-white text-[20px] font-semibold mb-4">
              System Constraints (Read-Only Section)
            </h3>

            <div className="space-y-4">
              <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#FFAE66]">
                <p className="text-[#FFAE66] text-[14px] font-medium mb-2">What this is:</p>
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  This section contains essential technical guidelines that help your sub-agent operate properly within its environment. Think of it as the "operating manual" that ensures everything runs smoothly.
                </p>
              </div>

              <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
                <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Why it's protected:</p>
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed mb-2">
                  These constraints are carefully designed to ensure your agent can:
                </p>
                <div className="ml-4 space-y-1">
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    • Access the right tools and features
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    • Communicate with other agents effectively
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    • Maintain security and performance standards
                  </p>
                  <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                    • Follow best practices automatically
                  </p>
                </div>
              </div>

              <div className="bg-[#F8FF9914] p-4 rounded-[8px] border border-[#F8FF9933]">
                <p className="text-[#F8FF99] text-[14px] font-medium">
                  You don't need to modify this section - it's like the foundation of a house that supports everything you build on top!
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

// Step 2 content component for Sub-agents - Tools
const Step2SubagentContent = ({ onBack }: { onBack: () => void }) => {
  return (
    <motion.div
      className="relative flex flex-col w-full gap-2 pb-4 md:gap-4 md:pb-8"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <motion.div
        className="sticky top-0 p-4 bg-[#1C1C1F] md:px-6 md:pt-6 z-[10] border-b-[1px] border-[#242424]"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-[#1C1C1F] flex items-center justify-between">
          <h2 className="text-white/80 text-[18px] md:text-[24px] font-semibold">Learn about Tools</h2>
          <button
            type="button"
            onClick={onBack}
            className="w-10 h-10 bg-[#FFFFFF0D] rounded-full flex items-center justify-center text-[#737780] backdrop-blur-lg hover:text-white transition-colors hover:bg-[#FFFFFF0A]"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        className="px-4 space-y-6 md:px-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          staggerChildren: 0.1,
          delayChildren: 0.3
        }}
      >
        {/* Selecting Tools: Equipping Your Agent */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Selecting Tools: Equipping Your Agent
          </h3>

          <p className="text-[#FFFFFF99] text-[16px] font-['Inter'] leading-relaxed">
            <span className="font-medium text-white">Think of tools as your agent's superpowers</span> – each one gives your agent a specific ability to help you better. Just like a chef needs the right utensils or a carpenter needs the right tools, your AI agent needs the right digital tools to accomplish its tasks effectively.
          </p>
        </motion.div>

        {/* What Exactly Are Tools? */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            What Exactly Are Tools?
          </h3>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
            Imagine your agent as a talented professional who needs access to different resources to do their job. Tools are these resources:
          </p>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">A Web Search tool</span> is like giving your agent internet access to find current information
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">A File Reader tool</span> helps your agent access and understand documents you provide
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center rounded-[8px] bg-white/10 backdrop-blur-lg max-h-5 max-w-5 min-h-5 min-w-5">
                <div className="w-2 h-2 bg-white rounded-[1.5px]" />
              </div>
              <div className="flex flex-col gap-1">
                <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                  <span className="font-medium text-white">A Screenshot tool</span> helps your agent take screenshots
                </p>
              </div>
            </div>
          </div>

          <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed font-medium">
            Think of each tool as adding a new skill to your agent's toolkit!
          </p>
        </motion.div>

        {/* The Art of Tool Selection */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            The Art of Tool Selection
          </h3>

          {/* Purpose-Driven Selection */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              Purpose-Driven Selection
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed italic">
              Each tool should have a clear job to do
            </p>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Before adding a tool, ask yourself: "Will my agent actually need this for the task I'm giving it?"
            </p>

            <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#80FFF9]">
              <p className="text-[#80FFF9] text-[14px] font-medium mb-2">Examples:</p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                Building a research assistant? Definitely include Web Search and Document Analysis tools
              </p>
            </div>
          </div>

          {/* Quality Over Quantity */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              Quality Over Quantity
            </h4>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Giving your agent too many tools is like giving someone a Swiss Army knife when they just need a screwdriver. More tools means:
            </p>

            <div className="ml-4 space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Your agent takes longer to decide which tool to use
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • It might get distracted from the main task
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • The responses become less focused and more scattered
              </p>
            </div>

            <div className="bg-[#F8FF9914] p-4 rounded-[8px] border border-[#F8FF9933]">
              <p className="text-[#F8FF99] text-[14px] font-medium">
                <span className="font-semibold">Rule of thumb:</span> If you're not sure whether you need a tool, start without it.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Important Notes */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <h3 className="text-white text-[20px] font-semibold">
            Important Notes
          </h3>

          {/* About Default Tools */}
          <div className="space-y-3">
            <h4 className="text-white text-[18px] font-semibold">
              About Default Tools
            </h4>

            <div className="bg-[#FFFFFF08] p-4 rounded-[8px] border-l-4 border-[#FFAE66]">
              <p className="text-[#FFAE66] text-[14px] font-medium mb-2 italic">
                The tools selected by default are mandatorily required by the agents to function.
              </p>
            </div>

            <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
              Think of default tools as the "starter pack" – they're like the engine in a car. Your agent needs these to:
            </p>

            <div className="ml-4 space-y-2">
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Understand and process your requests
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Communicate responses effectively
              </p>
              <p className="text-[#FFFFFF99] text-[14px] font-['Inter'] leading-relaxed">
                • Maintain its core functionality
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export const LearnMoreModal = ({
  isOpen,
  onOpenChange,
  currentStep = 1,
  isSubagent = false,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  currentStep?: number;
  isSubagent?: boolean;
}) => {
  const { bannerHeight } = useAlert();
  const heightDiff = bannerHeight + 56;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          style={{
            height: `calc(100vh - ${heightDiff}px)`,
            top: `${heightDiff}px`,
          }}
          className={cn("fixed inset-0 w-full z-[100] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]")}
          onClick={() => onOpenChange(false)}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
        >
          <motion.div
            className={cn(
              "p-0 sm:max-w-[700px] max-h-[90dvh] overflow-y-auto w-full max-w-[600px] mx-auto bg-[#18181A] border border-[#242424] rounded-[20px] font-['Inter']"
            )}
            onClick={(e) => e.stopPropagation()}
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
          >
            {isSubagent ? (
              currentStep === 1 ? (
                <Step1SubagentContent
                  onBack={() => onOpenChange(false)}
                />
              ) : currentStep === 2 ? (
                <Step2SubagentContent
                  onBack={() => onOpenChange(false)}
                />
              ) : (
                <Step1SubagentContent
                  onBack={() => onOpenChange(false)}
                />
              )
            ) : (
              currentStep === 1 ? (
                <Step1Content
                  onBack={() => onOpenChange(false)}
                />
              ) : currentStep === 2 ? (
                <Step2Content
                  onBack={() => onOpenChange(false)}
                />
              ) : currentStep === 3 ? (
                <Step3Content
                  onBack={() => onOpenChange(false)}
                />
              ) : (
                <Step1Content
                  onBack={() => onOpenChange(false)}
                />
              )
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
