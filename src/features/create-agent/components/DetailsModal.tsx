import React from "react";
import { X } from "lucide-react";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import NormalRobot from "@/components/icons/Robots/NormalRobot";

interface DetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: "tool" | "subagent";
  data: any;
}

export const DetailsModal: React.FC<DetailsModalProps> = ({
  isOpen,
  onClose,
  type,
  data,
}) => {
  const isApiTool = type === "tool" && "user_descriptions" in data;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[700px] p-0 bg-[#18181A] border-[#242424]">
        <div className="p-8 space-y-[40px]">
          <div className="space-y-4">
            <div className="flex justify-between">
              <div className="flex flex-col gap-4">
                <div className="w-12 h-12 rounded-full flex items-center justify-center bg-[#272729]">
                  <NormalRobot size={28} primaryColor="#fff" />
                </div>
                <h4 className="text-xl font-semibold text-[#FFFFFF] capitalize">
                  {data.name?.replaceAll("_", " ") || "Unknown"}
                </h4>
              </div>
              <DialogClose asChild>
                <button
                  type="button"
                  aria-label="Close details modal"
                  className="text-[#737780] h-8 w-8 flex items-center justify-center rounded-full backdrop-blur-lg bg-[#FFFFFF1A] hover:bg-[#FFFFFF20] hover:text-white transition-colors"
                >
                  <X size={20} />
                </button>
              </DialogClose>
            </div>
            <p className="text-[#FFFFFF]/60 font-inter font-medium text-wrap">
              {type === "tool"
                ? isApiTool
                  ? data.user_descriptions?.description ||
                    "No description available"
                  : data.description || data.skill_description
                : data.description|| data.skill_description || "No description available"}
            </p>
          </div>

          {/* <div className="flex flex-col gap-4">
            <span>And this can help in:</span>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex md:grid md:grid-cols-3 gap-[10px] max-md:pl-5 overflow-x-auto hide-scrollbar"
            >
              {INFO.map((item, index) => (
                <div
                  key={index}
                  className="flex rounded-[8px] items-start max-md:my-2 flex-col-reverse max-md:items-start p-3 md:px-5 md:py-4 w-full text-start bg-[#80FFF90A] gap-4"
                >
                  <div className="flex flex-col items-start w-full gap-[2px] md:gap-[6px]">
                    <h3 className="text-[14px] text-nowrap md:text-[16px] text-[#80FFF9] font-medium">
                      {item.title}
                    </h3>
                    <p className="text-[12px] md:text-[14px] font-[500] text-start text-[#80FFF966] font-['Inter']">
                      {item.description}
                    </p>
                  </div>
                  <img src={item.icon} alt={item.title} className="w-5 h-5" />
                </div>
              ))}
            </motion.div>
          </div> */}

          {/* <LearnMoreButton
            text={`Learn more about ${type}`}
            className="mt-6"
          /> */}
        </div>
      </DialogContent>
    </Dialog>
  );
};
