import Panel_Testing from "@/assets/panel_icons/panel_testing.json";
import Edit_Panel from "@/assets/panel_icons/panel_edit.json";
import Intergration_Panel from "@/assets/panel_icons/panel_integration.json";
import Vision_Panel from "@/assets/panel_icons/panel_vision.json";

interface SubagentStyle{
    key: string;
    color: string;
    bgColor: string;
    icon: string;
    iconColor: string;
    text: string;
    runningText: string;
    beforeBorder: string;
    panelLottie: any;
    gradients: {
        running: string;
        waiting: string;
        stopping: string;
    };
}

export const SUBAGENT_STYLES_OVERALL: SubagentStyle[] = [
    {
        key: "testing",
        color: "#4D320F",
        bgColor: "#BBA1E5",
        icon: "code",
        iconColor: "#4D320F",
        text: "Testing Agent",
        runningText: "is Running",
        beforeBorder:"before:border-[#BBA1E520]",
        panelLottie: Panel_Testing,
        gradients: {
            running: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(187, 161, 229, 0.3) 100%)",
            waiting: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(187, 161, 229, 0.2) 100%)",
            stopping: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)"
        }
    },

     {
        key: "replace",
        color: "#4D320F",
        bgColor: "#F8CC55",
        icon: "code",
        iconColor: "#4D320F",
        text: "Edit Agent",
        runningText: "is Running",
        beforeBorder:"before:border-[#4D320F20]",
        panelLottie: Edit_Panel,
        gradients: {
            running: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(248, 204, 85, 0.2) 100%)",
            waiting: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(248, 204, 85, 0.2) 100%)",
            stopping: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)"
        }
    },
    {
        key: "vision",
        color: "#661F3D",
        bgColor: "#E5A8A1",
        icon: "vision",
        iconColor: "#661F3D",
        text: "Image Agent",
        runningText: "is Running",
        beforeBorder:"before:border-[#E5A8A120]",
        panelLottie: Vision_Panel,
        gradients: {
            running: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(229, 168, 161, 0.2) 100%)",
            waiting: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(229, 168, 161, 0.2) 100%)",
            stopping: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)"
        }
    },
    {
        key: "support",
        color: "#661F3D",
        bgColor: "#E5A8A1",
        icon: "vision",
        iconColor: "#661F3D",
        text: "Support Agent",
        runningText: "is Running",
        beforeBorder:"before:border-[#E5A8A120]",
        panelLottie: Vision_Panel,
        gradients: {
            running: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(229, 168, 161, 0.2) 100%)",
            waiting: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(229, 168, 161, 0.2) 100%)",
            stopping: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)"
        }
    },
     {
        key: "troubleshoot",
        color: "#661F3D",
        bgColor: "#E5A8A1",
        icon: "vision",
        iconColor: "#661F3D",
        text: "Triage Agent",
        runningText: "is Running",
        beforeBorder:"before:border-[#E5A8A120]",
        panelLottie: Vision_Panel,
        gradients: {
            running: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(229, 168, 161, 0.2) 100%)",
            waiting: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(229, 168, 161, 0.2) 100%)",
            stopping: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)"
        }
    },
    {
        key: "integration",
        color: "#243045",
        bgColor: "#A1BFE5",
        icon: "integration",
        iconColor: "#243045",
        text: "Integration Agent",
        runningText: "is Running",
        beforeBorder:"before:border-[#A1BFE520]",
        panelLottie: Intergration_Panel,
        gradients: {
            running: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(161, 191, 229, 0.3) 100%)",
            waiting: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(161, 191, 229, 0.2) 100%)",
            stopping: "linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0%, rgba(244, 155, 87, 0.2) 100%)"
        }
    },

]


const getOverallKey = (expertise_type: string)=>{
    if(!expertise_type) return "Subagent";

    const testingAgent = expertise_type.includes("testing");
    const integrationAgent = expertise_type.includes("integration");
    const imageAgent = expertise_type.includes("vision");
    const troubleshootAgent = expertise_type.includes("troubleshoot");
    const supportAgent = expertise_type.includes("support");
    const editorAgent = expertise_type.includes("replace");


    if(testingAgent) return "testing";
    if(integrationAgent) return "integration";
    if(imageAgent) return "vision";
    if(troubleshootAgent) return "troubleshoot";
    if(supportAgent) return "support";
    if(editorAgent) return "replace";

    return "Subagent";
}



export const getSubagentName = (expertise_type: string)=>{
    if(!expertise_type) return "Subagent";

    const testingAgent = expertise_type.includes("testing");
    const integrationAgent = expertise_type.includes("integration");
    const imageAgent = expertise_type.includes("vision");
    const troubleshootAgent = expertise_type.includes("troubleshoot");
    const supportAgent = expertise_type.includes("support");
    const editorAgent = expertise_type.includes("replace");


    if(testingAgent) return "Testing Agent";
    if(integrationAgent) return "Integration Agent";
    if(imageAgent) return "Image Agent";
    if(troubleshootAgent) return "Triage Agent";
    if(supportAgent) return "Support Agent";
    if(editorAgent) return "Edit Agent";


    return "Subagent";
}

export const getSubagentStyle = (expertise_type: string)=>{
    const overallKey = getOverallKey(expertise_type);
    return SUBAGENT_STYLES_OVERALL.find((item)=> item.key === overallKey) || SUBAGENT_STYLES_OVERALL[0];
}

// Get gradient for subagent based on status
export const getSubagentGradient = (subagentName: string, status: "running" | "waiting" | "stopping", direction: string = "bottom") => {
    const subagentStyle = getSubagentStyle(subagentName);
    const gradient = subagentStyle.gradients[status];
    // Replace the hardcoded "to bottom" with the provided direction
    return gradient.replace("to bottom", `to ${direction}`);
}

// Get dynamic colors for status text based on agent/subagent
export const getDynamicStatusColor = (
    agentStatus: string,
    subagentName?: string,
    isSubagentActive?: boolean
) => {
    // Default colors for main agent statuses - these match the gradient end colors
    const defaultColors = {
        running: "#67CB65",        // rgba(103, 203, 101, 0.3) end color
        waiting: "#5FD3F3",        // rgba(95, 211, 243, 0.3) end color
        stopped: "#7B7B80",        // rgba(123, 123, 128, 0.2) end color
        stopping: "#F49B57",       // rgba(244, 155, 87, 0.3) end color - matches gradient
        subagent_stopping: "#F49B57", // Same orange color for subagent stopping
        exit_cost: "#7B7B80",
        forking: "#80FFF9"
    };

    // Use agent/subagent specific colors when status is running, waiting, or their subagent equivalents
    const isActiveSubagentStatus = agentStatus === "running" || agentStatus === "subagent_running" ||
                                   agentStatus === "subagent_waiting";

    // If subagent is active, we have a subagent name, and status is an active subagent status, use subagent colors
    if (isSubagentActive && subagentName && isActiveSubagentStatus) {
        const subagentStyle = getSubagentStyle(subagentName);
        return subagentStyle.bgColor;
    }

    // For subagent statuses without specific subagent, use default purple for active statuses
    if (agentStatus.startsWith('subagent_') && isActiveSubagentStatus) {
        return "#DD99FF"; // Fixed the hex color format
    }

    // For all other statuses (stopping, stopped, etc.), always use default colors
    return defaultColors[agentStatus as keyof typeof defaultColors] || defaultColors.stopped;
}