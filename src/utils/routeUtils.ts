/**
 * Utility functions for route checking and authentication
 */

/**
 * Checks if the current pathname is on an authentication route
 * @param pathname - The current pathname from location
 * @returns boolean - true if on an auth route, false otherwise
 */
export function isOnAuthRoute(pathname: string): boolean {
  const authRoutes = ['/verify', '/activate', '/reset-password', '/login', '/register', '/oauth'];
  return authRoutes.some(route => pathname.includes(route));
}
