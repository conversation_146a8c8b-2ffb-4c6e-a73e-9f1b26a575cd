/**
 * Service for handling referral code storage and retrieval during authentication flow
 */

const REFERRAL_CODE_KEY = 'pending_referral_code';
const REFERRAL_APPLIED_KEY = 'referral_applied';

export interface PendingReferralData {
  code: string;
  timestamp: number;
  source: 'url' | 'manual';
}

export class ReferralService {
  /**
   * Store a referral code temporarily during the authentication process
   */
  static storePendingReferralCode(code: string, source: 'url' | 'manual' = 'url'): void {
    if (!code || typeof code !== 'string') {
      console.warn('Invalid referral code provided to storePendingReferralCode');
      return;
    }

    const referralData: PendingReferralData = {
      code: code.trim(),
      timestamp: Date.now(),
      source,
    };

    try {
      localStorage.setItem(REFERRAL_CODE_KEY, JSON.stringify(referralData));
      console.log('Stored pending referral code:', code);
    } catch (error) {
      console.error('Failed to store referral code:', error);
    }
  }

  /**
   * Retrieve the stored referral code
   */
  static getPendingReferralCode(): PendingReferralData | null {
    try {
      const stored = localStorage.getItem(REFERRAL_CODE_KEY);
      if (!stored) return null;

      const referralData: PendingReferralData = JSON.parse(stored);
      
      // Check if the referral code is expired (24 hours)
      const EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      if (Date.now() - referralData.timestamp > EXPIRY_TIME) {
        console.log('Referral code expired, removing from storage');
        this.clearPendingReferralCode();
        return null;
      }

      return referralData;
    } catch (error) {
      console.error('Failed to retrieve referral code:', error);
      this.clearPendingReferralCode(); // Clear corrupted data
      return null;
    }
  }

  /**
   * Clear the stored referral code
   */
  static clearPendingReferralCode(): void {
    try {
      localStorage.removeItem(REFERRAL_CODE_KEY);
      console.log('Cleared pending referral code');
    } catch (error) {
      console.error('Failed to clear referral code:', error);
    }
  }

  /**
   * Mark that a referral has been successfully applied
   */
  static markReferralAsApplied(referralCode: string, reward?: number): void {
    try {
      const appliedData = {
        code: referralCode,
        timestamp: Date.now(),
        reward: reward || 5, // Default reward amount
      };
      localStorage.setItem(REFERRAL_APPLIED_KEY, JSON.stringify(appliedData));
      console.log('Marked referral as applied:', referralCode, 'with reward:', reward);
    } catch (error) {
      console.error('Failed to mark referral as applied:', error);
    }
  }

  /**
   * Check if a referral was recently applied (for showing success modal)
   */
  static wasReferralRecentlyApplied(): { applied: boolean; code?: string; reward?: number } {
    try {
      const stored = localStorage.getItem(REFERRAL_APPLIED_KEY);
      if (!stored) return { applied: false };

      const appliedData = JSON.parse(stored);
      
      // Check if it was applied recently (within last 5 minutes)
      const RECENT_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds
      if (Date.now() - appliedData.timestamp <= RECENT_TIME) {
        return {
          applied: true,
          code: appliedData.code,
          reward: appliedData.reward || 5 // Default reward amount
        };
      }

      // Clear old applied data
      this.clearAppliedReferralData();
      return { applied: false };
    } catch (error) {
      console.error('Failed to check applied referral:', error);
      this.clearAppliedReferralData();
      return { applied: false };
    }
  }

  /**
   * Clear the applied referral data
   */
  static clearAppliedReferralData(): void {
    try {
      localStorage.removeItem(REFERRAL_APPLIED_KEY);
      console.log('Cleared applied referral data');
    } catch (error) {
      console.error('Failed to clear applied referral data:', error);
    }
  }

  /**
   * Extract referral code from URL parameters
   */
  static extractReferralCodeFromUrl(url: string = window.location.href): string | null {
    try {
      const urlObj = new URL(url);
      // Support both 'referral' and 'ref' parameters
      return urlObj.searchParams.get('referral') || urlObj.searchParams.get('ref');
    } catch (error) {
      console.error('Failed to extract referral code from URL:', error);
      return null;
    }
  }

  /**
   * Remove referral parameters from URL without page reload
   */
  static cleanUrlParameters(): void {
    try {
      const url = new URL(window.location.href);
      const hasReferralParams = url.searchParams.has('referral') || url.searchParams.has('ref');
      
      if (hasReferralParams) {
        url.searchParams.delete('referral');
        url.searchParams.delete('ref');
        
        // Update URL without page reload
        window.history.replaceState({}, document.title, url.toString());
        console.log('Cleaned referral parameters from URL');
      }
    } catch (error) {
      console.error('Failed to clean URL parameters:', error);
    }
  }

  /**
   * Initialize referral handling on page load
   */
  static initializeReferralHandling(): void {
    // Extract referral code from URL if present
    const referralCode = this.extractReferralCodeFromUrl();

    if (referralCode) {
      console.log('Found referral code in URL:', referralCode, 'on page:', window.location.pathname);
      this.storePendingReferralCode(referralCode, 'url');
      this.cleanUrlParameters();
    } else {
      // Check if we already have a pending referral code
      const existing = this.getPendingReferralCode();
      if (existing) {
        console.log('Existing pending referral code found:', existing.code, 'from:', existing.source);
      }
    }
  }

  /**
   * Get referral share URL
   */
  static getReferralShareUrl(referralCode: string, baseUrl: string = window.location.origin): string {
    if (!referralCode) return baseUrl;

    try {
      const url = new URL(baseUrl);
      url.searchParams.set('ref', referralCode);
      return url.toString();
    } catch (error) {
      console.error('Failed to create referral share URL:', error);
      return `${baseUrl}?ref=${encodeURIComponent(referralCode)}`;
    }
  }
}

// Export convenience functions
export const {
  storePendingReferralCode,
  getPendingReferralCode,
  clearPendingReferralCode,
  markReferralAsApplied,
  wasReferralRecentlyApplied,
  clearAppliedReferralData,
  extractReferralCodeFromUrl,
  cleanUrlParameters,
  initializeReferralHandling,
  getReferralShareUrl,
} = ReferralService;
