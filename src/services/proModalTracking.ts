import { trackEvent, trackFeatureUsage } from './postHogService';

export interface ProModalTrackingData {
  userId?: string;
  userEmail?: string;
  timestamp: string;
}

export interface StepProgressionData extends ProModalTrackingData {
  fromStep: number;
  toStep: number;
  direction: 'forward' | 'backward';
  timeOnStepMs: number;
  timeOnStepSeconds: number;
  currentView: 'intro' | 'form';
}

export interface FormCompletionData extends ProModalTrackingData {
  formValue: string;
  formValueLength: number;
  totalTimeMs: number;
  totalTimeSeconds: number;
  stepsCompleted: number;
}

export interface ModalCloseData extends ProModalTrackingData {
  reason: 'user_close' | 'completion' | 'abandonment';
  timeSpentMs: number;
  timeSpentSeconds: number;
  currentView: 'intro' | 'form';
  currentStep: number;
  formValue: string;
  formValueLength: number;
}

export interface ViewChangeData extends ProModalTrackingData {
  fromView: 'intro' | 'form';
  toView: 'intro' | 'form';
  currentStep: number;
}

export interface FormInputData extends ProModalTrackingData {
  formValueLength: number;
  hasContent: boolean;
}

/**
 * ProModal Analytics Service
 * Provides structured tracking for the Pro Modal user journey
 */
export class ProModalAnalytics {
  /**
   * Track when the Pro Modal is opened
   */
  static trackModalOpened(data: ProModalTrackingData) {
    trackEvent('pro_modal_opened', {
      ...data,
      source: 'pro_modal'
    });
  }

  /**
   * Track when the Pro Modal is closed
   */
  static trackModalClosed(data: ModalCloseData) {
    trackEvent('pro_modal_closed', data);
  }

  /**
   * Track when user clicks "I'm Interested" button
   */
  static trackInterestedClicked(data: ProModalTrackingData) {
    trackEvent('pro_modal_interested_clicked', data);
  }

  /**
   * Track step progression (forward/backward navigation)
   */
  static trackStepProgression(data: StepProgressionData) {
    trackEvent('pro_modal_step_progression', data);
  }

  /**
   * Track view changes (intro <-> form)
   */
  static trackViewChanged(data: ViewChangeData) {
    trackEvent('pro_modal_view_changed', data);
  }

  /**
   * Track form input activity
   */
  static trackFormInput(data: FormInputData) {
    trackEvent('pro_modal_form_input', data);
  }

  /**
   * Track form completion
   */
  static trackFormCompleted(data: FormCompletionData) {
    trackEvent('pro_modal_form_completed', data);
    
    // Also track as feature usage for broader analytics
    trackFeatureUsage('pro_modal_completed', {
      userId: data.userId,
      formValueLength: data.formValueLength,
      totalTimeSeconds: data.totalTimeSeconds
    });
  }

  /**
   * Track abandonment at specific steps
   */
  static trackStepAbandonment(step: number, data: ProModalTrackingData & { 
    timeOnStepMs: number;
    formValue?: string;
  }) {
    trackEvent('pro_modal_step_abandoned', {
      ...data,
      abandonedAtStep: step,
      timeOnStepSeconds: Math.round(data.timeOnStepMs / 1000),
      formValueLength: data.formValue?.trim().length || 0
    });
  }

  /**
   * Track button clicks within the modal
   */
  static trackButtonClick(buttonType: 'next' | 'back' | 'close' | 'interested', data: ProModalTrackingData & {
    currentStep: number;
    currentView: 'intro' | 'form';
  }) {
    trackEvent('pro_modal_button_clicked', {
      ...data,
      buttonType
    });
  }
}

/**
 * Helper function to create base tracking data
 */
export const createTrackingData = (userId?: string, userEmail?: string): ProModalTrackingData => ({
  userId,
  userEmail,
  timestamp: new Date().toISOString()
});

/**
 * Analytics queries and insights for PostHog dashboard
 * These are the key metrics you should track in PostHog:
 * 
 * 1. CONVERSION FUNNEL:
 *    - pro_modal_opened → pro_modal_interested_clicked → pro_modal_form_completed
 * 
 * 2. STEP-BY-STEP COMPLETION RATES:
 *    - Count of users reaching each step (0, 1, 2, 3, 4)
 *    - Drop-off rates between steps
 * 
 * 3. TIME ANALYSIS:
 *    - Average time spent on each step
 *    - Total time to completion
 *    - Time spent by users who abandon vs complete
 * 
 * 4. FORM QUALITY METRICS:
 *    - Average form response length
 *    - Completion rate by response length
 *    - Most common abandonment points
 * 
 * 5. USER SEGMENTATION:
 *    - Completion rates by user type (new vs returning)
 *    - Geographic completion patterns
 *    - Device/browser completion rates
 * 
 * 6. ABANDONMENT ANALYSIS:
 *    - Most common abandonment step
 *    - Time before abandonment
 *    - Reasons for abandonment (close button vs navigation away)
 */

/**
 * PostHog Dashboard Queries:
 * 
 * 1. Overall Conversion Rate:
 *    Events: pro_modal_opened → pro_modal_form_completed
 * 
 * 2. Step Completion Funnel:
 *    Events: pro_modal_step_progression where direction = 'forward'
 *    Group by: toStep
 * 
 * 3. Average Time to Complete:
 *    Event: pro_modal_form_completed
 *    Metric: Average of totalTimeSeconds
 * 
 * 4. Abandonment Heatmap:
 *    Event: pro_modal_closed where reason = 'abandonment'
 *    Group by: currentStep
 * 
 * 5. Form Quality Distribution:
 *    Event: pro_modal_form_completed
 *    Metric: Distribution of formValueLength
 */
