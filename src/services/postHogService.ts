import posthog from "posthog-js";
import { store } from "@/store";
import { User } from "@supabase/supabase-js";
import surveyService from "./surveyService";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { getAppVersion } from "@/lib/utils/getAppVersion";

/**
 * Initialize PostHog
 * Note: The actual initialization is handled by the PostHogProvider in main.tsx
 */
export const initializePostHog = () => {
  // PostHog is already initialized via the PostHogProvider in main.tsx
  // This function is kept for API compatibility with the previous LogRocket service

  // Track page views
  trackPageView(window.location.pathname);

  // Listen for route changes
  setupRouteChangeTracking();
};

/**
 * Identify the current user for PostHog
 * @param user The user object from Supabase
 */
export const identifyPostHogUser = (user: User | null) => {
  if (user) {
    // Get additional user metadata
    const userMetadata = user.user_metadata || {};


    surveyService.createUser(user.id);
    surveyService.setUserEmail(user.email || '');

    // Identify the user
    posthog.identify(user.id, {
      name: userMetadata.name || user.email || 'Unknown User',
      email: user.email || '<EMAIL>',
      // Add user properties for better segmentation
      isNewUser: !!userMetadata.is_new_user,
      signUpDate: user.created_at || '',
      lastSignIn: user.last_sign_in_at || '',
      authProvider: userMetadata.provider || 'email',
      hasGithubConnected: !!userMetadata.github_connected,
      userRole: userMetadata.role || 'user',
      // Add referrer if available
      referrer: document.referrer || localStorage.getItem('user_referrer') || 'direct',
      // Add UTM parameters if stored
      utmSource: localStorage.getItem('utm_source') || '',
      utmMedium: localStorage.getItem('utm_medium') || '',
      utmCampaign: localStorage.getItem('utm_campaign') || ''
    });

    // Track user identification as an event
    trackEvent('user_identified', {
      userId: user.id,
      isNewUser: !!userMetadata.is_new_user,
      authProvider: userMetadata.provider || 'email'
    });
  } else {
    // Track user logout or session end
    trackEvent('user_session_ended');
  }
};

/**
 * Capture an error with context in PostHog
 * @param error The error object or message
 * @param context Additional context to include with the error
 */
export const captureError = (error: Error | string, context: Record<string, any> = {}) => {
  // Get the current state
  const state = store.getState();

  // Extract relevant information from the state
  const activeTabId = state.tabs.activeTab;
  const tabState = state.tabs.tabStates[activeTabId] || {};

  // Build the context with all relevant information
  const errorContext = {
    // User information
    userId: context.userId,

    // Job information
    jobId: tabState.jobId || context.jobId,
    containerId: tabState.containerId || context.containerId,

    // Tab information
    tabId: activeTabId,

    // Additional context provided
    ...context,

    // Error details
    errorMessage: error instanceof Error ? error.message : error,
    errorStack: error instanceof Error ? error.stack : undefined,
  };

  // Log to console for debugging
  console.error('Error captured by PostHog:', errorContext);

  // Send to PostHog
  posthog.capture('error', errorContext);
};

/**
 * Track a specific event in PostHog
 * @param eventName The name of the event
 * @param properties Additional properties to include with the event
 */
export const trackEvent = (eventName: string, properties: Record<string, any> = {}) => {
  // Get the current state
  const state = store.getState();

  // Extract relevant information from the state
  const activeTabId = state.tabs.activeTab;
  const tabState = state.tabs.tabStates[activeTabId] || {};

  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search);

  // Build the event properties with all relevant information
  const eventProperties: Record<string, string | number | boolean | null> = {
    // Job information
    jobId: tabState.jobId || '',
    containerId: tabState.containerId || '',

    // Tab information
    tabId: activeTabId || '',
    tabCount: Object.keys(state.tabs.tabStates || {}).length,

    // Page information
    currentPath: window.location.pathname,
    referrer: document.referrer,

    // Timestamp
    timestamp: new Date().toISOString(),
  };

  // Add URL parameters individually
  urlParams.forEach((value, key) => {
    eventProperties[`url_param_${key}`] = value;
  });

  // Add additional properties, ensuring they're the right type
  Object.entries(properties).forEach(([key, value]) => {
    if (value !== undefined) {
      if (typeof value === 'object' && value !== null) {
        eventProperties[key] = JSON.stringify(value);
      } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        eventProperties[key] = value;
      } else if (value === null) {
        eventProperties[key] = null;
      }
    }
  });

  // Send to PostHog
  posthog.capture(eventName, eventProperties);

  // Uncomment to log to console in development
  // console.log(`[PostHog Event] ${eventName}`, eventProperties);
};

/**
 * Track page views in PostHog
 * @param path The current path
 * @param params Optional URL parameters
 */
export const trackPageView = (path: string, params: Record<string, any> = {}) => {
  // Get referrer information
  const referrer = document.referrer;
  const isNewSession = !sessionStorage.getItem('session_started');

  if (isNewSession) {
    sessionStorage.setItem('session_started', 'true');
    // Store referrer for the session if it's external
    if (referrer && !referrer.includes(window.location.host)) {
      localStorage.setItem('user_referrer', referrer);
    }
  }

  // Always store UTM parameters if present in URL (regardless of session state)
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.has('utm_source')) localStorage.setItem('utm_source', urlParams.get('utm_source') || '');
  if (urlParams.has('utm_medium')) localStorage.setItem('utm_medium', urlParams.get('utm_medium') || '');
  if (urlParams.has('utm_campaign')) localStorage.setItem('utm_campaign', urlParams.get('utm_campaign') || '');
  if (urlParams.has('utm_content')) localStorage.setItem('utm_content', urlParams.get('utm_content') || '');
  if (urlParams.has('utm_term')) localStorage.setItem('utm_term', urlParams.get('utm_term') || '');

  // Track the page view
  trackEvent('page_view', {
    path,
    referrer,
    isNewSession,
    ...params
  });
};

/**
 * Setup tracking for route changes
 */
const setupRouteChangeTracking = () => {
  // Use history API to track route changes
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function(state, title, url) {
    originalPushState.apply(this, [state, title, url]);
    if (url) {
      trackPageView(typeof url === 'string' ? url : window.location.pathname);
    }
  };

  history.replaceState = function(state, title, url) {
    originalReplaceState.apply(this, [state, title, url]);
    if (url) {
      trackPageView(typeof url === 'string' ? url : window.location.pathname);
    }
  };

  // Track on popstate (back/forward navigation)
  window.addEventListener('popstate', () => {
    trackPageView(window.location.pathname);
  });
};

/**
 * Track authentication events
 */
export const trackAuthEvent = (eventType: 'login_attempt' | 'login_success' | 'login_failure' | 'signup_attempt' | 'signup_success' | 'signup_failure' | 'logout', details: Record<string, any> = {}) => {
  trackEvent(`auth_${eventType}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Track chat interactions
 */
export const trackChatInteraction = (action: 'message_sent' | 'message_received' | 'feature_used', details: Record<string, any> = {}) => {
  trackEvent(`chat_${action}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Track navigation events
 * @param from The source path
 * @param to The destination path
 */
export const trackNavigation = (from: string, to: string, options?: any) => {
  trackEvent('navigation', {
    from,
    to,
    navigationOptions: options ? JSON.stringify(options) : undefined
  });
};

/**
 * Track tab interactions
 */
export const trackTabInteraction = (action: 'tab_opened' | 'tab_closed' | 'tab_switched', details: Record<string, any> = {}) => {
  trackEvent(`tab_${action}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Track feature usage
 */
export const trackFeatureUsage = (featureName: string, details: Record<string, any> = {}) => {
  trackEvent(`feature_${featureName}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Track task creation events
 */
export const trackTaskCreation = (action: 'started' | 'submitted' | 'completed' | 'abandoned', details: Record<string, any> = {}) => {
  trackEvent(`task_${action}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Track user onboarding events
 */
export const trackUserOnboarding = (action: 'first_login' | 'first_task_created' | 'first_message_sent' | 'profile_completed', details: Record<string, any> = {}) => {
  trackEvent(`onboarding_${action}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};

/**
 * Track user referral source
 */
export const trackUserSource = (details: Record<string, any> = {}) => {
  // Get referrer information
  const referrer = document.referrer;
  const isNewSession = !sessionStorage.getItem('session_started');
  const urlParams = new URLSearchParams(window.location.search);

  // Build source tracking data
  const sourceData = {
    referrer,
    isNewSession,
    landingPage: window.location.pathname,
    utmSource: urlParams.get('utm_source') || localStorage.getItem('utm_source') || '',
    utmMedium: urlParams.get('utm_medium') || localStorage.getItem('utm_medium') || '',
    utmCampaign: urlParams.get('utm_campaign') || localStorage.getItem('utm_campaign') || '',
    utmContent: urlParams.get('utm_content') || localStorage.getItem('utm_content') || '',
    utmTerm: urlParams.get('utm_term') || localStorage.getItem('utm_term') || '',
    ...details
  };

  trackEvent("user_source", sourceData);
};

export const fetchConfigVersion = async () => {
  try {
    const variant = posthog.getFeatureFlag("config_version")?.toString();
    return variant?.replace(/_/g, ".");
  } catch {
    // Fallback to app version when PostHog fails
    try {
      return await getAppVersion();
    } catch (error) {
      console.error('Error getting app version fallback:', error);
      return undefined;
    }
  }
};

export const isExperimentalEnabled = (): boolean => {
  try {
    const experimentalFlag = posthog.getFeatureFlag("experimental_agent_features");
    return experimentalFlag === true || experimentalFlag === "true";
  } catch (error) {
    console.warn("Failed to get experimental feature flag:", error);
    return false;
  }
};

export const isForkEnabled = () : boolean=>{
  const isEmergentUser = useIsEmergentUser();
  try {
    const forkFlag = posthog.getFeatureFlag("feature_forking");
    return forkFlag === true || forkFlag === "true" || isEmergentUser;
  } catch (error) {
    console.warn("Failed to get forking feature flag:", error);
    return false;
  }
}

export const isReferralEnabled = () : boolean=>{
  const isEmergentUser = useIsEmergentUser();
  try {
    const referralFlag = posthog.getFeatureFlag("feature_referral");
    return referralFlag === true || referralFlag === "true" || isEmergentUser;
  } catch (error) {
    console.warn("Failed to get referral feature flag:", error);
    return false;
  }
}

export const useIsUploadAssetEnabled = () : boolean=>{
  const isEmergentUser = useIsEmergentUser();
  try {
    const uploadAssetFlag = posthog.getFeatureFlag("feature_upload_assets");
    return uploadAssetFlag === true || uploadAssetFlag === "true" || isEmergentUser;
  } catch (error) {
    console.warn("Failed to get upload assets feature flag:", error);
    return false;
  }
}

export const isInputDisabled = () : boolean=>{
  try {
    const isInputDisabled = posthog.getFeatureFlag("disable_inputs");
    return (isInputDisabled === true || isInputDisabled === "true") && localStorage.getItem("mobile") === "true";
  } catch (error) {
    console.warn("Failed to get forking feature flag:", error);
    return false;
  }
}


export const trackExperimentalFeature = (enabled: boolean, context: Record<string, any> = {}) => {
  trackEvent('experimental_feature_toggled', {
    enabled,
    source: 'posthog_feature_flag',
    ...context
  });
};



export const isAutoBudgetUpdateEnabled = () : boolean=>{
  try {
    const autoBudgetUpdateFlag = posthog.getFeatureFlag("auto_budget_update");
    return autoBudgetUpdateFlag === true || autoBudgetUpdateFlag === "true";
  } catch (error) {
    console.warn("Failed to get auto budget update feature flag:", error);
    return false;
  }
}

export const isMobilePreviewEnabled = () : boolean=>{
  try {
    const mobilePreviewEnabled = posthog.getFeatureFlag("mobile_preview_enabled");
    return mobilePreviewEnabled === true || mobilePreviewEnabled === "true";
  } catch (error) {
    console.warn("Failed to get auto budget update feature flag:", error);
    return false;
  }
}

export const isE1Enabled = () : boolean=>{
  try {
    const e1enabled = posthog.getFeatureFlag("e1_enabled");
    return e1enabled === true;
  } catch (error) {
    console.warn("Failed to get e1new feature flag:", error);
    return false;
  }
}

export const isCustomAgentsEnabled = () : boolean=>{
  const isEmergentUser = useIsEmergentUser();
  try {
    const customAgentsFlag = posthog.getFeatureFlag("feature_custom_agents");
    return customAgentsFlag === true || customAgentsFlag === "true" || isEmergentUser;
  } catch (error) {
    console.warn("Failed to get custom agents feature flag:", error);
    return false;
  }
}


/**
 * Track referral-related events
 */
export const trackReferralEvent = (action: 'get_free_credits_clicked' | 'modal_opened' | 'copy_link_clicked' | 'modal_closed', details: Record<string, any> = {}) => {
  trackEvent(`referral_${action}`, {
    ...details,
    timestamp: new Date().toISOString()
  });
};