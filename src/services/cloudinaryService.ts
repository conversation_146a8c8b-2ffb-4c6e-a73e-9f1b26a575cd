// @ts-nocheck
import { Cloudinary } from '@cloudinary/url-gen';
import { auto } from '@cloudinary/url-gen/actions/resize';
import { autoGravity } from '@cloudinary/url-gen/qualifiers/gravity';
import { format, quality, dpr } from '@cloudinary/url-gen/actions/delivery';
// import { progressive } from '@cloudinary/url-gen/actions/delivery';

/**
 * Cloudinary Service
 * 
 * Centralized service for handling Cloudinary image transformations and optimizations.
 * This service provides a clean interface for creating optimized images with consistent
 * settings across the application.
 */

// Initialize Cloudinary instance
const cld = new Cloudinary({
  cloud: {
    cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME
  }
});

/**
 * Creates an optimized Cloudinary image with automatic transformations
 * 
 * @param publicId - The public ID of the image in Cloudinary
 * @param maxWidth - Maximum width for the image (default: 800)
 * @returns Cloudinary image instance with optimizations applied
 * 
 * Features:
 * - Auto-resize with gravity detection
 * - Auto format selection (WebP, AVIF, etc.)
 * - Network-aware quality optimization
 * - Device pixel ratio optimization
 * - Progressive loading
 */
export const createOptimizedImage = (publicId: string, maxWidth: number = 800) : any => {
  return cld
    .image(publicId)
    .resize(auto().gravity(autoGravity()).width(`auto:100:${maxWidth}`))
    .delivery(format('auto'))           // Auto format (WebP, AVIF, etc.)
    .delivery(quality('auto'))          // Network-aware quality
    .delivery(dpr('auto'))             // Device pixel ratio
    // .delivery(progressive());           // Progressive loading
};

/**
 * Creates a basic Cloudinary image without optimizations
 * Useful when you need more control over transformations
 * 
 * @param publicId - The public ID of the image in Cloudinary
 * @returns Basic Cloudinary image instance
 */
export const createImage = (publicId: string) => {
  return cld.image(publicId);
};

/**
 * Creates an optimized image with custom dimensions
 * 
 * @param publicId - The public ID of the image in Cloudinary
 * @param width - Specific width for the image
 * @param height - Specific height for the image (optional)
 * @returns Cloudinary image instance with custom dimensions
 */
export const createCustomSizedImage = (publicId: string, width: number, height?: number) => {
  const image = cld.image(publicId);
  
  if (height) {
    image.resize(auto().gravity(autoGravity()).width(width).height(height));
  } else {
    image.resize(auto().gravity(autoGravity()).width(width));
  }
  
  return image
    .delivery(format('auto'))
    .delivery(quality('auto'))
    .delivery(dpr('auto'))
    .delivery(progressive());
};

/**
 * Creates a thumbnail version of an image
 * 
 * @param publicId - The public ID of the image in Cloudinary
 * @param size - Size for the thumbnail (default: 150)
 * @returns Cloudinary image instance optimized for thumbnails
 */
export const createThumbnail = (publicId: string, size: number = 150) => {
  return cld
    .image(publicId)
    .resize(auto().gravity(autoGravity()).width(size).height(size))
    .delivery(format('auto'))
    .delivery(quality('auto'))
    .delivery(dpr('auto'));
};

/**
 * Validates that Cloudinary environment variables are properly configured
 * 
 * @throws Error if VITE_CLOUDINARY_CLOUD_NAME is not set
 */
export const validateCloudinaryConfig = () => {
  if (!import.meta.env.VITE_CLOUDINARY_CLOUD_NAME) {
    throw new Error('Missing required environment variable: VITE_CLOUDINARY_CLOUD_NAME');
  }
};

/**
 * Gets the Cloudinary cloud name from environment variables
 * 
 * @returns The configured cloud name
 */
export const getCloudName = () => {
  return import.meta.env.VITE_CLOUDINARY_CLOUD_NAME;
};

/**
 * Export the Cloudinary instance for advanced usage
 * Use this when you need direct access to the Cloudinary SDK
 */
export { cld };

// Validate configuration on module load
validateCloudinaryConfig();
