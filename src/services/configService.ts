import { agent<PERSON>pi } from "./agent<PERSON>pi";
import { getAppVersion } from "@/lib/utils/getAppVersion";
import { fetchConfigVersion } from "./postHogService";

export interface GlobalConfig {
  // Add your config interface properties here based on the API response
  app_version: string;
  portfolio?: any[]; // Adding portfolio property to fix TypeScript error
  overload_lock: {
    enabled: boolean;
    capacity: string;
    concurrent_tasks: string;
    locked_until: string;
    title: string;
    description: string;
  };
  lite_version: {
    enabled: boolean;
    code: string;
  }
}

export const fetchGlobalConfig = async (): Promise<GlobalConfig> => {
  const payload = await fetchConfigVersion();
  const version = payload || (await getAppVersion());
  return agentApi.getConfig(version);
};