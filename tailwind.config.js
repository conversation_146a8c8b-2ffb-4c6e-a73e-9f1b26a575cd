const { fontFamily } = require("tailwindcss/defaultTheme")

/** @type {import("tailwindcss").Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ["Brockmann", ...fontFamily.sans],
        berkeley: ["Berkeley Mono Trial", "monospace"],
        inter: ["Inter", "sans-serif"],
        jetbrains: ["JetBrains Mono", "monospace"],
        nothing: ["Nothing", "monospace"],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        loader: {
          DEFAULT: '#61D7FB',
          muted: 'rgba(97, 215, 251, 0.2)',
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
          blue: "hsl(var(--card-blue-background))",
          blueGray: "hsl(var(--card-blue-gray-background))"
        },
        yellow: {
          DEFAULT: "hsl(var(--yellow-primary))",
          text: "hsl(var(--yellow-primary-text))",
        },
        task: {
          done: '#DDDDE6',
          running: '#2EBBE5',
          pending: '#DDDDE6',
        },
        step: {
          inactive: "#5C5F66",
          title: "#ACACB2",
          completed: "#939399",
          success: "#29CC83"
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "collapsible-down": {
          from: { height: 0 },
          to: { height: "var(--radix-collapsible-content-height)" },
        },
        "collapsible-up": {
          from: { height: "var(--radix-collapsible-content-height)" },
          to: { height: 0 },
        },
        "glow-border": {
          "0%": {
            transform: "rotate(0deg)",
          },
          "100%": {
            transform: "rotate(360deg)",
          }
        },
		  "shiny-text": {
          "0%, 90%, 100%": {
            "background-position": "calc(-100% - var(--shiny-width)) 0",
          },
          "30%, 60%": {
            "background-position": "calc(100% + var(--shiny-width)) 0",
          },
        },
      text: {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center',
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center',
          },
        },
      "border-beam": {
          "100%": {
            "offset-distance": "100%",
          },
        },
      "status-pulse": {
          "0%": {
            boxShadow: "0 0 0 0 var(--status-pulse-color, rgba(46, 187, 229, 0.7))",
          },
          "70%": {
            boxShadow: "0 0 0 6px var(--status-pulse-transparent, rgba(46, 187, 229, 0))",
          },
          "100%": {
            boxShadow: "0 0 0 0 var(--status-pulse-transparent, rgba(46, 187, 229, 0))",
          }
        },
      "status-ping": {
          "0%": {
            transform: "scale(0.8)",
            opacity: 0.8,
          },
          "70%, 100%": {
            transform: "scale(1.5)",
            opacity: 0,
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "border-beam": "border-beam calc(var(--duration)*1s) infinite linear",
        "accordion-up": "accordion-up 0.2s ease-out",
        "collapsible-down": "collapsible-down 0.2s ease-out",
        "collapsible-up": "collapsible-up 0.2s ease-out",
        "glow-border": "glow-border 3s linear infinite",
		    "shiny-text": "shiny-text 8s infinite",
        "text": 'text 5s ease infinite',
        "status-pulse": "status-pulse 2s ease-out infinite",
        "status-ping": "status-ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
